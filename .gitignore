# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# documentation
/documentation
/docs

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE files
.cursor/
.codeiumignore
.cursorignore

# Large binary files
*.png

#apps folder - under development
#/apps

#marketing-site folder - under development
#/marketing-site
/marketing-site
/apps
/marketing-site/node_modules
/marketing-site/.next

#www
/www
/www/node_modules
/www/.next

#apps
/apps/node_modules
/apps/.next


#provibe-io folder - under development
/provibe-io
/provibe-io/www
/provibe-io/apps

/provibe-io/node_modules
/provibe-io/.next

/provibe-io/www/node_modules
/provibe-io/www/.next

/provibe-io/apps/node_modules
/provibe-io/apps/.next

#chat-sdk-sandbox folder - under development
/chat-sdk-sandbox/node_modules
/chat-sdk-sandbox/.next
/chat-sdk-sandbox
