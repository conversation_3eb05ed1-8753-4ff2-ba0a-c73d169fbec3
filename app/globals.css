/* Font imports */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* ─── LIGHT MODE ───────────────────────── */
    --background:          60, 100%, 100%; /* equivalent to #FFFFB3 */
    --foreground:          230 15%  15%;   /* deep slate text */

    --card:                 0   0% 100%;
    --card-foreground:     var(--foreground);

    --popover:              0   0% 100%;
    --popover-foreground:  var(--foreground);

    /* Ultraviolet core */
    --primary:             235 85%  60%;   /* mid‑point of the gradient */
    --primary-foreground:    0   0% 100%;

    --secondary:           220 12%  94%;   /* subtle gray section bg */
    --secondary-foreground:230 15%  25%;

    --muted:               220 12%  95%;
    --muted-foreground:    230 10%  45%;

    --accent:              240 100% 97%;   /* whisper‑violet surface */
    --accent-foreground:   245 45%  35%;

    --destructive:           0 70%  55%;
    --destructive-foreground:0  0% 100%;

    --border:              220 12%  88%;
    --input:               220 12%  92%;
    --ring:                var(--primary);

    /* Hero / progress gradient */
    --gradient-start:      253 83%  65%;   /* #7B5CF0 */
    --gradient-end:        215 92%  58%;   /* #3182F6 */

    --radius: 0.5rem;

    /* Light theme variables */
    --code-bg-light: #f5f7fa;
    --code-color-light: #24292e;
    --sidebar-background:          60, 100%, 100%; /* equivalent to #FFFFB3 */
    --sidebar-foreground:          240 5.3% 26.1%;
    --sidebar-primary:          240 5.9% 10%;
    --sidebar-primary-foreground:          0 0% 98%;
    --sidebar-accent:          240 4.8% 95.9%;
    --sidebar-accent-foreground:          240 5.9% 10%;
    --sidebar-border:          220 13% 91%;
    --sidebar-ring:          217.2 91.2% 59.8%;
  }

  .dark {
    /* ─── DARK MODE ───────────────────────── */
    --background:          234 37%  10%;   /* #101223 charcoal‑ink */
    --foreground:            0  0%  95%;

    --card:                234 30%  14%;
    --card-foreground:     var(--foreground);

    --popover:             234 30%  12%;
    --popover-foreground:  var(--foreground);

    --primary:             235 85%  65%;   /* brighter ultraviolet */
    --primary-foreground:  234 37%  12%;

    --secondary:           232 20%  18%;
    --secondary-foreground:  0  0%  90%;

    --muted:               232 18%  15%;
    --muted-foreground:    232 12%  65%;

    --accent:              235 30%  22%;
    --accent-foreground:   235 70%  85%;

    --destructive:           0 65%  52%;
    --destructive-foreground: 0  0%  96%;

    --border:              232 20%  24%;
    --input:               232 20%  22%;
    --ring:                var(--primary);

    --gradient-start:      235 85%  65%;
    --gradient-end:        215 90%  60%;

    --radius: 0.5rem;

    /* Dark theme variables */
    --code-bg: #1e1e1e;
    --code-color: #d4d4d4;
    --sidebar-background:          240 5.9% 10%;
    --sidebar-foreground:          240 4.8% 95.9%;
    --sidebar-primary:          224.3 76.3% 48%;
    --sidebar-primary-foreground:          0 0% 100%;
    --sidebar-accent:          240 3.7% 15.9%;
    --sidebar-accent-foreground:          240 4.8% 95.9%;
    --sidebar-border:          240 3.7% 15.9%;
    --sidebar-ring:          217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1 { @apply text-4xl font-bold mb-6; }
  h2 { @apply text-3xl font-semibold mb-5; }
  h3 { @apply text-2xl font-semibold mb-4; }
  h4 { @apply text-xl font-medium mb-3; }
  h5 { @apply text-lg font-medium mb-2; }
  h6 { @apply text-base font-medium mb-2; }
  p  { @apply text-base mb-4; }
}

/* Properly defined Utility classes */
.bg-primary { background-color: hsl(var(--primary)); }
.bg-secondary { background-color: hsl(var(--secondary)); }
.bg-accent { background-color: hsl(var(--accent)); }

.text-primary { color: hsl(var(--primary)); }
.text-secondary { color: hsl(var(--secondary-foreground)); }
.text-accent { color: hsl(var(--accent-foreground)); }

.gradient-text {
  background: linear-gradient(to right, hsl(var(--gradient-start)), hsl(var(--gradient-end)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-text-high-contrast {
  background: linear-gradient(to right, hsl(var(--primary)), hsl(215, 70%, 50%));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

.gradient-bg {
  background: linear-gradient(to bottom right, hsl(var(--gradient-start)), hsl(var(--gradient-end)));
}

/* Animations */
@keyframes float {
  0% { transform: translate(0, 0); }
  50% { transform: translate(-10px, -10px); }
  100% { transform: translate(0, 0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fade-in-up {
  animation: fadeInUp 0.5s ease-out forwards;
}

.animation-delay-150 { animation-delay: 150ms; }
.animation-delay-300 { animation-delay: 300ms; }
.animation-delay-450 { animation-delay: 450ms; }

.document-carousel-container {
  mask-image: linear-gradient(to right, transparent, black 10%, black 90%, transparent);
}

.document-carousel-track {
  animation: document-scroll 30s linear infinite;
  width: max-content;
}

@keyframes document-scroll {
  from { transform: translateX(0); }
  to { transform: translateX(-50%); }
}

.logo-carousel {
  overflow: hidden;
  position: relative;
}

.logo-track {
  display: flex;
  animation: logoScroll 20s linear infinite;
}

@keyframes logoScroll {
  from { transform: translateX(0); }
  to { transform: translateX(-100%); }
}

/* Grid Patterns */
.bg-grid-pattern {
  background-size: 40px 40px;
  background-image: linear-gradient(to right, rgba(255,255,255,0.05) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
}

.bg-binary-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/svg%3E");
}

/* Marquee animation */
@keyframes scroll {
  from { transform: translateX(0); }
  to { transform: translateX(calc(-50% - 0.5rem)); }
}

.animate-scroll {
  animation: scroll 40s linear infinite;
}

/* Rich text content styling */
.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3,
.rich-text-content h4,
.rich-text-content h5,
.rich-text-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.75em;
}

.rich-text-content h1:first-child,
.rich-text-content h2:first-child,
.rich-text-content h3:first-child,
.rich-text-content h4:first-child,
.rich-text-content h5:first-child,
.rich-text-content h6:first-child {
  margin-top: 0;
}

.rich-text-content p {
  margin-bottom: 1em;
}

.rich-text-content ul,
.rich-text-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.rich-text-content li + li {
  margin-top: 0.25em;
}

.rich-text-content code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: var(--font-mono);
  font-size: 0.9em;
}

/* Mermaid diagram styling */
.mermaid-diagram {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0;
  overflow-x: auto;
  background-color: white;
  padding: 1rem;
  border-radius: 0.5rem;
}

.dark .mermaid-diagram {
  background-color: #1e1e2e;
}

.mermaid-diagram svg {
  max-width: 100%;
}

/* Dark mode support for diagrams */
.dark .mermaid-diagram .label {
  color: #f8f8f2;
}

.dark .mermaid-diagram .node rect,
.dark .mermaid-diagram .node circle,
.dark .mermaid-diagram .node ellipse,
.dark .mermaid-diagram .node polygon,
.dark .mermaid-diagram .node path {
  fill: #44475a;
  stroke: #bd93f9;
}

.dark .mermaid-diagram .edgePath .path {
  stroke: #f8f8f2;
}

/* Document-style markdown renderer CSS */
.markdown-content {
  max-width: 56rem; /* 896px - matches editor max-width */
  margin: 0 auto;
  padding: 3rem 4rem; /* 48px 64px - matches editor padding */
  background-color: white;
  min-height: 100vh;
  font-family: inter;
  box-sizing: border-box;
}

.markdown-content h1 {
  color: #111827; /* text-gray-900 */
  font-size: 2.25rem; /* text-4xl */
  font-weight: 700;
  margin-top: 2rem; /* mt-8 */
  margin-bottom: 1.5rem; /* mb-6 */
  line-height: 1.25; /* leading-tight */
}

.markdown-content h2 {
  color: #111827; /* text-gray-900 */
  font-size: 1.875rem; /* text-3xl */
  font-weight: 700;
  margin-top: 1.5rem; /* mt-6 */
  margin-bottom: 1rem; /* mb-4 */
  line-height: 1.25; /* leading-tight */
}

.markdown-content h3 {
  color: #111827; /* text-gray-900 */
  font-size: 1.5rem; /* text-2xl */
  font-weight: 700;
  margin-top: 1.25rem; /* mt-5 */
  margin-bottom: 0.75rem; /* mb-3 */
  line-height: 1.25; /* leading-tight */
}

.markdown-content h4 {
  color: #111827; /* text-gray-900 */
  font-size: 1.25rem; /* text-xl */
  font-weight: 700;
  margin-top: 1rem; /* mt-4 */
  margin-bottom: 0.75rem; /* mb-3 */
  line-height: 1.25; /* leading-tight */
}

.markdown-content h5 {
  color: #111827; /* text-gray-900 */
  font-size: 1.125rem; /* text-lg */
  font-weight: 700;
  margin-top: 0.75rem; /* mt-3 */
  margin-bottom: 0.5rem; /* mb-2 */
  line-height: 1.25; /* leading-tight */
}

.markdown-content h6 {
  color: #111827; /* text-gray-900 */
  font-size: 1rem; /* text-base */
  font-weight: 700;
  margin-top: 0.75rem; /* mt-3 */
  margin-bottom: 0.5rem; /* mb-2 */
  line-height: 1.25; /* leading-tight */
}

.markdown-content p {
  font-size: 1rem; /* text-base */
  line-height: 1.75; /* leading-7 */
  margin-bottom: 1rem; /* mb-4 */
  color: #1f2937; /* text-gray-800 */
  font-family: inter;
}

.markdown-content ul, .markdown-content ol {
  margin: 1rem 0; /* my-4 */
  padding-left: 1.5rem; /* pl-6 */
  font-size: 1rem; /* text-base */
  line-height: 1.75; /* leading-7 */
  color: #1f2937; /* text-gray-800 */
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin-bottom: 0.5rem; /* mb-2 */
  padding-left: 0.5rem; /* pl-2 */
}

.markdown-content blockquote {
  margin: 1.5rem 0; /* my-6 */
  border-left: 4px solid #d1d5db; /* border-l-4 border-gray-300 */
  padding-left: 1.5rem; /* pl-6 */
  font-style: italic;
  color: #374151; /* text-gray-700 */
  background-color: #f9fafb; /* bg-gray-50 */
  padding-top: 1rem; /* py-4 */
  padding-bottom: 1rem;
}

.markdown-content pre {
  background-color: #f3f4f6; /* bg-gray-100 */
  border-radius: 0.375rem; /* rounded-md */
  padding: 1rem;
  overflow-x: auto;
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb; /* border */
}

.markdown-content code {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Inconsolata, "Liberation Mono", "Courier New", monospace;
  font-size: 0.875rem; /* text-sm */
  padding: 0.125rem 0.375rem; /* px-1.5 py-0.5 */
  border-radius: 0.25rem; /* rounded */
  background-color: #f3f4f6; /* bg-gray-100 */
  color: #1f2937; /* text-gray-800 */
  border: 1px solid #e5e7eb; /* border */
}

.markdown-content pre code {
  padding: 0;
  background-color: transparent;
  border: none;
}

.markdown-content a {
  color: #2563eb; /* text-blue-600 */
  text-decoration: underline;
  text-underline-offset: 2px;
}

.markdown-content a:hover {
  color: #1e40af; /* text-blue-800 */
}

.markdown-content img {
  max-width: 100%;
  border-radius: 0.375rem; /* rounded-md */
  margin: 1rem 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.markdown-content table {
  width: 100%;
  margin: 1.5rem 0; /* my-6 */
  border-collapse: collapse;
  border: 1px solid #d1d5db; /* border-gray-300 */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-sm */
}

.markdown-content th, .markdown-content td {
  border: 1px solid #d1d5db; /* border-gray-300 */
  padding: 0.75rem 1rem; /* px-4 py-3 */
  text-align: left;
  font-size: 1rem; /* text-base */
  background-color: white;
}

.markdown-content th {
  background-color: #f9fafb; /* bg-gray-50 */
  font-weight: 700;
  color: #111827; /* text-gray-900 */
}

.markdown-content hr {
  margin: 2rem 0; /* my-8 */
  border: none;
  height: 1px;
  background-color: #d1d5db; /* bg-gray-300 */
}

/* Strong and emphasis styling to match editor */
.markdown-content strong {
  font-weight: 700;
  color: #111827; /* text-gray-900 */
}

.markdown-content em {
  font-style: italic;
}

.markdown-content del {
  text-decoration: line-through;
  color: #6b7280; /* text-gray-600 */
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .markdown-content {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .markdown-content h1, .markdown-content h2, .markdown-content h3, 
  .markdown-content h4, .markdown-content h5, .markdown-content h6 {
    color: #f9fafb;
  }
  
  .markdown-content p {
    color: #e5e7eb;
  }
  
  .markdown-content ul, .markdown-content ol {
    color: #e5e7eb;
  }
  
  .markdown-content blockquote {
    background-color: #374151;
    color: #d1d5db;
    border-left-color: #6b7280;
  }
  
  .markdown-content pre {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .markdown-content code {
    background-color: #374151;
    color: #e5e7eb;
    border-color: #4b5563;
  }
  
  .markdown-content table {
    border-color: #4b5563;
  }
  
  .markdown-content th {
    background-color: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }
  
  .markdown-content td {
    background-color: #1f2937;
    border-color: #4b5563;
  }
  
  .markdown-content hr {
    background-color: #4b5563;
  }
  
  .markdown-content strong {
    color: #f9fafb;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
