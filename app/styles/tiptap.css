.tiptap p {
    padding-left: 1.5em;
    margin-top: 0.5em;
    margin-bottom: 0.75em;
    line-height: 1.0;
  }
  
  .tiptap h1, .tiptap h2, .tiptap h3 {
    margin-top: 2.5em;
    margin-bottom: 1em;
    font-weight: bold;
  }
  
  .tiptap ul,
  .tiptap ol {
    padding-left: 2.5em;
    margin-bottom: 0.25em;
  }
  
  .tiptap li {
    margin-bottom: 0.5em;
    line-height: 1.0;
  }
  
  .tiptap blockquote {
    border-left: 4px solid #ccc;
    margin: 1em 0;
    padding-left: 1em;
    color: #666;
    font-style: italic;
  }
  
  .tiptap pre {
    background: #f6f8fa;
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 0.9em;
  }

  /* In styles/tiptap.css */
.slash-command-menu {
    position: absolute;
    background: white;
    border: 1px solid #ccc;
    border-radius: 0.375rem;
    box-shadow: 0 4px 14px rgba(0,0,0,0.1);
    font-size: 0.875rem;
    padding: 0.5rem;
    min-width: 200px;
    z-index: 50;
  }
  
  .slash-command-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  
  .slash-command-menu li {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    border-radius: 0.375rem;
  }
  
  .slash-command-menu li:hover {
    background-color: #f3f4f6;
  }

  .slash-command-menu li.active {
    background-color: #e5e7eb; /* Tailwind's gray-200 */
  }