"use client"

// Import necessary hooks, components, and libraries
import { useAuth } from "@/components/auth/auth-provider" // Assuming this provides user context
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge" // Badge component is imported but not used in the provided code
import {
  FolderKanban,
  Clock,
  Plus,
  ArrowUpRight,
  Lightbulb,
  FileCode,
  BookOpen,
  CheckCircle2,
  Sparkles,
  Zap,
  Code2,
  Database,
  Globe,
  Smartphone,
  CreditCard,
  Pencil,
  Trash2,
  FileText,
  PlusCircle,
  ChevronUp,
  ArrowUp,
  Mic,
  WandSparkles,
  FileImage,
  SendHorizonal,
  Shuffle,
  Paperclip,
  Loader2, // Import Loader2 for loading state
} from "lucide-react" // Icons library
import Link from "next/link" // Next.js link component for navigation
import { ArrowRight } from "lucide-react" // Specific icon import
import { useEffect, useState, useRef } from "react" // React hooks for state and side effects
import { supabase } from "@/lib/supabase-client" // Supabase client library
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useProjectStore } from '@/lib/store/project'; // *** ADDED: Import the project store ***

// --- Mock Data (Placeholder) ---
// Define mock projects for testing or preview environments
const MOCK_PROJECTS = [
  { id: 'proj_1', name: 'Mock Project Alpha', idea: 'This is the idea for mock project Alpha, used for testing purposes.', updated_at: new Date().toISOString() },
  { id: 'proj_2', name: 'Mock Project Beta', idea: 'Another mock project description for Beta, demonstrating the UI.', updated_at: new Date().toISOString() },
  { id: 'proj_3', name: 'Mock Project Gamma', idea: 'A third mock project, Gamma, showing how the list renders.', updated_at: new Date().toISOString() },
];

// --- Environment Check ---
// Check if the app is running in the Vercel preview environment (v0)
const isV0Preview = typeof window !== "undefined" && window.location.hostname.includes("vusercontent.net")

// --- Dashboard Page Component ---
export default function DashboardPage() {
  // --- State Variables ---
  const { user } = useAuth() // Get user data from authentication context
  const [recentProjects, setRecentProjects] = useState<any[]>([]) // State for recent projects
  const [projectCount, setProjectCount] = useState(0) // State for total project count
  const [documentsCount, setDocumentsCount] = useState(0) // State for total documents count
  const [loading, setLoading] = useState(true) // State to manage loading status for projects
  const [error, setError] = useState<string | null>(null); // State for storing fetch errors
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [ideaText, setIdeaText] = useState("")
  const [hasTyped, setHasTyped] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false)
  const [enhancedIdea, setEnhancedIdea] = useState<string | null>(null)
  const [suggestedProjectName, setSuggestedProjectName] = useState<string | null>(null);
  const router = useRouter()
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isTextareaFocused, setIsTextareaFocused] = useState(false); // For textarea placeholder
  const [isCreatingProject, setIsCreatingProject] = useState(false); // *** ADDED: State for new project creation loading ***

  // *** ADDED: Get createNewProject action from the store ***
  const createNewProject = useProjectStore((state) => state.createNewProject);

  // Check if idea is long enough
  const isIdeaLongEnough = ideaText.length >= 10

  // Handle text change in textarea
  const handleIdeaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setHasTyped(true);
    setIdeaText(e.target.value);
    setEnhancedIdea(null);
    setSuggestedProjectName(null);
    localStorage.removeItem("enhancedIdea");
    localStorage.removeItem("originalIdea");
    localStorage.removeItem("suggestedProjectName");
  }

  // Handle enhance idea with WandSparkles
  const handleEnhanceIdea = async () => {
    console.log("handleEnhanceIdea called, ideaText:", ideaText);
    if (!isIdeaLongEnough) {
      console.log("Idea not long enough, returning");
      return;
    }

    try {
      console.log("Setting isEnhancing to true");
      setIsEnhancing(true);
      console.log("Fetching from /api/enhance-idea");
      const resp = await fetch("/api/enhance-idea", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-user-id": user?.id || ""
        },
        body: JSON.stringify({
          idea: ideaText,
          source: uploadedFile ? "file" : "text" // Add source information
        }),
      });

      console.log("Response status:", resp.status);
      if (!resp.ok) {
        throw new Error(`Failed to enhance idea: ${resp.status}`);
      }

      const data = await resp.json();
      console.log("Received enhanced idea:", data);
      setEnhancedIdea(data.enhancedIdea);
      setSuggestedProjectName(data.productName || null);

      // Update the text input with the enhanced idea
      setIdeaText(data.enhancedIdea);

      // Store in localStorage
      localStorage.setItem("enhancedIdea", data.enhancedIdea);
      localStorage.setItem("originalIdea", ideaText);
      if (data.productName) {
        localStorage.setItem("suggestedProjectName", data.productName);
      } else {
        localStorage.removeItem("suggestedProjectName");
      }

      toast({
        title: "Idea enhanced",
        description: "Your idea has been refined and saved locally",
      });
    } catch (err) {
      console.error("Error enhancing idea:", err);
      toast({
        title: "Enhancement failed",
        description: "Could not enhance your idea. Please try again.",
        variant: "destructive",
      });
    } finally {
      console.log("Setting isEnhancing to false");
      setIsEnhancing(false);
    }
  };

  // Handle create project with ArrowUp (from idea input)
  const handleCreateProjectFromIdea = async () => {
    if (!isIdeaLongEnough) return
    if (!user) return;

    setIsCreatingProject(true); // Use the same loading state
    try {
      // Use enhanced idea if available, otherwise use original
      const finalIdea = enhancedIdea || ideaText
      const projectNameToUse = suggestedProjectName || "New Project"; // Use suggested name or default

      // Reset the project store first to clear any stale values
      useProjectStore.getState().resetProject();
      
      // *** MODIFIED: Call the store action with the idea ***
      const newProjectId = await createNewProject(user.id, finalIdea);

      if (newProjectId) {
        // Update project name if a suggested name was used and different from default
        if (suggestedProjectName && suggestedProjectName !== 'Untitled Project') {
           try {
             const { error: nameUpdateError } = await supabase
               .from('projects')
               .update({ name: suggestedProjectName })
               .eq('id', newProjectId);
             if (nameUpdateError) {
               console.warn("Failed to update project name immediately:", nameUpdateError.message);
               // Non-critical, proceed with redirect
             }
           } catch (nameError) {
              console.warn("Error during immediate name update:", nameError);
           }
        }

        toast({
          title: "Project Created",
          description: "Redirecting to your new project...",
        });
        // Clear local storage after successful creation
        localStorage.removeItem("enhancedIdea");
        localStorage.removeItem("originalIdea");
        localStorage.removeItem("suggestedProjectName");
        // Redirect to the specific project page
        router.push(`/dashboard/project/${newProjectId}`);
      } else {
        throw new Error("Failed to get new project ID.");
      }
    } catch (err: any) {
      console.error("Error creating project from idea:", err)
      toast({
        title: "Project creation failed",
        description: err.message || "Could not create a new project. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCreatingProject(false);
    }
  }

  // *** ADDED: Handler for the main "New Project" button (without idea) ***
  const handleNewProjectClick = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to create a new project.",
        variant: "destructive",
      });
      return;
    }

    setIsCreatingProject(true);
    try {
      // Reset the project store first to clear any stale values
      useProjectStore.getState().resetProject();
      
      // Call the store action without an idea
      const newProjectId = await createNewProject(user.id); // No idea passed

      if (newProjectId) {
        toast({
          title: "Project Created",
          description: "Redirecting to your new project...",
        });
        // Redirect to the specific project page
        router.push(`/dashboard/project/${newProjectId}`);
      } else {
        // This case might happen if the store action returns null unexpectedly
        // The store action should ideally throw an error in failure cases handled by catch
        throw new Error("Failed to get new project ID.");
      }
    } catch (error: any) {
      console.error("Error creating new project:", error);
      toast({
        title: "Project Creation Failed",
        description: error.message || "Could not create a new project. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingProject(false);
    }
  };

  // Add this function to handle the suggestion button click
  const handleSuggestionClick = async () => {
    try {
      // Show loading state
      setIdeaText("Generating suggestion...");

      console.log("Calling /api/app-suggestions endpoint");
      // Call the app-suggestions API with x-user-id header
      const response = await fetch("/api/app-suggestions", {
        method: "GET",
        headers: {
          "x-user-id": user?.id || ""
        }
      });

      console.log("API response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("API error response:", errorData);
        throw new Error(`Failed to get suggestion: ${response.status} ${errorData.error || ''}`);
      }

      // Get the suggestion from the response
      const data = await response.json();
      console.log("Received suggestion:", data);

      // Clear all stored ideas before setting new suggestion
      localStorage.removeItem("enhancedIdea");
      localStorage.removeItem("originalIdea");
      localStorage.removeItem("suggestedProjectName");

      setEnhancedIdea(null);
      setSuggestedProjectName(null);
      setIdeaText(data.suggestion);

      toast({
        title: "App Suggestion Generated",
        description: "You can now refine or create a project with this idea.",
      });
    } catch (error) {
      console.error("Error getting suggestion:", error);
      setIdeaText(""); // Clear the loading text
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate app suggestion. Please try again.",
        variant: "destructive",
      });
    }
  };

  // --- Data Fetching Effect ---
  useEffect(() => {
    // Load idea from local storage on mount
    const savedEnhancedIdea = localStorage.getItem("enhancedIdea");
    const savedOriginalIdea = localStorage.getItem("originalIdea");
    const savedSuggestedName = localStorage.getItem("suggestedProjectName");

    if (savedEnhancedIdea) {
      setIdeaText(savedEnhancedIdea);
      setEnhancedIdea(savedEnhancedIdea);
      setSuggestedProjectName(savedSuggestedName || null);
    } else if (savedOriginalIdea) {
      setIdeaText(savedOriginalIdea);
    }
    if (savedSuggestedName) {
      setSuggestedProjectName(savedSuggestedName);
    }

    const fetchProjects = async () => {
      setError(null); // Reset error state on new fetch
      // Ensure user context and Supabase client are available
      if (!user || !supabase) {
        if (!user) console.log("No user found, skipping project fetch.");
        if (!supabase) console.log("Supabase client not available, skipping project fetch.");
        setRecentProjects([]);
        setProjectCount(0);
        setLoading(false);
        return;
      }

      // Defer heavy data loading if coming from OAuth callback
      if (typeof window !== 'undefined' && window.location.search.includes('from=oauth')) {
        console.log("Deferring project fetch for OAuth redirect performance");
        setTimeout(fetchProjects, 500); // Delay by 500ms
        return;
      }

      try {
        setLoading(true)
        console.log("Fetching projects for user:", user.id)

        // --- Mock Data Handling ---
        if (user.id === "test_user_id" || isV0Preview) {
          console.log("Using mock projects data for test user or preview")
          // Simulate network delay for mock data
          await new Promise(resolve => setTimeout(resolve, 500));
          setRecentProjects(MOCK_PROJECTS)
          setProjectCount(MOCK_PROJECTS.length)
          setLoading(false)
          return
        }

        // --- UUID Validation ---
        const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id)
        if (!isValidUUID) {
          console.error("Invalid UUID format:", user.id)
          setError("Invalid user identifier."); // Set error state
          setRecentProjects([])
          setProjectCount(0)
          setLoading(false)
          return
        }

        // --- Fetch Recent Projects and Count in Parallel ---
        console.log("Querying Supabase...");
        const [projectsResult, countResult] = await Promise.all([
          supabase
            .from("projects")
            .select("*")
            .eq("user_id", user.id)
            .order("updated_at", { ascending: false })
            .limit(3),
          supabase
            .from("projects")
            .select("*", { count: "exact", head: true })
            .eq("user_id", user.id)
        ]);

        // --- Handle Projects Result ---
        if (projectsResult.error) {
          console.error("Error fetching recent projects:", projectsResult.error)
          throw new Error(`Failed to fetch projects: ${projectsResult.error.message}`);
        }
        console.log("Fetched recent projects:", projectsResult.data);
        setRecentProjects(projectsResult.data || [])

        // --- Handle Count Result ---
        if (countResult.error) {
          console.error("Error fetching project count:", countResult.error)
          throw new Error(`Failed to fetch project count: ${countResult.error.message}`);
        }
        console.log("Fetched project count:", countResult.count);
        setProjectCount(countResult.count || 0)

        // Now fetch all project IDs for this user to count documents
        const { data: allProjectIds, error: projectIdsError } = await supabase
          .from("projects")
          .select("id")
          .eq("user_id", user.id);

        if (projectIdsError) {
          console.error("Error fetching project IDs:", projectIdsError);
          setDocumentsCount(0);
        } else if (allProjectIds && allProjectIds.length > 0) {
          const projectIds = allProjectIds.map(project => project.id);

          const { data: docCountData, error: docCountError, count: docCount } = await supabase
            .from("project_documents")
            .select("*", { count: "exact", head: true })
            .in("project_id", projectIds);

          if (docCountError) {
            console.error("Error fetching documents count:", docCountError);
            setDocumentsCount(0);
          } else {
            console.log("Fetched documents count:", docCount);
            setDocumentsCount(docCount || 0);
          }
        } else {
          setDocumentsCount(0);
        }

      } catch (err: any) {
        console.error("An error occurred during project fetching:", err)
        setError(err.message || "An unexpected error occurred while fetching data."); // Set specific error message
        setRecentProjects([]) // Reset state on error
        setProjectCount(0)
      } finally {
        setLoading(false) // Ensure loading is set to false
      }
    }

    fetchProjects()
  }, [user]) // Dependency array: re-run effect if the user object changes

  const handleDeleteProject = async () => {
    if (!projectToDelete) return

    setIsDeleting(true)
    try {
      const { error } = await supabase
        .from("projects")
        .delete()
        .eq("id", projectToDelete)

      if (error) throw error

      // Update local state to remove the deleted project
      setRecentProjects(recentProjects.filter(project => project.id !== projectToDelete))
      setProjectCount(prev => prev - 1)
      setProjectToDelete(null)
    } catch (err) {
      console.error("Error deleting project:", err)
    } finally {
      setIsDeleting(false)
    }
  }

  // Add this function to handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setUploadedFile(file)
  }

  // Add this function to process the uploaded file
  const processUploadedFile = async () => {
    if (!uploadedFile) return

    setIsUploading(true)
    try {
      // Read the file content
      const text = await uploadedFile.text()

      // Set the idea text directly
      setIdeaText(text)

      // Close the dialog
      setIsUploadDialogOpen(false)

      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

      // Optionally enhance the idea immediately
      if (text.length >= 10) {
        await handleEnhanceIdea()
      }

      toast({
        title: "File uploaded successfully",
        description: "Your file has been uploaded and content added to the idea field.",
      })
    } catch (err) {
      console.error("Error processing file:", err)
      toast({
        title: "Upload failed",
        description: "Could not process your file. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setUploadedFile(null)
    }
  }

  // --- Static Data for UI ---
  // Data for the key metrics cards
  const metrics = [
    {
      title: "Credits Remaining",
      value: user?.credits_remaining ?? "N/A",
      icon: <CreditCard className="h-5 w-5 text-emerald-500" />,
      change: user?.subscription === "pro" ? "Pro Plan" : "Free Plan",
    },
    {
      title: "Projects Created",
      value: projectCount,
      icon: <FolderKanban className="h-5 w-5 text-emerald-500" />,
      change: `Limit: ${user?.projects_limit ?? 2}`,
    },
    {
      title: "Documents Created",
      value: documentsCount,
      icon: <FileText className="h-5 w-5 text-emerald-500" />,
      change: "Total documents",
    },
  ]

  // Data for the inspiration cards
  const inspirationCards = [
    {
      id: "prd", // Added id for potential key prop or handler
      title: "Product Requirements",
      description: "Create detailed PRDs for your next feature",
      icon: <Sparkles className="h-10 w-10 text-emerald-500" />,
    },
    {
      id: "specs",
      title: "Technical Specs",
      description: "Document architecture and implementation details",
      icon: <Code2 className="h-10 w-10 text-emerald-500" />,
    },
    {
      id: "guides",
      title: "User Guides",
      description: "Create user-friendly documentation",
      icon: <BookOpen className="h-10 w-10 text-emerald-500" />,
    },
  ]

  // Data for the project template cards
  const projectTemplates = [
    {
      id: "web", // Added id
      title: "Web Application",
      description: "React, Next.js, TypeScript",
      icon: <Globe className="h-6 w-6 text-emerald-500" />,
    },
    {
      id: "mobile",
      title: "Mobile App",
      description: "React Native, Flutter",
      icon: <Smartphone className="h-6 w-6 text-emerald-500" />,
    },
    {
      id: "api",
      title: "Backend API",
      description: "Node.js, Express, MongoDB",
      icon: <Database className="h-6 w-6 text-emerald-500" />,
    },
  ]

  // Data for the documentation tips list
  const documentationTips = [
    "Start with a clear outline before diving into details",
    "Use consistent terminology throughout your documentation",
    "Include examples and code snippets where applicable",
    "Add diagrams to explain complex concepts visually",
    "Keep your audience in mind and adjust technical depth accordingly",
  ]

  // --- JSX Rendering ---
  return (
    <div className="flex flex-col gap-6 p-6"> {/* Adjusted padding */}
      {/* Text Input Area */}
      <div className="mt-24 mb-8"> {/* Adjusted margins */}
        <h1 className="mb-4 text-4xl font-bold tracking-tight text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          What do you want to build?
        </h1>
        {/* */}
        <p className="text-center text-muted-foreground max-w-xl mx-auto mb-16 text-sm">
          Provibe creates{" "}
          <span className="inline-block bg-primary/10 text-primary px-2 py-0.5 rounded-md font-medium mx-0.5">
            PRD
          </span>
          ,{" "}
          <span className="inline-block bg-primary/10 text-primary px-2 py-0.5 rounded-md font-medium mx-0.5">
            User Flow
          </span>
          ,{" "}
          <span className="inline-block bg-primary/10 text-primary px-2 py-0.5 rounded-md font-medium mx-0.5">
            Architecture
          </span>
          , and other specs for your AI coding agents and developers to build your product.
        </p>
        

        <div className="relative max-w-3xl mx-auto">
          <textarea
            className="min-h-[150px] w-full text-center resize-none rounded-lg border border-input bg-card px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring " // focus-visible:ring-2  focus-visible:ring-offset-2
            placeholder={isTextareaFocused ? "" : "Describe your product idea..."}
            value={ideaText} // Bind directly to ideaText
            onChange={handleIdeaChange}
            onFocus={() => {
              setIsTextareaFocused(true);
              if (!hasTyped) {
                setIdeaText(""); // Preserve original logic: clear pre-filled (non-typed) text on focus
              }
            }}
            onBlur={() => setIsTextareaFocused(false)}
          />
          <div className="absolute bottom-3 right-3 flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              disabled={!isIdeaLongEnough || isEnhancing || isCreatingProject} // Disable during creation too
              onClick={handleEnhanceIdea}
              title="Enhance Idea"
            >
              <WandSparkles className={`h-5 w-5 ${isIdeaLongEnough ? 'text-emerald-700' : 'text-gray-400'}`} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              disabled={!isIdeaLongEnough || isCreatingProject || isEnhancing} // Disable during enhancing too
              onClick={handleCreateProjectFromIdea} // Use specific handler for this button
              title="Create Project from Idea"
            >
              {isCreatingProject ? (
                 <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                 <SendHorizonal className={`h-5 w-5 ${isIdeaLongEnough ? 'text-emerald-700' : 'text-gray-400'}`} />
              )}
            </Button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-4 mb-12 flex flex-wrap justify-center gap-3 max-w-3xl mx-auto">
          <Button 
            variant="outline" 
            className="rounded-full h-9 px-4 text-sm"
            onClick={handleSuggestionClick}
            disabled={ideaText === "Generating suggestion..."}
          >
            {ideaText === "Generating suggestion..." ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Shuffle className="mr-2 h-4 w-4" />
                Suggestions
              </>
            )}
          </Button>
          <Button 
            variant="outline" 
            className="rounded-full h-9 px-4 text-sm"
            disabled={true} // Disabled
            title="Coming soon"
          >
            <FileImage className="mr-2 h-4 w-4" />
            Upload image
          </Button>
          <Button
            variant="outline"
            className="rounded-full h-9 px-4 text-sm"
            disabled={true} // Disabled
            title="Coming soon"
          >
            <Paperclip className="mr-2 h-4 w-4" />
            Upload
          </Button>
          <Button 
            variant="outline" 
            className="rounded-full h-9 px-4 text-sm"
            disabled={true} // Disabled
            title="Coming soon"
          >
            <Mic className="mr-2 h-4 w-4" />
            Record
          </Button>
        </div>
      </div>

      {/* Welcome Header Section - Adjusted alignment to match sidebar logo level */}
      <div className="flex flex-col justify-between space-y-1 md:flex-row md:items-center md:space-y-0">
        <div>
          <h2 className="text-lg font-normal tracking-tight leading-tight mb-2">Welcome back, {user?.name || 'User'}</h2>
          <p className="text-sm text-muted-foreground mt-0 leading-tight">Here's an overview of your documentation projects</p>
        </div>
        {/*
        <div className="flex space-x-4">
           *** MODIFIED: Updated "New Project" button *** 
          <Button onClick={handleNewProjectClick} disabled={isCreatingProject}>
            {isCreatingProject ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Plus className="mr-2 h-4 w-4" />
            )}
            New Project
          </Button>
          
        </div>
        */}
      </div>

       {/* Display Error Message if any */}
       {error && (
        <Card className="border-destructive bg-destructive/10">
          <CardHeader>
            <CardTitle className="text-destructive text-lg">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Key Metrics Section */}
      <div className="grid gap-6 md:grid-cols-3">
        {metrics.map((metric, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                {/* Metric title style remains text-sm */}
                <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                {metric.icon}
              </div>
            </CardHeader>
            <CardContent>
              {/* Metric value style remains text-2xl */}
              <div className="text-2xl font-bold">{metric.value}</div>
              <p className="text-xs text-muted-foreground mt-1">{metric.change}</p>
            </CardContent>
          </Card>
        ))}
      </div>


      {/* Main Content - Two Column Layout */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Recent Projects Section */}
        <Card className="h-full flex flex-col">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div>
              {/* Updated Card Title style */}
              <CardTitle className="text-lg font-semibold">Recent Projects</CardTitle>
              {/* Updated Card Description style */}
              <CardDescription className="text-sm text-muted-foreground">Your recently updated projects</CardDescription>
            </div>
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard/project">
                View all <ArrowUpRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent className="space-y-4 flex-grow pt-4"> {/* Added pt-4 */}
            {loading ? (
              // Loading State: Simple spinner
              <div className="flex items-center justify-center h-40">
                <Loader2 className="h-8 w-8 animate-spin text-emerald-500" /> {/* Use Loader2 */}
              </div>
            ) : recentProjects.length > 0 ? (
              // Data Loaded State
              recentProjects.map((project) => (
                <div key={project.id} className="rounded-lg border p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-start justify-between gap-4"> {/* Added gap */}
                    <div className="flex-1">
                      {/* Updated project name style */}
                      <h3 className="text-base font-semibold">{project.name || "Untitled Project"}</h3>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {project.idea || "No description provided."}
                      </p>
                    </div>
                    <div className="flex gap-2 flex-shrink-0">
                      {/* *** MODIFIED: Link to the new project page structure *** */}
                      <Button variant="ghost" size="icon" asChild>
                        <Link href={`/dashboard/project/${project.id}`}>
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Edit Project {project.name}</span>
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setProjectToDelete(project.id)}
                        disabled={isDeleting && projectToDelete === project.id} // Disable while deleting this specific project
                      >
                        {isDeleting && projectToDelete === project.id ? (
                           <Loader2 className="h-4 w-4 animate-spin text-red-500" />
                        ) : (
                           <Trash2 className="h-4 w-4 text-red-500" />
                        )}
                        <span className="sr-only">Delete Project {project.name}</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              // Empty State
              <div className="flex flex-col items-center justify-center h-40 text-center">
                <FolderKanban className="h-12 w-12 text-muted-foreground mb-3" />
                <p className="text-muted-foreground mb-4">You haven't created any projects yet.</p>
                {/* *** MODIFIED: Use onClick for the button in empty state *** */}
                <Button onClick={handleNewProjectClick} disabled={isCreatingProject}>
                   {isCreatingProject ? (
                     <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                   ) : (
                     <Plus className="mr-2 h-4 w-4" />
                   )}
                   Create Your First Project
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Inspiration Section */}
        <Card className="h-full flex flex-col">
          <CardHeader>
            <div className="flex items-center">
              <Lightbulb className="mr-2 h-5 w-5 text-emerald-500" />
              {/* Updated Card Title style */}
              <CardTitle className="text-lg font-semibold">Inspiration</CardTitle>
            </div>
            {/* Updated Card Description style */}
            <CardDescription className="text-sm text-muted-foreground">Ideas for your next documentation project</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-4 grid-cols-1 sm:grid-cols-2 flex-grow pt-4"> {/* Added pt-4 */}
            {inspirationCards.map((card) => (
              <div
                key={card.id} // Use id as key
                className="flex flex-col items-center rounded-lg border p-4 text-center hover:bg-muted/50 transition-colors cursor-pointer"
                // onClick={() => handleInspirationClick(card.id)} // Example onClick
              >
                {card.icon}
                 {/* Updated inspiration title style */}
                <h3 className="mt-3 text-base font-semibold">{card.title}</h3>
                <p className="mt-1 text-xs text-muted-foreground">{card.description}</p>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Documentation Tips Section */}
        <Card className="h-full flex flex-col">
          <CardHeader>
            <div className="flex items-center">
              <Zap className="mr-2 h-5 w-5 text-emerald-500" />
              {/* Updated Card Title style */}
              <CardTitle className="text-lg font-semibold">Tips for Better Docs</CardTitle>
            </div>
            {/* Updated Card Description style */}
            <CardDescription className="text-sm text-muted-foreground">Best practices for effective documentation</CardDescription>
          </CardHeader>
          <CardContent className="flex-grow pt-4"> {/* Added pt-4 */}
            <ul className="space-y-3">
              {documentationTips.map((tip, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle2 className="mr-2 h-4 w-4 text-emerald-500 mt-0.5 flex-shrink-0" />
                  {/* Tip text style remains text-sm */}
                  <span className="text-sm">{tip}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Project Templates Section */}
        <Card className="h-full flex flex-col">
          <CardHeader>
            <div className="flex items-center">
              <FileCode className="mr-2 h-5 w-5 text-emerald-500" />
              {/* Updated Card Title style */}
              <CardTitle className="text-lg font-semibold">Project Templates</CardTitle>
            </div>
            {/* Updated Card Description style */}
            <CardDescription className="text-sm text-muted-foreground">Start quickly with pre-built structures</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 flex-grow pt-4"> {/* Added pt-4 */}
            {projectTemplates.map((template) => (
              <div
                key={template.id} // Use id as key
                className="flex items-center justify-between rounded-lg border p-3 hover:bg-muted/50 transition-colors cursor-pointer"
                // onClick={() => handleTemplateClick(template.id)} // Example onClick
              >
                <div className="flex items-center">
                  {template.icon}
                  <div className="ml-3">
                    {/* Updated template title style */}
                    <h3 className="text-base font-semibold">{template.title}</h3>
                    <p className="text-xs text-muted-foreground">{template.description}</p>
                  </div>
                </div>
                <Button variant="ghost" size="icon">
                  <ArrowUpRight className="h-4 w-4" />
                   <span className="sr-only">Use template {template.title}</span>
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!projectToDelete} onOpenChange={(open) => !open && setProjectToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this project and all associated documents.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel> {/* Disable cancel while deleting */}
            <AlertDialogAction
              onClick={handleDeleteProject}
              className="bg-red-600 hover:bg-red-700"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Upload File Dialog */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Upload File</DialogTitle>
            <DialogDescription>
              Upload a .md or .txt file to use as your project idea.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="file-upload" className="text-right">
                File
              </Label>
              <Input
                id="file-upload"
                type="file"
                ref={fileInputRef}
                accept=".md,.txt"
                onChange={handleFileUpload}
                className="col-span-3"
              />
            </div>
            {uploadedFile && (
              <p className="text-sm text-muted-foreground">
                Selected file: {uploadedFile.name}
              </p>
            )}
          </div>
          <DialogFooter>
            <Button
              type="submit"
              onClick={processUploadedFile}
              disabled={!uploadedFile || isUploading}
            >
              {isUploading ? <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</> : "Upload"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}