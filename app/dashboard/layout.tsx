"use client"

import type React from "react"
import { useEffect, useState, useCallback } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/components/auth/auth-provider"
import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar"
import { DashboardShell } from "@/components/dashboard/dashboard-shell" // Import DashboardShell
import { Loader2 } from "lucide-react"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  // Set initial sidebar state from localStorage
  useEffect(() => {
    const savedState = localStorage.getItem("sidebarCollapsed")
    if (savedState !== null) {
      setSidebarCollapsed(savedState === "true")
    }
  }, [])

  // Update sidebar state when it changes
  const handleSidebarCollapseChange = useCallback((isCollapsed: boolean) => {
    // Only update if the value actually changed
    setSidebarCollapsed((prevState) => {
      if (prevState !== isCollapsed) {
        localStorage.setItem("sidebarCollapsed", String(isCollapsed))
        return isCollapsed;
      }
      return prevState;
    });
  }, [setSidebarCollapsed]); // Added setSidebarCollapsed for completeness

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/auth/login")
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-background dark:bg-slate-900"> {/* Use min-h-screen and theme background */}
      <DashboardSidebar onCollapseChange={handleSidebarCollapseChange} />
      <DashboardShell isSidebarCollapsed={sidebarCollapsed}>
        {children}
      </DashboardShell>
    </div>
  )
}
