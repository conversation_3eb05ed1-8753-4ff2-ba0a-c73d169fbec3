// @ts-nocheck
"use client"

import React, { memo, useMemo, useReducer } from "react";
import { useState, useEffect, useCallback } from "react"
import { Plus, Loader2, FileCode, FileSpreadsheet, FolderKanban, Trash2, Grid, List, FileText, Search, Clock, Star, ArrowUpRight } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/components/auth/auth-provider"
import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { PageHeader } from "@/components/dashboard/page-header"
import { supabase } from "@/lib/supabase-client"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialog<PERSON>it<PERSON>,
} from "@/components/ui/alert-dialog"
import { useProjectStore } from '@/lib/store/project';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { ProjectCard } from './[id]/components/ProjectCard';
import { formatDistanceToNow } from 'date-fns';
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useDebounce } from "@/hooks/useDebounce";
import { useMemo } from "react";

const ProjectsList = memo(function ProjectsList({ projects, loading, error, viewMode, onNew, onDelete }) {
  return (
    <TabsContent value="projects" className="space-y-4 mt-4">
      {loading ? (
        <div className="flex h-[200px] items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="flex h-[200px] flex-col items-center justify-center p-6">
            <p className="text-center text-muted-foreground">{error}</p>
            <Button variant="outline" className="mt-4" onClick={onNew}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      ) : projects.length === 0 ? (
        <Card>
          <CardContent className="flex h-[200px] flex-col items-center justify-center p-6">
            <FolderKanban className="h-12 w-12 text-muted-foreground" />
            <p className="mt-4 text-center text-muted-foreground">You don't have any projects yet.</p>
            <Button onClick={onNew} className="mt-4">
              Create Your First Project
            </Button>
          </CardContent>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {projects.map(project => (
            <ProjectCard key={project.id} project={project} onDelete={onDelete} />
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="border rounded-md overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Documents</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {projects.map(project => (
                  <tr key={project.id} className="hover:bg-gray-50">
                    <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div className="flex items-center">
                        <FolderKanban className="h-5 w-5 text-blue-500 mr-2" />
                        <a href={`/dashboard/project/${project.id}`} className="hover:underline line-clamp-1">
                          {project.name || 'Untitled Project'}
                        </a>
                      </div>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                      {project.document_count || 0} {project.document_count === 1 ? 'Document' : 'Documents'}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                      {project.formattedUpdatedAt || 'Never'}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Button variant="ghost" size="sm" asChild>
                          <a href={`/dashboard/project/${project.id}`}>View</a>
                        </Button>
                        <Button variant="ghost" size="icon" className="text-destructive" onClick={() => onDelete(project.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </TabsContent>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.loading === nextProps.loading &&
    prevProps.error === nextProps.error &&
    prevProps.viewMode === nextProps.viewMode &&
    prevProps.projects.length === nextProps.projects.length &&
    prevProps.projects === nextProps.projects
  );
});

const DocumentsList = memo(function DocumentsList({ documents, loading, error, viewMode, loadMore, onDelete, hasMore }) {
  return (
    <TabsContent value="documents" className="space-y-4 mt-4">
      {loading && documents.length === 0 ? (
        <div className="flex h-[300px] items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <Card>
          <CardContent className="flex h-[300px] flex-col items-center justify-center p-6">
            <p className="text-center text-muted-foreground">{error}</p>
            <Button variant="outline" className="mt-4" onClick={() => loadMore(0)}>
              Try Again
            </Button>
          </CardContent>
        </Card>
      ) : documents.length === 0 ? (
        <Card>
          <CardContent className="flex h-[300px] flex-col items-center justify-center p-6">
            <FileText className="h-12 w-12 text-muted-foreground" />
            <p className="mt-4 text-center text-muted-foreground">No documents found for this user.</p>
          </CardContent>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {documents.map(doc => (
            <Card key={doc.id} className="overflow-hidden">
              {/* ...existing grid card markup... */}
              <CardHeader className="p-4">
                <div className="flex items-start justify-between">
                  {/* You may need to pass getDocumentIcon or adapt this part */}
                  {/* getDocumentIcon(doc.type) */}
                  <FileText className="h-5 w-5 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <CardTitle className="line-clamp-1">{doc.title}</CardTitle>
                <div className="text-sm text-muted-foreground mt-1">
                  <Badge variant="outline" className="mr-2">
                    {doc.type}
                  </Badge>
                  <span>{doc.project?.name || 'No Project'}</span>
                </div>
              </CardContent>
              <CardFooter className="border-t p-4">
                <div className="flex w-full items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <Clock className="mr-1 h-3 w-3" />
                    Updated {doc.formattedUpdatedAt || 'Never'}
                  </div>
                  {/* ...actions... */}
                  <Link href={`/dashboard/project/${doc.project.id}?selectedDocId=${doc.id}`}>
                    <ArrowUpRight className="h-4 w-4" />
                  </Link>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          <div className="border rounded-md overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Document Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Updated</th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {documents.map(doc => (
                  <tr key={doc.id} className="hover:bg-gray-50">
                    <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                      <div className="flex items-center">
                        <FileText className="h-5 w-5 text-muted-foreground mr-2" />
                        <span className="line-clamp-1">{doc.title}</span>
                      </div>
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                      {doc.project?.name || 'No Project'}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                      {doc.created_at ? formatDistanceToNow(new Date(doc.created_at), { addSuffix: true }) : 'Never'}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-500">
                      {doc.formattedUpdatedAt || 'Never'}
                    </td>
                    <td className="px-6 py-3 whitespace-nowrap text-right text-sm font-medium">
                      <Button variant="ghost" size="icon" asChild>
                        <Link href={`/dashboard/project/${doc.project.id}?selectedDocId=${doc.id}`}>
                          <ArrowUpRight className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="ghost" size="icon" className="text-destructive" onClick={() => onDelete(doc.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      {hasMore && (
        <div className="flex justify-center mt-4">
          <Button onClick={loadMore} disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Load More Documents
          </Button>
        </div>
      )}
    </TabsContent>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.loading === nextProps.loading &&
    prevProps.error === nextProps.error &&
    prevProps.viewMode === nextProps.viewMode &&
    prevProps.hasMore === nextProps.hasMore &&
    prevProps.documents.length === nextProps.documents.length &&
    prevProps.documents === nextProps.documents
  );
});
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function ProjectsPage() {
  const { user } = useAuth();
  const router = useRouter();

  // Project States
  const [projects, setProjects] = useState<any[]>([]);
  const [loadingProjects, setLoadingProjects] = useState(true);
  const [projectError, setProjectError] = useState<string | null>(null);
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<string | null>(null);
  const [isDeletingProject, setIsDeletingProject] = useState(false);
  const [projectViewMode, setProjectViewMode] = useState<'grid' | 'list'>('list');

  // Document States (useReducer)
  const initialDocState = {
    items: [],
    loading: false,
    error: null,
    searchQuery: "",
    toDelete: null,
    isDeleting: false,
    viewMode: 'list',
    currentPage: 0,
    itemsPerPage: 10,
    hasMore: true,
  };

  function docReducer(state, action) {
    switch (action.type) {
      case 'SET_FIELD':
        return { ...state, [action.field]: action.value };
      case 'RESET_ON_TAB_SWITCH':
        return { ...state, items: [], loading: false, error: null, currentPage: 0, hasMore: true };
      default:
        return state;
    }
  }

  const [docState, dispatch] = useReducer(docReducer, initialDocState);
  const debouncedDocumentSearch = useDebounce(docState.searchQuery, 300);

  const { setProjectId } = useProjectStore();
  const [activeTab, setActiveTab] = useState<'projects' | 'documents'>('projects');

  // Load projects on mount (or when tab is projects)
  useEffect(() => {
    if (!user || activeTab !== 'projects' || projects.length > 0) return;

    const fetchProjects = async () => {
      setLoadingProjects(true);
      setProjectError(null);

      if (!supabase) {
        console.error("Supabase client not initialized.");
        setLoadingProjects(false);
        setProjectError("Database connection failed. Please try again.");
        return;
      }

      try {
        // Fetch projects with document counts via RPC (limit to 50 for performance)
        const { data, error: projectsError } = await supabase
          .rpc('get_project_counts', { _user_id: user.id })
          .limit(50);

        if (projectsError) throw projectsError;

        const formattedProjects = (data || []).map(p => ({
          ...p,
          formattedUpdatedAt: p.updated_at ? formatDistanceToNow(new Date(p.updated_at), { addSuffix: true }) : null
        }));
        
        setProjects(formattedProjects);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setProjectError('Failed to load projects. Please try again.');
      } finally {
        setLoadingProjects(false);
      }
    };

    fetchProjects();
  }, [user, activeTab, projects.length]);

  // Create a new project
  const handleNewProjectClick = async () => {
    if (isCreatingProject) return;
    if (!user) {
      toast.error("You need to be logged in to create a project.");
      return;
    }

    setIsCreatingProject(true);
    
    if (!supabase) {
      console.error("Supabase client not initialized.");
      toast.error("Database connection failed: Please try again.");
      setIsCreatingProject(false);
      return;
    }

    try {
      // Create a new project
      const { data, error } = await supabase
        .from('projects')
        .insert({
          name: 'New Project',
          user_id: user.id,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          status: 'draft'
        })
        .select()
        .single();
        
      if (error) throw error;
      
      // Set the project ID in the store
      setProjectId(data.id);
      
      // Redirect to the project page
      router.push(`/dashboard/project/${data.id}`);
    } catch (err) {
      console.error('Error creating project:', err);
      toast.error("Failed to create project: Please try again later.");
    } finally {
      setIsCreatingProject(false);
    }
  };

  // Delete a project
  const handleDeleteProject = async () => {
    if (!projectToDelete || isDeletingProject) return;
    
    setIsDeletingProject(true);
    
    if (!supabase) {
      console.error("Supabase client not initialized.");
      toast.error("Database connection failed: Please try again.");
      setIsDeletingProject(false);
      return;
    }

    try {
      // Delete the project
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectToDelete);
        
      if (error) throw error;
      
      // Update the local state
      setProjects(projects.filter(p => p.id !== projectToDelete));
      
      toast.success("Project deleted: The project has been successfully deleted.");
    } catch (err) {
      console.error('Error deleting project:', err);
      toast.error("Failed to delete project: Please try again later.");
    } finally {
      setIsDeletingProject(false);
      setProjectToDelete(null);
    }
  };

  // --- Document Related Logic (Adapted from documents/page.tsx) ---

  // Check if we're in the v0 preview environment (Needed for mock data handling if any)
  const isV0Preview = typeof window !== "undefined" && window.location.hostname.includes("vusercontent.net")

  // Fetch documents function (memoized)
  const fetchDocuments = useCallback(
    async (page: number, limit: number, search: string, ignore: boolean) => {
      if (!user) {
        if (!ignore) {
          dispatch({ type: 'SET_FIELD', field: 'loading', value: false });
          dispatch({ type: 'SET_FIELD', field: 'items', value: [] });
          dispatch({ type: 'SET_FIELD', field: 'hasMore', value: false });
        }
        return;
      }

      if (!supabase) {
        console.error("Supabase client not initialized.");
        if (!ignore) {
          dispatch({ type: 'SET_FIELD', field: 'loading', value: false });
          dispatch({ type: 'SET_FIELD', field: 'items', value: [] });
          dispatch({ type: 'SET_FIELD', field: 'hasMore', value: false });
          dispatch({ type: 'SET_FIELD', field: 'error', value: "Database connection failed. Please try again." });
        }
        return;
      }

      if (user.id === "test_user_id" || isV0Preview) {
        console.log("Using mock documents data for test user or preview");
        // Handle mock data filtering/pagination if necessary for testing large mock sets
        // Note: No mock data defined in this file currently. Will need to add or remove if not using mocks.
        if (!ignore) {
          dispatch({ type: 'SET_FIELD', field: 'loading', value: false });
          dispatch({ type: 'SET_FIELD', field: 'items', value: [] });
          dispatch({ type: 'SET_FIELD', field: 'hasMore', value: false });
        }
        return;
      }

      const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id);

      if (!isValidUUID) {
        console.error("Invalid UUID format:", user.id);
        if (!ignore) {
          dispatch({ type: 'SET_FIELD', field: 'items', value: [] });
          dispatch({ type: 'SET_FIELD', field: 'loading', value: false });
          dispatch({ type: 'SET_FIELD', field: 'hasMore', value: false });
        }
        return;
      }

      try {
        // Modified select query to join and fetch project name
        let query = supabase
          .from("project_documents")
          .select(`
             *,
             projects ( name )
          `)
          .eq("user_id", user.id);

        if (search) {
          query = query.ilike(`title`, `%${search}%`);
        }

        const start = page * limit;
        const end = start + limit - 1;
        query = query.range(start, end);

        query = query.order("updated_at", { ascending: false });

        const { data: documentsData, error: documentsError } = await query;

        if (documentsError) {
          throw documentsError;
        }

        // Map data, correctly accessing the nested project name
        const docsWithProjectInfo = documentsData.map((doc: any) => ({
          ...doc,
          // Accessing the nested project name from the join
          project: { name: doc.projects?.name || 'No Project', id: doc.project_id || 'unknown' },
          starred: false,
        }));

        const enriched = docsWithProjectInfo.map(d => ({
          ...d,
          formattedUpdatedAt: formatDistanceToNow(new Date(d.updated_at), { addSuffix: true })
        }));

        if (!ignore) {
          if (page === 0) {
            dispatch({ type: 'SET_FIELD', field: 'items', value: enriched || [] });
          } else {
            dispatch({ type: 'SET_FIELD', field: 'items', value: [...docState.items, ...(enriched || [])] });
          }
          dispatch({ type: 'SET_FIELD', field: 'hasMore', value: (documentsData?.length || 0) === limit });
          dispatch({ type: 'SET_FIELD', field: 'error', value: null });
        }

      } catch (err: any) {
        console.error("Error fetching documents:", err)
        if (!ignore) {
          dispatch({ type: 'SET_FIELD', field: 'error', value: err.message || "Failed to load documents" });
          dispatch({ type: 'SET_FIELD', field: 'items', value: [] });
          dispatch({ type: 'SET_FIELD', field: 'hasMore', value: false });
        }
      } finally {
        if (!ignore) {
          dispatch({ type: 'SET_FIELD', field: 'loading', value: false });
        }
      }
    },
    [user, isV0Preview]
  );

  // Effect to handle tab switching
  useEffect(() => {
    if (activeTab !== 'documents') {
      // Reset document states when switching away from the documents tab
      dispatch({ type: 'RESET_ON_TAB_SWITCH' });
    }
  }, [activeTab]);

  // Effect to handle initial fetch when switching to documents tab
  useEffect(() => {
    if (activeTab !== 'documents') return;
    
    // Skip fetch if documents already loaded for current search and page
    if (docState.items.length > 0 && docState.currentPage === 0 && !docState.loading) {
      return;
    }

    let ignore = false; // Flag to ignore outdated responses

    dispatch({ type: 'SET_FIELD', field: 'loading', value: true });
    if (docState.currentPage === 0) {
      dispatch({ type: 'SET_FIELD', field: 'items', value: [] }); // Clear documents only on initial fetch or search change
    }
    dispatch({ type: 'SET_FIELD', field: 'error', value: null }); // Clear previous errors
    dispatch({ type: 'SET_FIELD', field: 'hasMore', value: true }); // Assume more data on new fetch

    fetchDocuments(docState.currentPage, docState.itemsPerPage, debouncedDocumentSearch, ignore);

    // Cleanup function to set ignore to true when the effect re-runs or component unmounts
    return () => {
      ignore = true;
    };
  }, [activeTab, fetchDocuments]);

  // Effect to handle pagination changes
  useEffect(() => {
    if (activeTab !== 'documents' || docState.currentPage === 0) return;

    let ignore = false;
    dispatch({ type: 'SET_FIELD', field: 'loading', value: true });
    fetchDocuments(docState.currentPage, docState.itemsPerPage, debouncedDocumentSearch, ignore);

    return () => {
      ignore = true;
    };
  }, [docState.currentPage, activeTab, fetchDocuments, docState.itemsPerPage, debouncedDocumentSearch]);

  // Effect to handle search changes
  useEffect(() => {
    if (activeTab !== 'documents') return;

    let ignore = false;
    dispatch({ type: 'SET_FIELD', field: 'loading', value: true });
    dispatch({ type: 'SET_FIELD', field: 'currentPage', value: 0 });
    dispatch({ type: 'SET_FIELD', field: 'items', value: [] });
    dispatch({ type: 'SET_FIELD', field: 'error', value: null });
    dispatch({ type: 'SET_FIELD', field: 'hasMore', value: true });

    fetchDocuments(0, docState.itemsPerPage, debouncedDocumentSearch, ignore);

    return () => {
      ignore = true;
    };
  }, [debouncedDocumentSearch, activeTab, fetchDocuments, docState.itemsPerPage]);

  // Function to load more documents
  const loadMoreDocuments = () => {
    if (docState.hasMore && !docState.loading) {
      dispatch({ type: 'SET_FIELD', field: 'currentPage', value: docState.currentPage + 1 });
    }
  };

  // Toggle star status for a document (Client-side only for now)
  const toggleStar = (docId: string) => {
    dispatch({
      type: 'SET_FIELD',
      field: 'items',
      value: docState.items.map((doc) => (doc.id === docId ? { ...doc, starred: !doc.starred } : doc))
    });
    // TODO: Implement server-side star update if needed
  }

  // Get document icon based on type
  const getDocumentIcon = (type: string) => {
    switch (type) {
      case "prd":
      case "user_flow":
        return <FileText className="h-5 w-5 text-emerald-500" />
      case "architecture":
      case "api_spec":
        return <FileCode className="h-5 w-5 text-blue-500" />
      case "schema":
        return <FileSpreadsheet className="h-5 w-5 text-orange-500" />
      default:
        return <FileText className="h-5 w-5 text-muted-foreground" />
    }
  }

  // Add delete document functionality
  const handleDeleteDocument = async () => {
    if (!docState.toDelete || docState.isDeleting) return

    dispatch({ type: 'SET_FIELD', field: 'isDeleting', value: true });
    try {
      // First, delete associated document chunks
      const { error: chunksError } = await supabase
        .from("document_chunks")
        .delete()
        .eq("document_id", docState.toDelete);

      if (chunksError) throw chunksError;

      // Then, delete the document itself
      const { error: documentError } = await supabase
        .from("project_documents")
        .delete()
        .eq("id", docState.toDelete)

      if (documentError) throw documentError

      // Update local state to remove the deleted document
      dispatch({
        type: 'SET_FIELD',
        field: 'items',
        value: docState.items.filter(doc => doc.id !== docState.toDelete)
      });
      dispatch({ type: 'SET_FIELD', field: 'toDelete', value: null });
      toast.success("Document deleted: The document has been successfully deleted.");
    } catch (err: any) {
      console.error("Error deleting document:", err)
      dispatch({ type: 'SET_FIELD', field: 'error', value: err.message || "Failed to delete document" });
      toast.error("Failed to delete document: Please try again later.");
    } finally {
      dispatch({ type: 'SET_FIELD', field: 'isDeleting', value: false });
    }
  }

  // --- End of Document Related Logic ---

  return (
    <div className="w-full min-h-screen bg-background text-foreground p-4 space-y-4">
      <PageHeader
        className="h-[56px] p-1"
        title={
          <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            {activeTab === 'projects' ? 'Projects' : 'Documents'}
          </span>
        }
      >
        {activeTab === 'projects' && (
          <Button onClick={handleNewProjectClick} disabled={isCreatingProject || !user}>
                {isCreatingProject ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="mr-2 h-4 w-4" />
                )}
                New Project
              </Button>
        )}
      </PageHeader>
          {/* Tabs and Controls Section */}
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'projects' | 'documents')} className="w-full">
             <div className="flex flex-col space-y-2 md:flex-row md:items-center md:space-y-0 md:space-x-4">
              {/* Tabs List */}
              <TabsList className="flex-shrink-0">
                <TabsTrigger value="projects">Projects</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
              </TabsList>

              {/* Controls for Projects Tab */}
              {activeTab === 'projects' && (
                 <div className="flex flex-col space-y-2 md:flex-row md:items-center md:space-y-0 md:space-x-4 w-full">
                <div className="relative flex-1">
                      {/* Project Search (Currently commented out) */}
                  {/* <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" /> */}
                  {/* <Input
                      type="search"
                      placeholder="Search projects..."
                      className="pl-8"
                      // value={searchQuery}
                      // onChange={(e) => setSearchQuery(e.target.value)}
                    /> */}
                </div>
                    {/* Project View Mode Switch */}
                    <div className="flex space-x-2 flex-shrink-0">
                      <Button
                        variant={projectViewMode === 'grid' ? 'secondary' : 'outline'}
                        size="icon"
                        onClick={() => setProjectViewMode('grid')}
                      >
                        <Grid className="h-4 w-4" />
                      </Button>
                      <Button
                        variant={projectViewMode === 'list' ? 'secondary' : 'outline'}
                        size="icon"
                        onClick={() => setProjectViewMode('list')}
                      >
                        <List className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
              )}
               {/* Controls for Documents Tab */}
               {activeTab === 'documents' && (
                 <div className="flex flex-col space-y-2 md:flex-row md:items-center md:space-y-0 md:space-x-4 w-full">
                   {/* Document Search Bar */}
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search documents..."
                  className="pl-8"
                  value={docState.searchQuery}
                  onChange={(e) => dispatch({ type: 'SET_FIELD', field: 'searchQuery', value: e.target.value })}
                />
              </div>
                    {/* Document View Mode Switch */}
                <div className="flex space-x-2 flex-shrink-0">
                  <Button
                    variant={docState.viewMode === 'grid' ? 'secondary' : 'outline'}
                    size="icon"
                    onClick={() => dispatch({ type: 'SET_FIELD', field: 'viewMode', value: 'grid' })}
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={docState.viewMode === 'list' ? 'secondary' : 'outline'}
                    size="icon"
                    onClick={() => dispatch({ type: 'SET_FIELD', field: 'viewMode', value: 'list' })}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
               )}
            </div>

            {/* Tabs Content - Projects & Documents using memoized lists */}
            <ProjectsList
              projects={projects}
              loading={loadingProjects}
              error={projectError}
              viewMode={projectViewMode}
              onNew={handleNewProjectClick}
              onDelete={setProjectToDelete}
            />
            <DocumentsList
              documents={docState.items}
              loading={docState.loading}
              error={docState.error}
              viewMode={docState.viewMode}
              loadMore={loadMoreDocuments}
              onDelete={(id) => dispatch({ type: 'SET_FIELD', field: 'toDelete', value: id })}
              hasMore={docState.hasMore}
            />
          </Tabs>

        {/* Delete Project Confirmation Dialog */}
        <AlertDialog open={!!projectToDelete} onOpenChange={(open) => !open && setProjectToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete this project and all associated documents.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteProject}
                className="bg-red-600 hover:bg-red-700"
                disabled={isDeletingProject}
              >
                {isDeletingProject ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

         {/* Delete Document Confirmation Dialog */}
        <AlertDialog open={!!docState.toDelete} onOpenChange={(open) => !open && dispatch({ type: 'SET_FIELD', field: 'toDelete', value: null })}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete this document.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteDocument}
                className="bg-red-600 hover:bg-red-700"
                disabled={docState.isDeleting}
              >
                {docState.isDeleting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
    </div>
  )
}
