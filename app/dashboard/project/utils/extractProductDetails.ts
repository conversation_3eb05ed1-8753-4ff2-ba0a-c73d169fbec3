export type ProductDetails = {
  targetAudience?: string
  keyFeatures?: string[]
  frontendTech?: string[]
  backendTech?: string[]
  usp?: string[]
  problemSolved?: string
}

export function extractProductDetails(data: ProductDetailsSourceData | null | undefined): ProductDetails {
  const defaultDetails: ProductDetails = {
    targetAudience: '',
    keyFeatures: [],
    frontendTech: [],
    backendTech: [],
    usp: [],
    problemSolved: ''
  }

  if (!data) return defaultDetails

  try {
    // Case 1: Nested structure from Step1 API response
    if (data.product_details && data.product_details.productDetails) {
      const pd = data.product_details.productDetails
      return {
        targetAudience: pd.targetAudience || '',
        keyFeatures: Array.isArray(pd.keyFeatures) ? pd.keyFeatures : [],
        frontendTech: Array.isArray(pd.frontendTech) ? pd.frontendTech : [],
        backendTech: Array.isArray(pd.backendTech) ? pd.backendTech : [],
        usp: Array.isArray(pd.usp) ? pd.usp : [],
        problemSolved: pd.problemSolved || ''
      }
    }

    // Case 2: Direct structure in product_details
    if (data.product_details && typeof data.product_details === 'object') {
      const pd = data.product_details
      if (pd.targetAudience !== undefined || pd.keyFeatures !== undefined) {
        return {
          targetAudience: pd.targetAudience || '',
          keyFeatures: Array.isArray(pd.keyFeatures) ? pd.keyFeatures : [],
          frontendTech: Array.isArray(pd.frontendTech) ? pd.frontendTech : [],
          backendTech: Array.isArray(pd.backendTech) ? pd.backendTech : [],
          usp: Array.isArray(pd.usp) ? pd.usp : [],
          problemSolved: pd.problemSolved || ''
        }
      }
    }

    // Case 3: Individual fields
    return {
      targetAudience: data.tg || '',
      keyFeatures: Array.isArray(data.features) ? data.features : [],
      frontendTech: Array.isArray(data.frontendTech) ? data.frontendTech : [],
      backendTech: Array.isArray(data.backendTech) ? data.backendTech : [],
      usp: Array.isArray(data.usp) ? data.usp : [],
      problemSolved: data.problemSolved || ''
    }
  } catch (e) {
    console.error('Error extracting product details:', e)
    return defaultDetails
  }
}
