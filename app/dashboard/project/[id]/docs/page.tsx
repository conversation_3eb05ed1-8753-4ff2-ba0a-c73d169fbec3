"use client"

import { useEffect, useState, use<PERSON>allback, useRef, useLayoutEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON>, useSearchParams } from "next/navigation" // Import useSearchParams
import { supabase } from "@/lib/supabase-client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card" // CardFooter is used
import { Loader2, ArrowLeft, Edit, Eye, MessageSquare, X } from "lucide-react"
import Link from "next/link"
// import Markdown from "react-markdown" // MarkdownRenderer is used
// import { DashboardShell } from "@/components/dashboard/dashboard-shell"
import { useAuth } from "@/components/auth/auth-provider"
import { ProjectHeader } from "../components/ProjectHeader"
import { Editor } from "@/components/blocks/editor-x/editor"
import { EditorContent, EditorFooter } from "@/components/blocks/editor-x/plugins" // Editor<PERSON><PERSON><PERSON> removed
import { SerializedEditorState } from 'lexical'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
// Import markdown conversion utilities
import { $convertToMarkdownString, TRANSFORMERS } from '@lexical/markdown';
// Import necessary node types
import { TableNode, TableCellNode, TableRowNode } from '@lexical/table';
import { ListNode, ListItemNode } from '@lexical/list';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { CodeNode, CodeHighlightNode } from '@lexical/code';
import { LinkNode } from '@lexical/link';
import { Chat } from "@/components/chat/chat" // Chat is used
import { InlineChatProvider as ChatProvider, useChat, InlineChatSidebar } from "../components/InlineChatProvider"
import { FloatingToolbar } from "../../../../../components/new-editor/components/FloatingToolbar"; // Corrected path
// Default editor state
const DEFAULT_EDITOR_STATE: SerializedEditorState = {
  root: {
    children: [
      {
        children: [
          {
            detail: 0,
            format: 0,
            mode: 'normal',
            style: '',
            text: '',
            type: 'text',
            version: 1,
          },
        ],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'paragraph',
        version: 1,
      },
    ],
    direction: 'ltr',
    format: '',
    indent: 0,
    type: 'root',
    version: 1,
  },
}

// Wrapper component with ChatProvider
function ProjectDocumentsPageWithChat() {
  const title = "Project Documents Chat" // You can customize this
  return (
    <ChatProvider predefinedChatTitle={title}>
      <ProjectDocumentsPageContent />
    </ChatProvider>
  )
}

function ProjectDocumentsPageContent() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams() // Initialize useSearchParams
  const projectId = params.id as string
  const [documents, setDocuments] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [activeDocument, setActiveDocument] = useState<string | null>(null)
  const [project, setProject] = useState<any>(null)
  const [isEditingName, setIsEditingName] = useState(false)
  const [tempProjectName, setTempProjectName] = useState("")
  const [error, setError] = useState<string | null>(null)
  // const [isEditMode, setIsEditMode] = useState(true) // Default to Edit Mode
  const [editorState, setEditorState] = useState<string | SerializedEditorState>(DEFAULT_EDITOR_STATE)
  const contentRef = useRef<HTMLDivElement>(null)
  const [floatingAnchorElem, setFloatingAnchorElem] = useState<HTMLDivElement | null>(null)
  // Add this state to track document changes
  const [editorKey, setEditorKey] = useState(`editor-${activeDocument}`);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [originalPathname, setOriginalPathname] = useState("");

  // Get chat context from ChatProvider
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit, // Use handleSubmit directly from the provider
    chatLoading, // Assuming this is the correct name for chat loading state
    stop,
    append: originalAppend,
    setMessages,
    selectedModel,
    setSelectedModel, // This is the setter for the selected model
    availableModels, // Assuming your useChat hook provides this
    setSelectedProject,
    documentTypes, // Assuming documentTypes is available from useChat context
    isLoading: chatComponentIsLoading, 
    setInput
  } = useChat();

  // Store the original pathname and add history listener
  useEffect(() => {
    setOriginalPathname(window.location.pathname);
    // More robust navigation prevention can be added here if needed,
    // similar to doc3/page.tsx, focusing on preventing navigation
    // initiated by the chat component if it tries to change routes.
    // For now, we'll rely on the chat component being well-behaved
    // or override its submission logic.
  }, []);

  const onContentRef = useCallback((node: HTMLDivElement) => {
    if (node !== null) {
      contentRef.current = node;
      setFloatingAnchorElem(node);
    }
  }, []);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        
        if (!projectId || !user?.id) {
          setError("Missing project ID or user information")
          setLoading(false)
          return
        }
        
        // Fetch project details
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .eq('user_id', user?.id)
          .single()
          
        if (projectError) {
          console.error("Project fetch error details:", projectError)
          throw new Error(projectError.message || "Failed to fetch project details")
        }
        
        if (!projectData) {
          setError("Project not found or you don't have access to it")
          setLoading(false)
          return
        }
        
        setProject(projectData)
        setTempProjectName(projectData.name)
        
        // Fetch documents
        const { data: documentsData, error: documentsError } = await supabase
          .from('project_documents')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: false })

        if (documentsError) {
          console.error("Documents fetch error details:", documentsError)
          throw new Error(documentsError.message || "Failed to fetch project documents")
        }
        
        setDocuments(documentsData || [])
        
        const queryDocId = searchParams.get('selectedDocId'); // Get docId from query
        
        if (documentsData && documentsData.length > 0) {
          if (queryDocId && documentsData.some(doc => doc.id === queryDocId)) {
            setActiveDocument(queryDocId); // Set from query if valid
          } else {
            setActiveDocument(documentsData[0].id); // Fallback to first document
          }
        }
      } catch (error: any) {
        console.error('Error fetching data:', error)
        setError(error?.message || 'An unexpected error occurred while fetching data. Please check the console for details.')
      } finally {
        setLoading(false)
      }
    }

    if (projectId && user) {
      fetchData()
    }
  }, [projectId, user, searchParams]) // Add searchParams to dependency array

  // Set the project context for chat when chat is opened
  useEffect(() => {
    if (isChatOpen && projectId && project) {
      setSelectedProject(projectId);
    }
  }, [isChatOpen, projectId, project, setSelectedProject]);


  // Handle project name update
  const handleProjectNameUpdate = async () => {
    if (!project || tempProjectName.trim() === "") return
    
    try {
      const { error } = await supabase
        .from("projects")
        .update({ 
          name: tempProjectName,
          updated_at: new Date().toISOString()
        })
        .eq("id", project.id)
      
      if (error) throw error
      
      // Update local state
      setProject({
        ...project,
        name: tempProjectName,
        updated_at: new Date().toISOString()
      })
      setIsEditingName(false)
    } catch (err) {
      console.error("Error updating project name:", err)
    }
  }

  const getDocumentById = (id: string) => {
    return documents.find(doc => doc.id === id)
  }

  // Save document content - updated to save as markdown string
  const saveDocumentContent = async (currentEditorContent: string | SerializedEditorState) => {
    if (!activeDocument) return;

    try {
      let markdownContentToSave: string | undefined;

      if (typeof currentEditorContent === 'string') {
        markdownContentToSave = currentEditorContent; // Already in Markdown format
      } else {
        // Convert SerializedEditorState to Markdown string
        const { createEditor } = await import('lexical');
        const tempEditor = createEditor({
          namespace: 'tempMarkdownConversion',
          onError: (error) => {
            console.error("Markdown conversion error:", error);
          },
          nodes: [ // Ensure all relevant nodes are included for conversion
            TableNode, TableCellNode, TableRowNode,
            ListNode, ListItemNode,
            HeadingNode, QuoteNode,
            CodeNode, CodeHighlightNode,
            LinkNode,
            // Add any other custom nodes your editor uses if they need specific markdown transformers
          ]
        });

        // Ensure currentEditorContent is a valid SerializedEditorState object
        if (currentEditorContent && typeof currentEditorContent === 'object' && currentEditorContent.root) {
          tempEditor.setEditorState(tempEditor.parseEditorState(currentEditorContent));
          tempEditor.update(() => {
            markdownContentToSave = $convertToMarkdownString(TRANSFORMERS);
          });
        } else {
          console.error("Invalid SerializedEditorState provided for Markdown conversion.");
          return;
        }
      }

      if (markdownContentToSave === undefined) {
        console.error("Failed to prepare content for saving. Content is undefined.");
        return;
      }

      // Save the markdown string instead of the full editor state
      const { error } = await supabase
        .from('project_documents')
        .update({ 
          content: markdownContentToSave, // Save as markdown string
          updated_at: new Date().toISOString()
        })
        .eq('id', activeDocument);

      if (error) throw error;
      
      // Update local state
      setDocuments(documents.map(doc => 
        doc.id === activeDocument 
          ? {...doc, content: contentToSave, updated_at: new Date().toISOString()} 
          : doc
      )); // Ensure contentToSave is used here if it's the definitive markdown string
      
      console.log("Document saved as markdown");
    } catch (err) {
      console.error("Error saving document content:", err);
    }
  };

  // Update editor state when active document changes
  useEffect(() => {
    if (activeDocument) {
      // Update the editor key to force remount
      setEditorKey(`editor-${activeDocument}-${Date.now()}`);

      const doc = getDocumentById(activeDocument);
      if (doc) {
        let newEditorState: string | SerializedEditorState = DEFAULT_EDITOR_STATE;
        if (doc.content != null) {
          if (typeof doc.content === 'string') {
            try {
              const parsed = JSON.parse(doc.content);
              if (parsed.root && typeof parsed.root === 'object') {
                newEditorState = parsed as SerializedEditorState;
              } else {
                newEditorState = doc.content;
              }
            } catch {
              newEditorState = doc.content;
            }
          } else if (typeof doc.content === 'object' && doc.content.root) {
            newEditorState = doc.content as SerializedEditorState;
          }
        }
        setEditorState(newEditorState);

        // Reset scroll position after editor state is updated
        if (contentRef.current) {
          setTimeout(() => {
            if (contentRef.current) {
              contentRef.current.scrollTop = 0;
            }
          }, 0);
        }
      }
    }
  }, [activeDocument, documents]);

  // Add this effect to handle scrolling

  if (loading) {
    return (
      <div className="space-y-1 w-full min-h-screen p-1 md:p-1 lg:p-1">
        <div className="flex h-[50vh] items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-1 w-full min-h-screen p-1 md:p-1 lg:p-1">
        <Card>
          <CardContent className="flex h-[300px] flex-col items-center justify-center p-6">
            <p className="text-center text-muted-foreground">{error}</p>
            <Button variant="outline" className="mt-4" onClick={() => router.back()}>
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (documents.length === 0) {
    return (
      <div className=" w-full min-h-screen p-1 md:p-1 lg:p-1">
        {project && (
          <ProjectHeader
            projectName={project.name}
            isEditingName={isEditingName}
            tempProjectName={tempProjectName}
            setTempProjectName={setTempProjectName}
            handleProjectNameUpdate={handleProjectNameUpdate}
            setIsEditingName={setIsEditingName}
            createdAt={project.created_at ? new Date(project.created_at) : null}
            updatedAt={project.updated_at ? new Date(project.updated_at) : null}
          />
        )}
        
        <Card>
          <CardHeader>
            <CardTitle>No Documents Found</CardTitle>
            <CardDescription>
              This project doesn't have any generated documents yet.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Go back to the project and generate documents from the "Create" flow.</p>
          </CardContent>
          <CardFooter>
            <Link href={`/dashboard/project/${projectId}`}>
              <Button>Generate Documents</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="w-full min-h-screen p-1 md:p-1 lg:p-1"> {/* Removed space-y-1, DashboardShell provides its own spacing/layout */}

      <div className={`flex flex-col h-screen gap-1 transition-all duration-300 ${isChatOpen ? "mr-[400px]" : ""}`}> {/* Adjusted margin for fixed sidebar width */}
      {project && (
        <ProjectHeader
          projectName={project.name}
          isEditingName={isEditingName}
          tempProjectName={tempProjectName}
          setTempProjectName={setTempProjectName}
          handleProjectNameUpdate={handleProjectNameUpdate}
          setIsEditingName={setIsEditingName}
          createdAt={project.created_at ? new Date(project.created_at) : null}
          updatedAt={project.updated_at ? new Date(project.updated_at) : null}
          showTrailingChevron={false}
        >
          <div className="flex flex-wrap items-center ml-auto gap-2 min-w-0">
            <Select value={activeDocument || ""} onValueChange={setActiveDocument}>
              <SelectTrigger className="w-[160px] h-8 flex-shrink truncate">
                <SelectValue placeholder="Select a document" />
              </SelectTrigger>
              <SelectContent>
                {documents.map(doc => (
                  <SelectItem key={doc.id} value={doc.id}>
                    {doc.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {/* Button made responsive: text hides on smaller viewports if space is tight */}
            <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/project/${projectId}`)} className="flex-shrink-0 truncate max-w-[120px]">
              <ArrowLeft className="h-4 w-4 sm:mr-2" />
              {/* <span className="hidden sm:inline">Back</span> */}
            </Button>
          </div>
        </ProjectHeader>
      )}

      {activeDocument && (
        <Card className="shadow-sm flex-1 min-h-0">
          <CardContent className="p-0 flex flex-col h-full">
            <div className="flex gap-0 flex-1 min-h-0">
              <main className="flex flex-1 flex-col min-h-0">
                <Editor
                  key={editorKey}
                  editorSerializedState={editorState}
                  onSerializedChange={(newState) => {
                    setEditorState(newState)
                  }}
                  editable={true}
                >
                  {/* Top bar with Save button */}
                  <div className="flex-shrink-0 border-b bg-card">
                    <div className="flex justify-end items-center p-2"> {/* Adjusted for only save button */}
                      <Button
                        onClick={() => saveDocumentContent(editorState)}
                        disabled={!activeDocument}
                        size="sm"
                      >
                        Save
                      </Button>
                    </div>
                  </div>

                  {/* Scrollable Content Area */}
                  <div className="flex-1 overflow-y-auto bg-card relative min-h-0" ref={onContentRef}>
                    <EditorContent
                      placeholder={`Enter content for ${getDocumentById(activeDocument)?.title || 'document'}... Press / for commands.`}
                      floatingAnchorElem={floatingAnchorElem}
                      enableHeadings={true}
                      enableLists={true}
                      enableQuote={true}
                      enableCode={true}
                      enableFormatting={true}
                      enableAlignment={true}
                      enableIndentation={true}
                      enableLink={true}
                      enableTable={true}
                    />
                  </div>
                  {/* Floating Toolbar for text formatting - self-positioning */}
                  <FloatingToolbar />
                </Editor>
              </main>
            </div>
          </CardContent>
        </Card>
      )}

      </div>

      {/* Floating Chat Button */}
      {!isChatOpen && (
        <div className="fixed bottom-6 right-6 z-[100]"> {/* Ensure high z-index for button when sidebar is closed */}
          <Button
            size="lg"
            className="rounded-full h-14 w-14 shadow-lg"
            onClick={() => setIsChatOpen(true)}
          >
            <MessageSquare className="h-6 w-6" />
          </Button>
        </div>
      )}

      {/* Inline Chat Sidebar */}
      <InlineChatSidebar isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
    </div>
  )
}

export default ProjectDocumentsPageWithChat;
