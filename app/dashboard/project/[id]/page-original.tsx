"use client";

import { InlineChatProvider as Cha<PERSON><PERSON><PERSON><PERSON>, useChat, InlineChatSidebar } from "./components/InlineChatProvider";
import { MessageSquare } from "lucide-react";


import { useState, useRef, useEffect } from "react";
import { useRouter, usePara<PERSON>, useSearchParams } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/auth/auth-provider";
import { supabase } from "@/lib/supabase-client";
// Removed Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, Progress from here as they are now in ProjectHeader
import { Button } from "@/components/ui/button"; // Keep Button if used elsewhere
import { ArrowDown, ArrowLeft, CheckCircle2, Loader2, AlertCircle, ChevronRight, Slash, FileText } from "lucide-react"; // Removed Pencil, Check, X as they are in ProjectHeader
// Removed format from here as it's in ProjectHeader
//import { Accordion, AccordionContent, AccordionItem, AccordionTrigger,} from "@/components/ui/accordion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"; // Added Alert components
import { Textarea } from "@/components/ui/textarea";
import { CompletedStepsAccordion } from './components/CompletedStepsAccordion'; // Import the new accordion component
import { format } from "date-fns";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ExternalLink, FolderOpen } from "lucide-react";

// Import step components
import Step1 from "./steps/Step1";
import Step2 from "./steps/Step2";
import Step3 from "./steps/Step3";
import Step4 from "./steps/Step4";
// Removed Step5 import as it's handled by CurrentStepDisplay
import { Step1Ref } from "./steps/Step1";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useProjectStore } from '@/lib/store/project';
import { extractProductDetails } from '../utils/extractProductDetails';
import LoadingComponent from "./loading"; // Import the loading component
import { ProjectHeader } from './components/ProjectHeader'; // Import the new header component
import { CurrentStepDisplay } from './components/CurrentStepDisplay'; // Import the new step display component

// Import new dialog components
import { GoogleDriveExportSuccessDialog } from './components/dialogs/GoogleDriveExportSuccessDialog';
import { NotionExportSuccessDialog } from './components/dialogs/NotionExportSuccessDialog';
import { NotionExplanationDialog } from './components/dialogs/NotionExplanationDialog';
import { GoogleDriveExplanationDialog } from './components/dialogs/GoogleDriveExplanationDialog';

// Define the form schema for the idea
const ideaSchema = z.object({
  idea: z.string().min(10, "Idea must be at least 10 characters"),
});

type IdeaFormData = z.infer<typeof ideaSchema>;


function ProjectPageContent() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const { user } = useAuth();
  const searchParams = useSearchParams();

  // --- Zustand Store Access with Shallow Comparison ---
  // Get specific state values and actions with stable references
  const {
    projectId: storeProjectId,
    projectName,
    isTestUser,
    createdAt,
    updatedAt,
    idea,
    productDetails,
    refinedIdea: storeRefinedIdea
  } = useProjectStore((state) => ({
    projectId: state.projectId,
    projectName: state.projectName,
    isTestUser: state.isTestUser,
    createdAt: state.createdAt,
    updatedAt: state.updatedAt,
    idea: state.idea,
    productDetails: state.productDetails,
    refinedIdea: state.refinedIdea
  }), (a, b) => JSON.stringify(a) === JSON.stringify(b));

  // Actions (these don't change so can be separate)
  const setProjectId = useProjectStore((state) => state.setProjectId);
  const setProjectName = useProjectStore((state) => state.setProjectName);
  const setCreatedAt = useProjectStore((state) => state.setCreatedAt);
  const setUpdatedAt = useProjectStore((state) => state.setUpdatedAt);
  const setIdea = useProjectStore((state) => state.setIdea);
  const setRefinedIdea = useProjectStore((state) => state.setRefinedIdea);
  const setSelectedToolsInStore = useProjectStore((state) => state.setSelectedTools);
  const setProjectPlanInStore = useProjectStore((state) => state.setProjectPlan);
  const setClarifyingQuestions = useProjectStore((state) => state.setClarifyingQuestions);
  const setProductDetails = useProjectStore((state) => state.setProductDetails);
  const setVoiceNoteUrlInStore = useProjectStore((state) => state.setVoiceNoteUrl);
  const setIsTestUser = useProjectStore((state) => state.setIsTestUser);
  const updateProjectInStore = useProjectStore((state) => state.updateProject);
  const markProjectAsCompleted = useProjectStore((state) => state.markProjectAsCompleted);

  // --- Local Component State ---
  // Chat sidebar open state
  const [isChatOpen, setIsChatOpen] = useState(false);

  // Initialize chat context if needed
  const {
    setSelectedProject, // from useChat context
  } = useChat();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [projectStatus, setProjectStatus] = useState<"Draft" | "In Progress" | "Completed">("Draft");
  const [step1Complete, setStep1Complete] = useState(false);
  const [step2Complete, setStep2Complete] = useState(false);
  const [step3Complete, setStep3Complete] = useState(false);
  const [step4Complete, setStep4Complete] = useState(false);
  const [currentDisplayStep, setCurrentDisplayStep] = useState(1);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [projectPlan, setProjectPlan] = useState<string>("");
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [voiceNoteUrl, setVoiceNoteUrl] = useState<string | null>(null);
  const [isEditingName, setIsEditingName] = useState(false);
  const [tempProjectName, setTempProjectName] = useState("");
  const [problemsComplete, setProblemsComplete] = useState(false);
  const [audienceComplete, setAudienceComplete] = useState(false);
  const [featuresComplete, setFeaturesComplete] = useState(false);
  const [techComplete, setTechComplete] = useState(false);
  const [localProductDetails, setLocalProductDetails] = useState({});
  const [activeStep, setActiveStep] = useState<string>("step-1");
  const [isEditMode, setIsEditMode] = useState(false);
  // Add the isAccordionOpen state with default based on project status
  const [isAccordionOpen, setIsAccordionOpen] = useState(true); // Default to open
  const [documents, setDocuments] = useState<any[]>([]);
  const [activeDocument, setActiveDocument] = useState<string | null>(null);
  const [googleDriveExportSuccessOpen, setGoogleDriveExportSuccessOpen] = useState(false);
  const [exportedFolderUrl, setExportedFolderUrl] = useState("");
  // Add state for Notion export
  const [notionConnected, setNotionConnected] = useState(false);
  const [isExportingNotion, setIsExportingNotion] = useState(false);
  const [notionExportUrl, setNotionExportUrl] = useState<string | null>(null); // Renamed from notionExportUrl
  const [notionExportSuccessOpen, setNotionExportSuccessOpen] = useState(false);
  // Add state for the Notion explanation modal
  const [notionExplanationOpen, setNotionExplanationOpen] = useState(false);
  // Add state for Google Drive connection
  const [googleDriveConnected, setGoogleDriveConnected] = useState(false);
  const [isExportingGoogleDrive, setIsExportingGoogleDrive] = useState(false);
  const [googleDriveExplanationOpen, setGoogleDriveExplanationOpen] = useState(false);

  // State for V0 integration
  const [isOpeningInV0, setIsOpeningInV0] = useState(false);

  const [pageViewMode, setPageViewMode] = useState(false); // false = Edit Mode, true = View Mode
  // --- Refs ---
  const step1Ref = useRef<Step1Ref>(null);
  const step4Ref = useRef<{ generateGeminiPlan: () => Promise<string> }>(null);

  // --- Form ---
  const ideaForm = useForm<IdeaFormData>({
    resolver: zodResolver(ideaSchema),
    defaultValues: {
      idea: "",
    },
  });

  // --- Helper Functions ---

  // Calculate progress percentage based on step completion
  const calculateProgress = () => {
    let progress = 0;
    if (step1Complete) progress += 25;
    if (step2Complete) progress += 25;
    if (step3Complete) progress += 25;
    if (step4Complete) progress += 25;
    return progress;
  };

  // --- Effects ---
  // Load project data on mount or when projectId changes
  useEffect(() => {
    // Get the project ID from the URL parameters
    const projectIdFromParams = params.id as string;

    if (!projectIdFromParams) {
      console.error("No project ID found in URL params.");
      setError("No project ID specified in the URL.");
      setIsLoading(false);
      return;
    }

    // Update store projectId only if it's different
    if (storeProjectId !== projectIdFromParams) {
      console.log(`Updating store projectId from ${storeProjectId} to ${projectIdFromParams}`);
      
      // Instead of resetting the entire store, just set the new project ID
      // and let the data fetch populate the store
      setProjectId(projectIdFromParams);
      
      // Reset local state related to the previous project
      setStep1Complete(false);
      setStep2Complete(false);
      setStep3Complete(false);
      setStep4Complete(false);
      setSelectedTools([]);
      setProjectPlan("");
      setVoiceNoteUrl(null);
      ideaForm.reset({ idea: "" });
      
      console.log("Local state reset for new project");
    }

    // Function to load project data
    const loadProject = async () => {
      setIsLoading(true);
      setError(null);

      if (isTestUser) {
        console.log("Test user detected, skipping data load.");
        setIsLoading(false);
        setTempProjectName("Test Project");
        setProjectName("Test Project");
        return;
      }

      try {
        console.log(`Loading project data for ID: ${projectIdFromParams}`);
        const { data, error: dbError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectIdFromParams)
          .maybeSingle();

        if (dbError) throw dbError;

        if (data) {
          console.log("Project data loaded:", data);
          
          // --- Update Store (using stable action references) ---
          setProjectName(data.name || "Untitled Project");
          setCreatedAt(data.created_at ? new Date(data.created_at) : new Date());
          setUpdatedAt(data.updated_at ? new Date(data.updated_at) : new Date());
          setIdea(data.idea || ''); // Update store idea
          if (data.refined_idea) setRefinedIdea(data.refined_idea);
          if (data.selected_tools) setSelectedToolsInStore(data.selected_tools);
          if (data.project_plan) setProjectPlanInStore(data.project_plan);
          
          // Extract product details
          const productDetailsData = extractProductDetails(data);
          
          // Ensure all expected fields exist
          const completeProductDetails = {
            targetAudience: "",
            keyFeatures: [],
            frontendTech: [],
            backendTech: [],
            usp: [],
            ...productDetailsData
          };
          
          console.log("Setting product details:", completeProductDetails);
          setProductDetails(completeProductDetails);
          
          // --- Update Local State ---
          ideaForm.setValue('idea', data.idea || '');
          setSelectedTools(data.selected_tools || []); // Sync local state
          setProjectPlan(data.project_plan || ''); // Sync local state
          setVoiceNoteUrl(data.voice_note_url || null); // Sync local state
          setTempProjectName(data.name || "Untitled Project");

          // Set Step Completion
          setStep1Complete(!!data.idea);
          setStep2Complete(!!data.product_details && Object.keys(data.product_details).length > 0);
          setStep3Complete(!!data.selected_tools && data.selected_tools.length > 0);
          setStep4Complete(!!data.project_plan);
        } else {
          // Handle case where project doesn't exist
          console.log(`Project with ID ${projectIdFromParams} not found. Creating a new one.`);
          
          // Create a new project with basic data
          const { error: createError } = await supabase
            .from('projects')
            .insert({
              id: projectIdFromParams,
              name: "New Project",
              user_id: user?.id,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              status: 'draft'
            });
            
          if (createError) {
            console.error("Error creating new project:", createError);
            throw createError;
          }
          
          setProjectName("New Project");
          setTempProjectName("New Project");
          setCreatedAt(new Date());
          setUpdatedAt(new Date());
        }
      } catch (err) {
        console.error("Error loading project:", err);
        setError(err instanceof Error ? err.message : "Failed to load project");
      } finally {
        setIsLoading(false);
      }
    };

    // Always attempt to load project if we have a param
    if (projectIdFromParams) {
      loadProject();
    }
  }, [
    params.id,
    storeProjectId,
    isTestUser,
    user,
    router,
    toast,
    setProjectId,
    setProjectName,
    setCreatedAt,
    setUpdatedAt,
    setIdea,
    setRefinedIdea,
    setSelectedToolsInStore,
    setProjectPlanInStore,
    setClarifyingQuestions,
    setProductDetails,
    setVoiceNoteUrlInStore,
    ideaForm,
  ]);

  // Check for test user
  useEffect(() => {
    if (user && user.email === "<EMAIL>") {
      setIsTestUser(true);
      console.log("Test user detected.");
    }
  }, [user, setIsTestUser]);

  // Determine current step for rendering
  useEffect(() => {
    // Calculate the appropriate step based on completion status
    let newStep = 1;
    if (step4Complete) newStep = 5;
    else if (step3Complete) newStep = 4;
    else if (step2Complete) newStep = 3;
    else if (step1Complete) newStep = 2;
    
    // Only update if the current step is different
    if (currentDisplayStep !== newStep) {
      console.log(`Auto-updating current step from ${currentDisplayStep} to ${newStep}`);
      setCurrentDisplayStep(newStep);
    }
  }, [step1Complete, step2Complete, step3Complete, step4Complete, currentDisplayStep]);

  // Sync tempProjectName when projectName from store changes
  useEffect(() => {
    setTempProjectName(projectName);
  }, [projectName]);

  // Track step completion based on idea form
  useEffect(() => {
    if (ideaForm.getValues("idea") && ideaForm.getValues("idea").trim() !== "") {
      setStep1Complete(true);
    }
  }, [ideaForm]);

  // Fetch documents when project is completed
  useEffect(() => {
    const fetchDocuments = async () => {
      if (!storeProjectId) return;
      
      try {
        const { data, error } = await supabase
          .from('project_documents')
          .select('*')
          .eq('project_id', storeProjectId)
          .order('created_at', { ascending: false });
          
        if (error) throw error;
        
        setDocuments(data || []);
        if (data && data.length > 0) {
          setActiveDocument(data[0].id);
        }
      } catch (err) {
        console.error('Error fetching documents:', err);
      }
    };
    
    if (step4Complete) {
      fetchDocuments();
    }
  }, [storeProjectId, step4Complete]);

  // Determine project status based on step completion
  useEffect(() => {
    if (step4Complete) {
      setProjectStatus("Completed");
    } else if (step1Complete || step2Complete || step3Complete) {
      setProjectStatus("In Progress");
    } else {
      setProjectStatus("Draft");
    }
    
    // Update accordion open state when project status changes
    // Only auto-close for completed projects
    if (projectStatus === "Completed") {
      setIsAccordionOpen(false);
    }
  }, [step1Complete, step2Complete, step3Complete, step4Complete, projectStatus]);

  // Add this effect to check for Notion connection status
  useEffect(() => {
    const checkNotionConnection = async () => {
      if (!user?.id) return;
      
      // First check URL parameter (for redirect after OAuth)
      if (searchParams?.get('notion') === 'connected') {
        console.log("Notion connected parameter detected in URL");
        setNotionConnected(true);
        toast({
          title: "Notion Connected",
          description: "Your Notion account has been successfully connected.",
        });
        return; // Skip DB check if URL parameter is present
      }
      
      // Then check database for existing token
      try {
        const { data, error } = await supabase
          .from('user_oauth_tokens')
          .select('access_token')
          .eq('user_id', user.id)
          .eq('provider', 'notion')
          .maybeSingle();
        
        if (!error && data?.access_token) {
          console.log("Notion token found in database");
          setNotionConnected(true);
        } else {
          console.log("No Notion token found in database");
          setNotionConnected(false);
        }
      } catch (error) {
        console.error("Error checking Notion connection:", error);
      }
    };
    
    checkNotionConnection();
  }, [user, searchParams]); // Include searchParams in dependencies

  // Check Google Drive connection
  useEffect(() => {
    const checkGoogleDriveConnection = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('user_oauth_tokens')
          .select('access_token')
          .eq('user_id', user.id)
          .eq('provider', 'google')
          .maybeSingle();
        
        if (!error && data?.access_token) {
          console.log("Google Drive token found in database");
          setGoogleDriveConnected(true);
        } else {
          console.log("No Google Drive token found in database");
          setGoogleDriveConnected(false);
        }
      } catch (error) {
        console.error("Error checking Google Drive connection:", error);
      }
    };
    
    checkGoogleDriveConnection();
  }, [user, searchParams]); // Include searchParams to refresh after OAuth redirect

  // --- Handlers ---
  // Wrapper for navigation between steps
  const navigateToStep = async (targetStep: number) => {
    console.log(`Navigating to step ${targetStep} from current display step ${currentDisplayStep}`);
    
    // If moving backward to edit a previous step, we don't need validation
    if (targetStep < currentDisplayStep) {
      console.log(`Moving backward to edit step ${targetStep}`);
      setIsEditMode(true); // Set edit mode when moving backward
      setCurrentDisplayStep(targetStep);
      return;
    }
    
    // If we're in edit mode and moving forward, we're saving changes
    if (isEditMode && targetStep > currentDisplayStep) {
      console.log(`Saving changes from edit mode and moving to step ${targetStep}`);
      const success = await handleStepComplete(currentDisplayStep);
      if (!success) {
        console.log(`Validation failed for step ${currentDisplayStep}, not navigating`);
        return; // Don't navigate if saving/validation failed
      }
      setIsEditMode(false); // Exit edit mode after saving
      setCurrentDisplayStep(targetStep);
      return;
    }
    
    // Normal forward navigation
    const success = await handleStepComplete(currentDisplayStep);
    if (!success) {
      console.log(`Validation failed for step ${currentDisplayStep}, not navigating`);
      return; // Don't navigate if saving/validation failed
    }
    
    // Set the current step
    setCurrentDisplayStep(targetStep);
  };

  // Step completion handler
  const handleStepComplete = async (stepNumber: number) => {
    try {
      // Perform validation if needed for the current step
      if (stepNumber === 1) {
        const ideaValue = ideaForm.getValues("idea");
        if (!ideaValue || ideaValue.length < 10) {
          toast({ title: "Idea must be at least 10 characters", variant: "destructive" });
          return false;
        }
        setStep1Complete(true);
      } else if (stepNumber === 2) {
        // Validate Step 2 data if needed
        setStep2Complete(true);
      } else if (stepNumber === 3) {
        // Validate Step 3 data if needed
        setStep3Complete(true);
      } else if (stepNumber === 4) {
        // Make sure we have a project plan before completing step 4
        if (!projectPlan || projectPlan.trim() === '') {
          toast({ title: "Please generate a project plan first", variant: "destructive" });
          return false;
        }
        setStep4Complete(true);
      }

      // Update the store with the current state
      if (storeProjectId) {
        // Only update the fields that are relevant to the current step
        let updateData = {};
        
        if (stepNumber === 1) {
          updateData = {
            idea: ideaForm.getValues("idea")
          };
        } else if (stepNumber === 3) {
          updateData = {
            selected_tools: selectedTools
          };
        } else if (stepNumber === 4) {
          updateData = {
            project_plan: projectPlan
          };
        }
        
        // Don't update anything for step 2, as it's handled by the Step2 component
        
        if (Object.keys(updateData).length > 0) {
          console.log(`Updating project for step ${stepNumber} with:`, updateData);
          await updateProjectInStore(updateData);
        } else {
          console.log(`No updates needed for step ${stepNumber}`);
        }
      }
      
      console.log(`Step ${stepNumber} completed.`);
      return true;

    } catch (error: any) {
      console.error(`Error completing step ${stepNumber}:`, error);
      toast({
        title: `Failed to save progress for Step ${stepNumber}`,
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  };

  // Handle project name update
  const handleProjectNameUpdate = async () => {
    if (!tempProjectName.trim()) {
      toast({ title: "Project name cannot be empty", variant: "destructive" });
      return;
    }
    const originalName = projectName;
    try {
      setProjectName(tempProjectName);
      setIsEditingName(false);
      
      // Update only the project name in the database
      if (!isTestUser && storeProjectId) {
        try {
          // Update directly with Supabase instead of using the store's updateProject
          const { error } = await supabase
            .from('projects')
            .update({ 
              name: tempProjectName,
              updated_at: new Date().toISOString()
            })
            .eq('id', storeProjectId);
            
          if (error) throw error;
        } catch (dbError) {
          console.error("Database error updating project name:", dbError);
          throw dbError;
        }
      }
      
      toast({ title: "Project name updated successfully" });
    } catch (error: any) {
      console.error("Error updating project name:", error);
      setProjectName(originalName);
      setTempProjectName(originalName);
      toast({
        title: "Failed to update project name",
        description: error.message,
        variant: "destructive"
      });
    }
  };

  // Handle enhanced idea
  const handleEnhancedIdea = async () => {
    const ideaValue = ideaForm.getValues("idea");
    if (!ideaValue || ideaValue.length < 10) {
      toast({ title: "Idea must be at least 10 characters", variant: "destructive" });
      return;
    }
    
    setIsRefining(true);
    try {
      const refineAction = useProjectStore.getState().refineIdea;
      if (refineAction) {
        const enhanced = await refineAction(ideaValue);
        if (enhanced) {
          ideaForm.setValue("idea", enhanced);
          toast({ title: "Idea refined successfully" });
        } else {
          throw new Error("Refining did not return an enhanced idea.");
        }
      } else {
        throw new Error("refineIdea action not found in store.");
      }
    } catch (error: any) {
      console.error("Error enhancing idea:", error);
      toast({
        title: "Failed to enhance idea",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsRefining(false);
    }
  };

  // Handle Step 4 save complete
  const handleStep4SaveComplete = () => {
    setStep4Complete(true);
    navigateToStep(5);
  };

  // Handle problems save
  const handleProblemsSave = async () => {
    console.log("handleProblemsSave called");
    try {
      // Implementation details...
    } catch (error) {
      console.error("Error saving problems:", error);
    }
  };

  // Handle Google Drive export
  const handleExportToGoogleDrive = async () => {
    if (!documents || documents.length === 0) {
      toast({
        title: "No documents to export",
        description: "This project doesn't have any documents to export.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: "Preparing export...",
        description: "Getting your documents ready for Google Drive.",
      });

      setIsExportingGoogleDrive(true);

      // Call the API to export to Google Drive
      const response = await fetch(`/api/projects/${storeProjectId}/export-to-drive`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          documentIds: documents.map(doc => doc.id),
          projectName: projectName
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        // Check if we need to authenticate with Google
        if (response.status === 401 && data.needsAuth) {
          toast({
            title: "Google Drive Authorization Required",
            description: (
              <div className="space-y-2">
                <p>{data.message || "You need to connect your Google account to export documents."}</p>
                <Button 
                  variant="outline" 
                  className="w-full mt-2"
                  onClick={() => {
                    // Store the current URL to redirect back after auth
                    localStorage.setItem("googleAuthRedirectUrl", window.location.href);
                    // Redirect to Google auth
                    window.location.href = data.authUrl || '/api/auth/google';
                  }}
                >
                  <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 0C5.372 0 0 5.373 0 12s5.372 12 12 12c6.627 0 12-5.373 12-12S18.627 0 12 0zm.14 19.018c-3.868 0-7-3.14-7-7.018 0-3.878 3.132-7.018 7-7.018 1.89 0 3.47.697 4.682 1.829l-1.974 1.978c-.532-.511-1.467-1.102-2.708-1.102-2.31 0-4.187 1.956-4.187 4.313 0 2.357 1.877 4.313 4.187 4.313 2.688 0 3.7-1.942 3.858-2.937h-3.858v-2.591h6.428c.064.343.1.686.1 1.01 0 3.937-2.595 6.223-6.528 6.223z" fill="currentColor"/>
                  </svg>
                  Connect Google Drive
                </Button>
              </div>
            ),
            duration: 10000, // Show for 10 seconds
          });
          return;
        } else if (data.loginUrl) {
          // User needs to log in first
          router.push(data.loginUrl);
          return;
        }
        
        throw new Error(data.error || "Failed to export to Google Drive");
      }
      
      // Show success toast with links to both folders
      toast({
        title: "Export successful! 🎉",
        description: (
          <div className="space-y-3">
            <p>Your documents have been exported to Google Drive.</p>
            {data.folderUrl && (
              <a 
                href={data.folderUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2 p-2 mt-2 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors"
              >
                <img 
                  src="https://fonts.gstatic.com/s/i/productlogos/drive_2020q4/v8/web-64dp/logo_drive_2020q4_color_2x_web_64dp.png" 
                  alt="Google Drive" 
                  className="h-5 w-5" 
                />
                <span>Open in Google Drive</span>
                <ExternalLink className="h-4 w-4 ml-auto" />
              </a>
            )}
          </div>
        ),
        duration: 10000, // Show for 10 seconds
      });

      // Optional: Also show a dialog for more prominence
      if (data.folderUrl) {
        setExportedFolderUrl(data.folderUrl); // This state is used by the dialog
        setGoogleDriveExportSuccessOpen(true);
      }

      return data;
    } catch (error) {
      console.error("Error exporting to Google Drive:", error);
      toast({
        title: "Export failed",
        description: error instanceof Error ? error.message : "Failed to export to Google Drive",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsExportingGoogleDrive(false);
    }
  };

  // Add Notion export handler
  const handleExportToNotion = async () => {
    if (!documents || documents.length === 0) {
      toast({
        title: "No documents to export",
        description: "This project doesn't have any documents to export.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: "Preparing export to Notion...",
        description: "Getting your documents ready for Notion.",
      });

      setIsExportingNotion(true);

      // Call the API to export to Notion
      const response = await fetch(`/api/projects/${storeProjectId}/export-to-notion`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          documentIds: documents.map(doc => doc.id),
          projectName: projectName
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        // Check if we need to authenticate with Notion
        if (response.status === 401 && data.needsAuth) {
          toast({
            title: "Notion Authorization Required",
            description: (
              <div className="space-y-2">
                <p>{data.message || "You need to connect your Notion account to export documents."}</p>
                <Button 
                  variant="outline" 
                  className="w-full mt-2"
                  onClick={() => {
                    // Redirect to profile page to set up Notion
                    router.push(data.authUrl || '/dashboard/profile?setup=notion');
                  }}
                >
                  <img 
                    src="https://logo.clearbit.com/notion.so" 
                    alt="Notion" 
                    className="h-4 w-4 mr-2" 
                  />
                  Connect Notion
                </Button>
              </div>
            ),
            duration: 10000, // Show for 10 seconds
          });
          return;
        } else if (data.loginUrl) {
          // User needs to log in first
          router.push(data.loginUrl);
          return;
        }
        
        throw new Error(data.error || "Failed to export to Notion");
      }
      
      // Show success toast
      toast({
        title: "Export to Notion successful! 🎉",
        description: (
          <div className="space-y-3">
            <p>Your documents have been exported to Notion.</p>
            {data.pageUrl && (
              <a 
                href={data.pageUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-2 p-2 mt-2 bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors"
              >
                <img 
                  src="https://logo.clearbit.com/notion.so" 
                  alt="Notion" 
                  className="h-5 w-5" 
                />
                <span>Open in Notion</span>
                <ExternalLink className="h-4 w-4 ml-auto" />
              </a>
            )}
          </div>
        ),
        duration: 10000, // Show for 10 seconds
      });

      // Show dialog for more prominence
      if (data.pageUrl) {
        setNotionExportUrl(data.pageUrl); // This state is used by the dialog
        setNotionExportSuccessOpen(true);
      }

      return data;
    } catch (error) {
      console.error("Error exporting to Notion:", error);
      toast({
        title: "Export to Notion failed",
        description: error instanceof Error ? error.message : "Failed to export to Notion",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsExportingNotion(false);
    }
  };

  const handleNotionAction = () => {
    console.log("handleNotionAction called, notionConnected:", notionConnected);
    
    if (!notionConnected) {
      // Show explanation modal first instead of redirecting immediately
      setNotionExplanationOpen(true);
      return;
    }
    
    // If connected, export to Notion
    console.log("Notion connected, exporting to Notion");
    handleExportToNotion();
  };

  // Handle Google Drive action (connect or export)
  const handleGoogleDriveAction = () => {
    console.log("handleGoogleDriveAction called, googleDriveConnected:", googleDriveConnected);
    
    if (!googleDriveConnected) {
      // Show explanation modal first instead of redirecting immediately
      setGoogleDriveExplanationOpen(true);
      return;
    }
    
    // If connected, export to Google Drive
    console.log("Google Drive connected, exporting to Google Drive");
    handleExportToGoogleDrive();
  };

  const handleV0Action = () => {
    if (!storeProjectId) {
      toast({
        title: "Project ID Missing",
        description: "Cannot open in v0 without a project ID.",
        variant: "destructive",
      });
      return;
    }
    // Server-side redirect will handle opening v0
    window.open(`/api/projects/${storeProjectId}/open-in-v0`, '_blank');
  };
  // --- Render ---
  if (error) {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-1 text-center">
          <Alert variant="destructive" className="max-w-lg w-full">
            <AlertCircle className="h-5 w-5" />
            <AlertTitle className="text-lg font-semibold">Error Loading Project</AlertTitle>
            <AlertDescription className="mt-2">{error}</AlertDescription>
          </Alert>
          <Button variant="outline" onClick={() => router.push("/dashboard")} className="mt-6">
            Go to Dashboard
          </Button>
        </div>
        {!isChatOpen && (
          <div className="fixed bottom-6 right-6 z-[100]">
            <button
              className="rounded-full h-14 w-14 shadow-lg bg-gradient-to-br from-purple-400 to-blue-200 flex items-center justify-center"
              onClick={() => {
                setIsChatOpen(true);
                setSelectedProject(storeProjectId);
              }}
            >
              <MessageSquare className="h-6 w-6" />
            </button>
          </div>
        )}
        <InlineChatSidebar isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
      </>
    );
  }

  return (
    <>
      {/* This root div for ProjectPageContent should fill the DashboardShell.
          DashboardShell is flex flex-col flex-1, so this should be h-full. */}
      <div className="flex flex-col h-full w-full p-0">
        {/* This inner div organizes the header and the main content area. It also needs to be h-full. */}
        <div className={`flex flex-col h-full gap-0 transition-all duration-300 ${isChatOpen ? "mr-[400px]" : ""}`}>
          {/* ProjectHeader component */}
          <div className="flex-none">
            <ProjectHeader
              projectName={projectName}
              isEditingName={isEditingName}
              tempProjectName={tempProjectName}
              setTempProjectName={setTempProjectName}
              handleProjectNameUpdate={handleProjectNameUpdate}
              setIsEditingName={setIsEditingName}
              createdAt={createdAt}
              updatedAt={updatedAt}
              projectStatus={step4Complete ? "Completed" : "Draft"}
              onActionSelect={(action) => {
                console.log("Selected action:", action);
                if (action === "edit") {
                  setIsEditingName(true);
                } else if (action === "duplicate") {
                  console.log("Duplicate project triggered");
                } else if (action === "delete") {
                  console.log("Delete project triggered");
                }
              }}
              notionConnected={notionConnected}
              onNotionAction={handleNotionAction}
              isExportingNotion={isExportingNotion}
              onOpenEditor={() => {
                router.push(`/dashboard/project/${storeProjectId}`);
              }}
              documents={documents || []}
              accordionTrigger={
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Input Summary</span>
                  <span className="text-muted-foreground text-xs">
                    ({step1Complete + step2Complete + step3Complete + step4Complete} completed steps)
                  </span>
                </div>
              }
              accordionContent={
                <CompletedStepsAccordion
                  step1Complete={step1Complete}
                  step2Complete={step2Complete}
                  step3Complete={step3Complete}
                  step4Complete={step4Complete}
                  currentDisplayStep={currentDisplayStep}
                  refinedIdea={idea || ideaForm.getValues().idea || ""}
                  productDetails={productDetails}
                  codingTools={{
                    selectedTools: selectedTools || []
                  }}
                  projectPlan={projectPlan}
                  navigateToStep={(step) => {
                    if (step < currentDisplayStep) {
                      setIsEditMode(true);
                    }
                    setCurrentDisplayStep(step);
                  }}
                  setIsEditMode={setIsEditMode}
                  projectId={params.id as string}
                  ideaText={ideaForm.getValues().idea || ""}
                  isTestUser={isTestUser}
                  calculateProgress={calculateProgress}
                />
              }
              isAccordionOpen={isAccordionOpen}
              toggleAccordion={() => setIsAccordionOpen(!isAccordionOpen)}
              documents={documents}
              onDocumentSelect={(docId) => { // When on Step 5 (document generation/viewing step),
                // selecting a document from the header should update the main project URL
                // with the selectedDocId. Step5.tsx will pick this up.
                // This also works for other steps if document selection is desired there.
                router.push(`/dashboard/project/${storeProjectId}?selectedDocId=${docId}`); }}
              googleDriveConnected={googleDriveConnected}
              onGoogleDriveAction={handleGoogleDriveAction}
              isExportingGoogleDrive={isExportingGoogleDrive}
              notionConnected={notionConnected}
              onNotionAction={handleNotionAction}
              isExportingNotion={isExportingNotion}
              pageViewMode={pageViewMode}
              setPageViewMode={setPageViewMode}
              onV0Action={handleV0Action}
            />
          </div>
          {/* Main content area for the CURRENT step */}
          {/* This div takes the remaining space after the header and handles its own scrolling. min-h-0 is crucial for flex children. */}
          <div className="flex-1 overflow-y-auto min-h-0">
            {(() => {
              let extraStepProps: Record<string, any> = {};
              if (currentDisplayStep !== 5) {
                extraStepProps = {
                  navigateToStep: navigateToStep,
                  markProjectAsCompleted: async () => {
                    if (storeProjectId) {
                      await markProjectAsCompleted(storeProjectId);
                    } else {
                      console.error("Project ID is missing, cannot mark as completed.");
                    }
                  }
                };
              } else {
                extraStepProps = {
                  navigateToStep: (step: number) => router.push(`/dashboard/project/${storeProjectId}/steps/${step}`),
                };
              }

              if (pageViewMode && currentDisplayStep < 5) {
                // In View Mode for steps 1-4, show the CompletedStepsAccordion or a similar summary
                // For simplicity, we'll reuse the accordion here. You might want a dedicated read-only summary component.
                return (
                  <div className="p-4">
                    <CompletedStepsAccordion
                      step1Complete={step1Complete}
                      step2Complete={step2Complete}
                      step3Complete={step3Complete}
                      step4Complete={step4Complete}
                      currentDisplayStep={currentDisplayStep} // This might be less relevant in a pure view mode
                      refinedIdea={idea || ideaForm.getValues().idea || ""}
                      productDetails={productDetails}
                      codingTools={{ selectedTools: selectedTools || [] }}
                      projectPlan={projectPlan}
                      navigateToStep={(step) => { /* Navigation might be disabled or different in view mode */ }}
                      setIsEditMode={setIsEditMode} // This is the step-specific edit mode
                      projectId={params.id as string}
                      ideaText={ideaForm.getValues().idea || ""}
                      isTestUser={isTestUser}
                      calculateProgress={calculateProgress}
                    />
                  </div>
                );
              } else {
                // In Edit Mode, or for Step 5 (which always shows its content)
                return (
                  <CurrentStepDisplay
                    currentDisplayStep={currentDisplayStep}
                    ideaForm={ideaForm}
                    isRecording={isRecording}
                    toggleRecording={() => setIsRecording(!isRecording)}
                    handleTranscription={(text, audioUrl) => {
                      ideaForm.setValue("idea", text);
                      setVoiceNoteUrl(audioUrl);
                      setVoiceNoteUrlInStore(audioUrl);
                    }}
                    isRefining={useProjectStore.getState().isRefining}
                    handleRefineIdea={handleEnhancedIdea}
                    projectId={storeProjectId}
                    isTestUser={isTestUser}
                    projectName={projectName}
                    onProjectNameChange={setProjectName}
                    clarifyingQuestionsData={useProjectStore.getState().clarifyingQuestions}
                    ideaText={ideaForm.getValues("idea")}
                    selectedTools={selectedTools}
                    onSelectionChange={(newSelection) => { setSelectedTools(newSelection); setSelectedToolsInStore(newSelection); }}
                    projectPlan={projectPlan}
                    setProjectPlan={(plan) => { setProjectPlan(plan); setProjectPlanInStore(plan); }}
                    isGeneratingPlan={isGeneratingPlan}
                    setIsGeneratingPlan={setIsGeneratingPlan}
                    onSaveComplete={handleStep4SaveComplete}
                    user={user}
                    refinedIdea={storeRefinedIdea}
                    isEditMode={isEditMode} // This is the step-specific edit mode for navigating back
                    setIsEditMode={setIsEditMode}
                    markProjectAsCompleted={async () => { if (storeProjectId) await markProjectAsCompleted(storeProjectId); }}
                    // Pass modal control props for Step5 (these are independent of pageViewMode)
                    googleDriveConnectedProp={googleDriveConnected}
                    notionConnectedProp={notionConnected}
                    setGoogleDriveExplanationOpen={setGoogleDriveExplanationOpen}
                    setNotionExplanationOpen={setNotionExplanationOpen}
                    setGoogleDriveExportSuccessOpen={setGoogleDriveExportSuccessOpen}
                    setNotionExportSuccessOpen={setNotionExportSuccessOpen}
                    setExportedFolderUrl={setExportedFolderUrl}
                    setNotionExportUrl={setNotionExportUrl}
                    {...extraStepProps}
                  />
                );
              }
            })()}
          </div>
          {/* Notion explanation modal */}
          <NotionExplanationDialog
            isOpen={notionExplanationOpen}
            onOpenChange={setNotionExplanationOpen}
            onConnect={() => {
              window.location.href = `/api/auth/notion?projectId=${storeProjectId}`;
            }}
          />
          {/* Google Drive Export Success Dialog */}
          <GoogleDriveExportSuccessDialog
            isOpen={googleDriveExportSuccessOpen}
            onOpenChange={setGoogleDriveExportSuccessOpen}
            folderUrl={exportedFolderUrl}
          />

          {/* Notion Export Success Dialog */}
          <NotionExportSuccessDialog
            isOpen={notionExportSuccessOpen}
            onOpenChange={setNotionExportSuccessOpen}
            pageUrl={notionExportUrl}
          />

          {/* Google Drive explanation modal */}
          <GoogleDriveExplanationDialog
            isOpen={googleDriveExplanationOpen}
            onOpenChange={setGoogleDriveExplanationOpen}
            onConnect={() => {
              window.location.href = `/api/auth/google?projectId=${storeProjectId}`;
            }}
          />
        </div>
        {/* Floating Chat Button */}
        {!isChatOpen && (
          <div className="fixed bottom-6 right-6 z-[100]">
            <button
              className="rounded-full h-14 w-14 shadow-lg bg-gradient-to-br from-blue-400 to-blue-200 flex items-center justify-center"
              onClick={() => {
                setIsChatOpen(true);
                setSelectedProject(storeProjectId);
              }}
            >
              <MessageSquare className="h-6 w-6" />
            </button>
          </div>
        )}
        {/* Inline Chat Sidebar */}
        <InlineChatSidebar isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
      </div>
    </>
  );
}

export default function ProjectPage() {
  const projectName = useProjectStore((state) => state.projectName);
  return (
    <ChatProvider predefinedChatTitle={projectName}>
      <ProjectPageContent />
    </ChatProvider>
  );
}
