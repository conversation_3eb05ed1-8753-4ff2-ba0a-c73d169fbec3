// /app/dashboard/project/[id]/steps/step5components/DocumentViewer.tsx
"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Added CardHeader, CardTitle
import { Button } from "@/components/ui/button";
import { Editor } from "@/components/blocks/editor-x/editor"; // EditorToolbar removed from imports
import { EditorContent } from "@/components/blocks/editor-x/plugins"; // EditorToolbar removed
import { Loader2, XCircle, RefreshCw } from "lucide-react";
import { SerializedEditorState, createEditor, $getRoot } from 'lexical'; // Added $getRoot
import { $convertFromMarkdownString, TRANSFORMERS } from '@lexical/markdown'; // Added Lexical Markdown utils
// Import Lexical nodes (ensure these are consistent with Step5.tsx or a shared config)
import { TableNode, TableCellNode, TableRowNode } from '@lexical/table';
import { ListNode, ListItemNode } from '@lexical/list';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { CodeNode, CodeHighlightNode } from '@lexical/code';
import { LinkNode } from '@lexical/link';

import { FloatingToolbar } from "../../../../../../components/new-editor/components/FloatingToolbar"; // Import FloatingToolbar

const LEXICAL_NODES = [TableNode, TableCellNode, TableRowNode, ListNode, ListItemNode, HeadingNode, QuoteNode, CodeNode, CodeHighlightNode, LinkNode];
const DEFAULT_LEXICAL_STATE_VIEWER: SerializedEditorState = { // Renamed to avoid conflict if imported
  root: { children: [{ children: [], direction: null, format: '', indent: 0, type: 'paragraph', version: 1 }], direction: null, format: '', indent: 0, type: 'root', version: 1 },
};

type GenerationStatus = "idle" | "generating" | "done" | "error";

interface DocumentViewerProps {
    activeDocId: string | null;
    activeCustomDoc: any | null;
    docContent: Record<string, SerializedEditorState | null>;
    docStatus: Record<string, GenerationStatus>;
    isLoadingDocLexicalContent: Record<string, boolean>;
    editorViewKey: string;
    onContentRef: (node: HTMLDivElement | null) => void;
    floatingAnchorElem: HTMLDivElement | null; // Added prop for the anchor element
    DEFAULT_LEXICAL_STATE: SerializedEditorState;
    onGenerateDocument: (docId: string) => Promise<void>;
    generationInProgress: boolean;
    onSerializedChange?: (newEditorState: SerializedEditorState) => void; 
    onSaveDocument?: (editorState: SerializedEditorState | null) => Promise<void>; // Updated to accept editor state
    isSaving?: boolean; // Added to manage save button UI state
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
    activeDocId,
    activeCustomDoc,
    docContent,
    docStatus,
    isLoadingDocLexicalContent,
    editorViewKey,
    onContentRef,
    floatingAnchorElem, // Destructure the new prop
    DEFAULT_LEXICAL_STATE,
    onGenerateDocument,
    generationInProgress,
    onSerializedChange,
    onSaveDocument,
    isSaving, // Destructure isSaving
}) => {
    const [customDocLexicalContent, setCustomDocLexicalContent] = useState<SerializedEditorState | null>(null);
    const [isLoadingCustomDoc, setIsLoadingCustomDoc] = useState(false);
    const [customDocEditorKey, setCustomDocEditorKey] = useState('custom-doc-editor-initial');
    const [adjustedDocLexicalContent, setAdjustedDocLexicalContent] = useState<SerializedEditorState | null>(null);
    const [isAdjustingDoc, setIsAdjustingDoc] = useState(false);

    // Helper function to adjust selection in a SerializedEditorState
    const adjustSelectionToStart = useCallback(async (editorState: SerializedEditorState): Promise<SerializedEditorState> => {
        if (!editorState || !editorState.root) return editorState;

        try {
            const tempEditor = createEditor({
                nodes: LEXICAL_NODES,
                onError: (e) => { console.error("Error adjusting selection (DocumentViewer):", e); throw e; }
            });
            const initialEditorState = tempEditor.parseEditorState(editorState);
            
            await tempEditor.update(() => {
                tempEditor.setEditorState(initialEditorState);
                const root = $getRoot();
                if (root.getChildrenSize() > 0) {
                    root.selectStart();
                }
            });
            return tempEditor.getEditorState().toJSON();
        } catch (error) {
            console.error("Failed to adjust selection (DocumentViewer):", error);
            return editorState; // Return original on error
        }
    }, []);
    // Memoized helper function to convert Markdown to Lexical's SerializedEditorState
    const convertMarkdownToLexical = useCallback(async (markdown: string): Promise<SerializedEditorState> => {
        if (!markdown || markdown.trim() === "") return DEFAULT_LEXICAL_STATE_VIEWER;
        try {
            const tempEditor = createEditor({
                nodes: LEXICAL_NODES,
                onError: (e) => { console.error("Markdown to Lexical Conversion Error (DocumentViewer):", e); throw e; }
            });
            await tempEditor.update(() => {
                $convertFromMarkdownString(markdown, TRANSFORMERS);
                // After conversion, set selection to the beginning of the document
                const root = $getRoot();
                if (root.getChildrenSize() > 0) {
                    root.selectStart();
                }
            });
            return tempEditor.getEditorState().toJSON();
        } catch (error) {
            console.error("Error in convertMarkdownToLexical (DocumentViewer):", error);
            return DEFAULT_LEXICAL_STATE_VIEWER; // Fallback on error
        }
    }, []);

    useEffect(() => {
        if (activeCustomDoc) {
            if (typeof activeCustomDoc.content === 'string') {
                setIsLoadingCustomDoc(true);
                convertMarkdownToLexical(activeCustomDoc.content)
                    .then(lexicalState => {
                        setCustomDocLexicalContent(lexicalState);
                        setCustomDocEditorKey(`custom-doc-editor-${activeCustomDoc.id}-${Date.now()}`);
                    })
                    .catch(() => {
                        setCustomDocLexicalContent(null); // Fallback if conversion fails
                    })
                    .finally(() => {
                        setIsLoadingCustomDoc(false);
                    });
            } else if (typeof activeCustomDoc.content === 'object' && activeCustomDoc.content?.root) {
                // Adjust selection for pre-existing Lexical JSON
                setIsLoadingCustomDoc(true);
                adjustSelectionToStart(activeCustomDoc.content as SerializedEditorState)
                    .then(adjustedState => {
                        setCustomDocLexicalContent(adjustedState);
                        setCustomDocEditorKey(`custom-doc-editor-${activeCustomDoc.id}-${Date.now()}`);
                    })
                    .catch(() => {
                        setCustomDocLexicalContent(activeCustomDoc.content as SerializedEditorState); // Fallback
                        setCustomDocEditorKey(`custom-doc-editor-${activeCustomDoc.id}-${Date.now()}`);
                    })
                    .finally(() => {
                        setIsLoadingCustomDoc(false);
                    });
            } else {
                // Content is not a string or recognizable Lexical state, fallback to pre
                setCustomDocLexicalContent(null);
                setIsLoadingCustomDoc(false);
            }
        } else {
            setCustomDocLexicalContent(null); // Clear when no custom doc is active
        }
    }, [activeCustomDoc, convertMarkdownToLexical, adjustSelectionToStart]);

    // New handler to manage editor state changes within DocumentViewer
    // and then propagate to the parent (Step5)
    const handleEditorStateChange = (newEditorState: SerializedEditorState) => {
        if (activeCustomDoc) {
            setCustomDocLexicalContent(newEditorState);
        } else if (activeDocId) {
            // When a generated document is edited, update its adjusted state
            setAdjustedDocLexicalContent(newEditorState);
        }

        // Call the original onSerializedChange from Step5 (if provided)
        // This updates Step5's master docContent state
        if (onSerializedChange) {
            onSerializedChange(newEditorState);
        }
    };

    // Adjust selection for generated documents
    useEffect(() => {
        if (activeDocId && docContent[activeDocId]) {
            setIsAdjustingDoc(true);
            adjustSelectionToStart(docContent[activeDocId] as SerializedEditorState)
                .then(state => {
                    setAdjustedDocLexicalContent(state); // This sets the initial adjusted state
                })
                .catch(() => {
                    setAdjustedDocLexicalContent(docContent[activeDocId] as SerializedEditorState); // Fallback
                })
                .finally(() => {
                    setIsAdjustingDoc(false);
                });
        } else {
            setAdjustedDocLexicalContent(null); // Clear if no active generated doc
        }
    }, [activeDocId, docContent, adjustSelectionToStart]);

    // Effect to scroll to top when custom document editor re-renders (key change)
    useEffect(() => {
        if (activeCustomDoc && customDocLexicalContent && floatingAnchorElem) {
            // Using setTimeout to allow the DOM to update after key change or content update
            setTimeout(() => {
                if (floatingAnchorElem) {
                    floatingAnchorElem.scrollTop = 0;
                }
            }, 0);
        }
    }, [customDocEditorKey, floatingAnchorElem, activeCustomDoc]); // Primarily trigger on key change

    // Effect to scroll to top when generated document editor re-renders (key change)
    useEffect(() => {
        if (activeDocId && !activeCustomDoc && adjustedDocLexicalContent && floatingAnchorElem) {
            setTimeout(() => {
                if (floatingAnchorElem) {
                    floatingAnchorElem.scrollTop = 0;
                }
            }, 0);
        }
    }, [editorViewKey, floatingAnchorElem, activeDocId, activeCustomDoc]); // Primarily trigger on key change

    // Effect to handle Ctrl+S / Cmd+S for saving
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault(); // Prevent browser's default save dialog
                if (onSaveDocument && !isSaving) { // Check if not already saving
                    if (activeCustomDoc && customDocLexicalContent) {
                        onSaveDocument(customDocLexicalContent);
                    } else if (activeDocId && adjustedDocLexicalContent) {
                        onSaveDocument(adjustedDocLexicalContent);
                    }
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);

        // Cleanup the event listener when the component unmounts
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [onSaveDocument, isSaving, activeCustomDoc, customDocLexicalContent, activeDocId, adjustedDocLexicalContent]);

    if (activeCustomDoc) {
        if (isLoadingCustomDoc) {
            return (
                <div className="flex h-full w-full flex-col items-center justify-center p-10 text-center text-sm text-muted-foreground">
                    <Loader2 className="h-8 w-8 animate-spin mb-2" />
                    <p className="font-semibold">Loading Document...</p>
                </div>);
        }
        if (customDocLexicalContent) {
            return (
                <Card className="shadow-sm h-full w-full flex flex-col min-h-0 border-0">
                    <CardContent className="p-0 flex flex-col h-full min-h-0">
                        <Editor
                            key={customDocEditorKey}
                            editorSerializedState={customDocLexicalContent}
                            editable={true}
                            onSerializedChange={handleEditorStateChange}
                        >
                            {/* Editor content area now takes full height, save button will overlay */}
                            <div className="flex-1 overflow-y-auto bg-card relative min-h-0" ref={onContentRef}> {/* Added relative positioning here */}
                                {onSaveDocument && (
                                    <div className="absolute top-2 right-2 z-10"> {/* Container for Save button */}
                                        <Button
                                            onClick={() => {
                                                if (customDocLexicalContent) onSaveDocument(customDocLexicalContent);
                                            }}
                                            size="sm"
                                            disabled={isSaving || !customDocLexicalContent}>
                                            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}Save
                                        </Button>
                                    </div>
                                )}
                                <EditorContent
                                    placeholder="Custom document content..."
                                    floatingAnchorElem={floatingAnchorElem}
                                    enableHeadings={true}
                                    enableLists={true}
                                    enableQuote={true}
                                    enableCode={true}
                                    enableFormatting={true}
                                    enableAlignment={true}
                                    enableIndentation={true}
                                    enableLink={true}
                                    enableTable={true}
                                    spellCheck={false}
                                />
                                {/* Floating Toolbar for text formatting - self-positioning */}
                                <FloatingToolbar />
                            </div>
                        </Editor>
                    </CardContent>
                </Card>
            );
        }
        // Fallback for custom documents if content is not convertible to Lexical
        return (
            <Card className="shadow-sm h-full w-full flex flex-col min-h-0 border-0">
                <CardHeader>
                    <CardTitle>{activeCustomDoc.title || "Custom Document"}</CardTitle>
                </CardHeader>
                <CardContent className="p-6 flex-1 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm">
                        {JSON.stringify(activeCustomDoc.content, null, 2)}
                    </pre>
                </CardContent>
            </Card>
        );
    }

    if (activeDocId && (isLoadingDocLexicalContent[activeDocId] || isAdjustingDoc)) {
        return (
            <div className="flex h-full w-full flex-col items-center justify-center p-10 text-center text-sm text-muted-foreground">
                <Loader2 className="h-8 w-8 animate-spin mb-2" />
                <p className="font-semibold">Loading Document Content...</p>
            </div>
        );
    }

    if (activeDocId && docContent[activeDocId]) {
        if (docStatus[activeDocId] === "generating") {
            return (
                <div className="flex h-full w-full flex-col items-center justify-center p-10 text-center text-sm text-muted-foreground">
                    <Loader2 className="h-8 w-8 animate-spin mb-2" />
                    <p className="font-semibold">Generating Document</p><p className="mb-4">This may take a minute or two...</p>
                </div>);
        }
        if (docStatus[activeDocId] === "error") {
        return (
            <Card className="shadow-sm h-full w-full flex flex-col min-h-0 border-0">
                <CardContent className="p-6 flex-1 overflow-y-auto">
                    <div className="flex h-full w-full flex-col items-center justify-center p-10 text-center text-sm text-muted-foreground">
                        <XCircle className="h-8 w-8 text-red-500 mb-2" /><p className="font-semibold">Error Generating Document</p><p className="mb-4">There was an error.</p>
                        <Button variant="outline" size="sm" onClick={() => onGenerateDocument(activeDocId)} disabled={generationInProgress}><RefreshCw className="mr-1.5 h-4 w-4" /> Try Again</Button>
                    </div>
                </CardContent>
            </Card>);
        }
        return (
            <Card className="shadow-sm h-full w-full flex flex-col min-h-0 border-0">
                <CardContent className="p-0 flex flex-col h-full min-h-0">
                    <Editor
                        key={editorViewKey}
                        editorSerializedState={adjustedDocLexicalContent || DEFAULT_LEXICAL_STATE}
                        editable={true}
                        onSerializedChange={handleEditorStateChange}
                    >
                        {/* Editor content area now takes full height, save button will overlay */}
                        <div className="flex-1 overflow-y-auto bg-card relative min-h-0" ref={onContentRef}> {/* Added relative positioning here */}
                             {onSaveDocument && (
                                <div className="absolute top-2 right-2 z-10"> {/* Container for Save button */}
                                    <Button
                                        onClick={() => {
                                            if (adjustedDocLexicalContent) onSaveDocument(adjustedDocLexicalContent);
                                        }}
                                        size="sm"
                                        disabled={isSaving || !adjustedDocLexicalContent}>
                                        {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}Save
                                    </Button>
                                </div>
                            )}
                            <EditorContent
                                placeholder="Document content..."
                                floatingAnchorElem={floatingAnchorElem} // Ensure this is passed
                                enableHeadings={true}
                                enableLists={true}
                                enableQuote={true}
                                enableCode={true}
                                enableFormatting={true}
                                enableAlignment={true}
                                enableIndentation={true}
                                enableLink={true}
                                enableTable={true}
                                spellCheck={false}
                            />
                        {/* Floating Toolbar for text formatting - self-positioning */}
                        <FloatingToolbar />
                        </div>
                    </Editor>
                </CardContent>
            </Card>);
    }

    return <div className="flex h-full w-full items-center justify-center p-8 text-muted-foreground">{activeDocId ? "Loading..." : "Select or generate a document."}</div>;
};