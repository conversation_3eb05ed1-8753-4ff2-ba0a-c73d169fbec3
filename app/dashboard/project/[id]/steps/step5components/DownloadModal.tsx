// /app/dashboard/project/[id]/steps/step5components/DownloadModal.tsx
"use client";

import React from 'react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface DownloadModalProps {
    isOpen: boolean;
    onOpenChange: (isOpen: boolean) => void;
    selectedDocForDownload: string | null; // Assuming this is the docId
    selectedDownloadFormat: string;
    onFormatSelection: (format: string) => void;
    onModalDownload: () => void;
}

export const DownloadModal: React.FC<DownloadModalProps> = ({
    isOpen,
    onOpenChange,
    // selectedDocForDownload, // Not directly used in this component's UI other than for context
    selectedDownloadFormat,
    onFormatSelection,
    onModalDownload,
}) => {
    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Download Document</DialogTitle>
                    <DialogDescription>Select the format for your download.</DialogDescription>
                </DialogHeader>
                <div className="py-4">
                    <RadioGroup value={selectedDownloadFormat} onValueChange={onFormatSelection}>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="markdown" id="format-markdown" />
                            <Label htmlFor="format-markdown">Markdown (.md)</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="pdf" id="format-pdf" disabled />
                            <Label htmlFor="format-pdf">PDF (.pdf) - Coming Soon</Label>
                        </div>
                    </RadioGroup>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
                    <Button onClick={onModalDownload} disabled={selectedDownloadFormat === "pdf"}>Download</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};