// /app/dashboard/project/[id]/steps/step5components/DocumentSidebar.tsx
"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, TooltipTrigger, TooltipContent, TooltipProvider } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { supabase } from '@/lib/supabase-client';
import {
    ArrowLeft,
    Check,
    Circle,
    Download,
    Loader2,
    LoaderCircle,
    PanelLeftClose,
    PanelLeftOpen,
    XCircle,
    ChevronsLeftRightEllipsis
} from "lucide-react";

export interface UIDocumentType {
    id: string;
    title: string;
    icon: string; // Assuming icon is a string identifier or URL
    cost: number;
    description?: string;
}

type GenerationStatus = "idle" | "generating" | "done" | "error";

interface DocumentSidebarProps {
    isSidebarCollapsed: boolean;
    setIsSidebarCollapsed: (isCollapsed: boolean) => void;
    isLoadingDocTypes: boolean;
    fetchedDocumentTypes: UIDocumentType[];
    customDocuments: any[]; // Documents not matching fetchedDocumentTypes
    activeDocId: string | null;
    activeCustomDoc: any | null;
    docStatus: Record<string, GenerationStatus>;
    generationInProgress: boolean;
    creditsRemaining: number;
    isEditMode: boolean;
    hasGeneratedAnyDocument: boolean;
    isExportingGoogleDrive: boolean;
    googleDriveConnected: boolean;
    isExportingNotion: boolean;
    notionConnected: boolean;
    onDocumentSelect: (item: UIDocumentType | any, isCustom: boolean) => void;
    onGenerateDocument: (docId: string) => Promise<void>;
    onDownloadDocument: (docId: string) => Promise<void>; // For individual downloads
    onDownloadAllDocuments: () => Promise<void>;
    onExportToGoogleDrive: () => Promise<void>;
    onExportToNotion: () => Promise<void>;
    projectId: string;
    allFetchedProjectDocuments: any[];
    toast: (options: any) => void; // Simplified toast prop
}

const StatusIcon = ({ status }: { status: GenerationStatus }) => {
    switch (status) {
        case "done":
            return (
                <span className="h-4 w-4 bg-emerald-600 border-2 border-white rounded-full flex items-center justify-center shrink-0">
                    <Check className="h-3 w-3 text-white" />
                </span>
            );
        case "generating":
            return <Loader2 className="h-4 w-4 animate-spin text-blue-500 shrink-0" />;
        case "error":
            return <XCircle className="h-4 w-4 text-red-600 shrink-0" />;
        default:
            return <Circle className="h-4 w-4 text-muted-foreground shrink-0" />;
    }
};

export const DocumentSidebar: React.FC<DocumentSidebarProps> = ({
    isSidebarCollapsed,
    setIsSidebarCollapsed,
    isLoadingDocTypes,
    fetchedDocumentTypes,
    customDocuments,
    activeDocId,
    activeCustomDoc,
    docStatus,
    generationInProgress,
    // creditsRemaining, // Not used in this component directly, but kept for potential future use
    isEditMode,
    hasGeneratedAnyDocument,
    isExportingGoogleDrive,
    googleDriveConnected,
    isExportingNotion,
    notionConnected,
    onDocumentSelect,
    onGenerateDocument,
    onDownloadDocument,
    onDownloadAllDocuments,
    onExportToGoogleDrive,
    onExportToNotion,
    projectId,
    // allFetchedProjectDocuments, // Used by parent for routing, not directly here
    toast
}) => {
    const [hoveredDocForPopover, setHoveredDocForPopover] = useState<UIDocumentType | null>(null);
    const [popoverStyle, setPopoverStyle] = useState<React.CSSProperties>({});

    const handleDocItemClick = async (doc: UIDocumentType) => {
        if (docStatus[doc.id] === "done") {
            onDocumentSelect(doc, false);
        } else if (docStatus[doc.id] !== "generating") {
            await onGenerateDocument(doc.id);
            // Parent will handle activeDocId and routing after generation
        }
    };

    const handleCustomDocItemClick = (customDoc: any) => {
        onDocumentSelect(customDoc, true);
    };

    return (
        <aside className={cn(
            "shrink-0 flex flex-col transition-all duration-300 p-0", // Removed bg-muted/50 from here
            isSidebarCollapsed ? "w-16" : "w-72"
        )}>
            {/* Apply the background color to the Card component directly */}
            <Card className={cn(
                "h-full rounded-lg border",
                "bg-muted/25" // Added bg-muted/50 here
            )}>
                <div className="flex flex-col h-full">
                    <div className={cn("px-1 pt-4 text-center", isSidebarCollapsed ? "block" : "md:hidden")}>
                        <h3 className="font-semibold text-sm uppercase tracking-wider text-muted-foreground">Docs</h3>
                    </div>

                    {isSidebarCollapsed && (
                        <div className="px-2 py-2 mb-2 text-center">
                            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => setIsSidebarCollapsed(false)} title="Expand sidebar">
                                <PanelLeftOpen className="h-4 w-4" />
                            </Button>
                        </div>
                    )}

                    {!isSidebarCollapsed && (
                        <div className="flex items-center justify-between px-4 py-2">
                            <p className="text-sm font-semibold uppercase ml-2 mt-4 text-muted-foreground">Generate Documents</p>
                            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={() => setIsSidebarCollapsed(true)} title="Collapse sidebar">
                                <PanelLeftClose className="h-4 w-4" />
                            </Button>
                        </div>
                    )}

                    <div className="flex-1 overflow-y-auto px-4 pb-4">
                {isLoadingDocTypes && <div className="flex items-center justify-center p-4"><Loader2 className="h-5 w-5 animate-spin text-muted-foreground" /></div>}
                {!isLoadingDocTypes && fetchedDocumentTypes.length === 0 && <p className={cn("text-xs text-muted-foreground text-center", isSidebarCollapsed ? "hidden" : "block")}>No document types.</p>}
                
                <ul className="space-y-1">
                    {fetchedDocumentTypes.map((doc) => (
                        <li key={doc.id} className="relative"
                            onMouseEnter={(e) => { if (isSidebarCollapsed) { setHoveredDocForPopover(doc); const rect = e.currentTarget.getBoundingClientRect(); setPopoverStyle({ top: `${rect.top}px`, left: `${rect.right + 8}px` }); } }}
                            onMouseLeave={() => { if (isSidebarCollapsed) setHoveredDocForPopover(null); }}>
                            <div className={cn("flex w-full items-center rounded-md text-left text-sm", activeDocId === doc.id ? "bg-accent text-accent-foreground" : "hover:bg-accent/50 hover:text-accent-foreground")}>
                                {isSidebarCollapsed ? (
                                    <Tooltip>
                                        <TooltipTrigger asChild><button onClick={() => handleDocItemClick(doc)} disabled={docStatus[doc.id] === "generating" || generationInProgress} className="w-full flex-1 flex items-center justify-center p-2"><StatusIcon status={docStatus[doc.id]} /></button></TooltipTrigger>
                                        <TooltipContent side="right"><span className="text-sm">{doc.title.replace("Document", "").replace("Diagram", "").replace("Specification", "").trim()}</span></TooltipContent>
                                    </Tooltip>
                                ) : (
                                    <button onClick={() => handleDocItemClick(doc)} disabled={docStatus[doc.id] === "generating" || generationInProgress} className="w-full flex-1 flex items-center gap-2 text-left justify-start p-2 text-sm">
                                        <StatusIcon status={docStatus[doc.id]} />
                                        <span className="flex-1 truncate max-w-[10rem]">{doc.title.replace("Document", "").replace("Diagram", "").replace("Specification", "").trim()}</span>
                                    </button>
                                )}
                                {!isSidebarCollapsed && docStatus[doc.id] === "done" && (
                                    <div className="ml-auto shrink-0 p-1">
                                        <Button variant="ghost" size="icon" className="h-6 w-6 p-1 text-muted-foreground hover:text-accent-foreground" title={`Download ${doc.title}`} onClick={(e) => { e.stopPropagation(); onDownloadDocument(doc.id); }}>
                                            <Download className="h-4 w-4" />
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </li>
                    ))}
                    {customDocuments.map((pd) => (
                         <li key={pd.id}>
                         <div className={cn("flex w-full items-center rounded-md text-left text-sm", activeCustomDoc?.id === pd.id ? "bg-accent text-accent-foreground" : "hover:bg-accent/50 hover:text-accent-foreground")}>
                             {isSidebarCollapsed ? (
                                 <Tooltip>
                                     <TooltipTrigger asChild><button onClick={() => handleCustomDocItemClick(pd)} className="w-full flex-1 flex items-center justify-center p-2"><StatusIcon status={pd.status === 'completed' ? 'done' : 'idle'} /></button></TooltipTrigger>
                                     <TooltipContent side="right"><span className="text-sm">{pd.title || pd.id}</span></TooltipContent>
                                 </Tooltip>
                             ) : (
                                 <button onClick={() => handleCustomDocItemClick(pd)} className="w-full flex-1 flex items-center gap-2 text-left p-2 text-sm">
                                     <StatusIcon status={pd.status === 'completed' ? 'done' : 'idle'} />
                                     <span className="flex-1 truncate max-w-[10rem]">{pd.title || pd.id}</span>
                                 </button>
                             )}
                             {!isSidebarCollapsed && (
                                 <div className="ml-auto shrink-0 p-1">
                                     <Button variant="ghost" size="icon" className="h-6 w-6 p-1 text-muted-foreground hover:text-accent-foreground" onClick={async (e) => { e.stopPropagation(); 
                                        try {
                                            const { data, error } = await supabase.from("project_documents").select("content").eq("id", pd.id).single();
                                            if (error || !data?.content) throw error || new Error("No content");
                                            const blob = new Blob([typeof data.content === "string" ? data.content : JSON.stringify(data.content)],{ type: "text/markdown;charset=utf-8" });
                                            const url = URL.createObjectURL(blob);
                                            const a = document.createElement("a");
                                            const filename = `${pd.title?.replace(/\s+/g, "-").toLowerCase() || pd.id}.md`;
                                            a.href = url; a.download = filename; document.body.appendChild(a); a.click(); document.body.removeChild(a); URL.revokeObjectURL(url);
                                            toast({ title: "Download Started", description: filename });
                                        } catch (err) { console.error("Error downloading custom document:", err); toast({title: "Download Failed",description: "Could not download the document",variant: "destructive"});}
                                     }}>
                                         <Download className="h-4 w-4" />
                                     </Button>
                                 </div>
                             )}
                         </div>
                     </li>
                    ))}
                </ul>

                {!isEditMode && (
                    <div className={cn("mt-4 space-y-2")}>
                        {isSidebarCollapsed ? (
                            <TooltipProvider><Tooltip delayDuration={300}>
                                <TooltipTrigger asChild><Button variant="secondary" size="icon" className="w-full justify-center" onClick={onDownloadAllDocuments} disabled={!hasGeneratedAnyDocument || generationInProgress}><Download className="h-4 w-4" /></Button></TooltipTrigger>
                                <TooltipContent side="right"><p>Download All Documents</p></TooltipContent>
                            </Tooltip></TooltipProvider>
                        ) : (
                            <Button variant="secondary" className="w-full" onClick={onDownloadAllDocuments} disabled={!hasGeneratedAnyDocument || generationInProgress}><Download className="h-4 w-4 md:mr-2" /> <span className="hidden md:inline">Download All</span></Button>
                        )}

                        {hasGeneratedAnyDocument && (
                            <Button variant="secondary" size={isSidebarCollapsed ? "icon" : "default"} className={cn("w-full", !isSidebarCollapsed && "justify-center gap-2 px-3", isSidebarCollapsed && "justify-center")} onClick={onExportToGoogleDrive} disabled={isExportingGoogleDrive}>
                                {isExportingGoogleDrive ? (<><LoaderCircle className="h-5 w-5 animate-spin text-blue-600" />{!isSidebarCollapsed && <span>Exporting...</span>}</>) : (<><img src="https://fonts.gstatic.com/s/i/productlogos/drive_2020q4/v8/web-64dp/logo_drive_2020q4_color_2x_web_64dp.png" alt="Google Drive" className="h-5 w-5" />{!isSidebarCollapsed && (<><span>Export to Google Drive</span>{!googleDriveConnected && <ChevronsLeftRightEllipsis className="h-4 w-4 text-muted-foreground animate-pulse ml-auto" />}</>)}</>)}
                            </Button>


                        )}
                        {hasGeneratedAnyDocument && (
                            <TooltipProvider><Tooltip delayDuration={300}>
                                <TooltipTrigger asChild>
                                    <Button variant="secondary" size={isSidebarCollapsed ? "icon" : "default"} className={cn("w-full", !isSidebarCollapsed && "justify-center gap-2 px-3", isSidebarCollapsed && "justify-center")} onClick={onExportToNotion} disabled={isExportingNotion}>
                                        {isExportingNotion ? (<><LoaderCircle className="h-5 w-5 animate-spin text-gray-700" />{!isSidebarCollapsed && <span>Exporting...</span>}</>) : (<><img src="/assets/img/integrations/notion-logo.svg" alt="Notion" className="h-5 w-5" />{!isSidebarCollapsed && (<><span>Export to Notion</span>{!notionConnected && <ChevronsLeftRightEllipsis className="h-4 w-4 text-muted-foreground animate-pulse ml-auto" />}</>)}</>)}
                                    </Button>
                                </TooltipTrigger>
                                <TooltipContent side={isSidebarCollapsed ? "right" : "top"}><p>{isSidebarCollapsed ? (notionConnected ? "Export to Notion" : "Connect Notion") : (notionConnected ? "Export to Notion" : "Connect Notion to export")}</p></TooltipContent>
                            </Tooltip></TooltipProvider>
                        )}
                    </div>
                )}
                    </div>
                </div>
            </Card>
            {hoveredDocForPopover && isSidebarCollapsed && (
                <div
                    className="fixed z-50 bg-popover text-popover-foreground shadow-md rounded-md px-3 py-1.5 text-sm animate-in fade-in-0 zoom-in-95"
                    style={popoverStyle}
                >
                    {hoveredDocForPopover.title.replace("Document", "").replace("Diagram", "").replace("Specification", "").trim()}
                </div>
            )}
        </aside>
    );
};