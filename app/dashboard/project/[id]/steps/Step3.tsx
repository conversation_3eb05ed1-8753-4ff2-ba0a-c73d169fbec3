import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
// Checkbox import removed
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ArrowLeft, ArrowRight, Loader2, AlertCircle } from "lucide-react";
import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase-client"; // Use shared client
import { cn } from "@/lib/utils"; // Import cn utility
import { useToast } from "@/hooks/use-toast";

// --- Data Structure Definitions ---
type ToolInfo = {
  id: string; // Corresponds to 'id' from ai_tools table
  name: string; // Corresponds to 'tool_name'
  logoUrl?: string | null; // Corresponds to 'logo_url'
  description: string; // Corresponds to 'description'
  website?: string | null; // Corresponds to 'website'
};

type CategoryInfo = {
  id: string; // Corresponds to category_name or a generated ID
  title: string; // Corresponds to 'category_name' from ai_tool_categories
  description: string; // Corresponds to 'description' from ai_tool_categories
  use_case: string; // Corresponds to 'use_case' from ai_tool_categories
  skill_level: string; // Corresponds to 'skill_level' from ai_tool_categories
  displayOrder: number; // Add this field
  tools: ToolInfo[]; // Array of tools belonging to this category
};

// Type for data fetched from Supabase tables
type DbCategory = {
  category_name: string;
  description: string | null;
  use_case: string | null;
  skill_level: string | null;
  key_characteristics: string | null;
  display_order: number | null;
};

type DbTool = {
  id: string;
  name: string;
  category: string | null;
  description: string | null;
  logo: string | null;
  website: string | null;
  is_active: boolean;
};


// --- Component Definition ---

type Step3Props = {
  selectedTools: string[]; // Array of selected tool IDs (using the tool's 'id' field)
  onSelectionChange: (selectedIds: string[]) => void; // Callback with updated IDs
  navigateToStep: (step: number) => void;
  projectId: string | null;
  isEditMode?: boolean; // Add this prop
}

export default function Step3({ 
  selectedTools, 
  onSelectionChange, 
  navigateToStep, 
  projectId,
  isEditMode = false // Default to false
}: Step3Props) {
  // State for storing fetched data, loading status, and errors
  const { toast } = useToast(); // Initialize toast FIRST
  const [categorizedData, setCategorizedData] = useState<Record<string, CategoryInfo>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false); // Add saving state

  // Add logging to help debug
  useEffect(() => {
    console.log("Step3: Component mounted with props:", {
      selectedTools,
      projectId,
      isEditMode,
      navigateToStep: typeof navigateToStep
    });
  }, [selectedTools, projectId, isEditMode, navigateToStep]);

  // Add this at the beginning of the component to validate projectId
  useEffect(() => {
    // Log the projectId to help debug
    console.log("Step3: projectId received:", projectId);
    
    if (!projectId) {
      console.error("Step3: No projectId provided to component");
      toast({
        title: "Configuration Error",
        description: "Project ID is missing. Please refresh the page or contact support.",
        variant: "destructive"
      });
    }
  }, [projectId, toast]);

  // Add this function to save selected tools
  const saveSelectedTools = async () => {
    console.log("saveSelectedTools called with projectId:", projectId);
    setIsSaving(true); // Set saving state
    if (!projectId) {
      console.error("Cannot save without project ID");
      setIsSaving(false);
      toast({
        title: "Error saving tools",
        description: "Project ID is missing",
        variant: "destructive"
      });
      return false; // Cannot save without project ID
    }

    try {
      // Create a standardized tech_stack object
      const techStackData = {
        selectedTools: selectedTools,
        categories: Object.values(categorizedData)
          .filter(category => category.tools.some(tool => selectedTools.includes(tool.id)))
          .map(category => ({
            id: category.id,
            name: category.title,
            tools: category.tools
              .filter(tool => selectedTools.includes(tool.id))
              .map(tool => ({
                id: tool.id,
                name: tool.name,
                description: tool.description
              }))
          }))
      };

      console.log("Saving tech stack data:", techStackData);
      const { error } = await supabase
        .from("projects")
        .update({
          selected_tools: selectedTools,
          tech_stack: techStackData, // Save the structured tech stack
          // Only update last_creation_step if not in edit mode
          ...(isEditMode ? {} : { last_creation_step: 3 })
        })
        .eq("id", projectId);

      if (error) {
        console.error("Supabase error saving tools:", error);
        throw error;
      }
      
      toast({ title: "Tools saved successfully" });
      return true;
    } catch (err) {
      console.error("Error saving tools:", err);
      toast({
        title: "Error saving tools",
        description: err instanceof Error ? err.message : "Unknown error",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Fix the handleNext function to ensure it's properly calling navigateToStep
  const handleNext = async () => {
    if (selectedTools.length === 0) {
      toast({
        title: "Please select at least one tool",
        variant: "destructive"
      });
      return;
    }

    try {
      const saved = await saveSelectedTools();
      if (saved) {
        if (isEditMode) {
          // In edit mode, fetch the last step to return to
          try {
            const { data, error } = await supabase
              .from("projects")
              .select("last_creation_step")
              .eq("id", projectId)
              .single();
            
            if (error) {
              console.error("Error fetching last step:", error);
              throw error;
            }
            
            const lastStep = data?.last_creation_step || 4;
            console.log("Edit mode save complete, navigating to step", lastStep);
            navigateToStep(lastStep);
          } catch (error) {
            console.error("Error fetching last step:", error);
            // Even if there's an error, still navigate to step 4 as fallback
            navigateToStep(4);
          }
        } else {
          // Normal flow
          console.log("Tools saved successfully, navigating to step 4");
          navigateToStep(4);
        }
      } else {
        console.error("Failed to save tools, not navigating");
        // Add a more descriptive error message
        toast({
          title: "Failed to save tools",
          description: "Please try again or check the console for details",
          variant: "destructive"
        });
      }
    } catch (err) {
      console.error("Unexpected error in handleNext:", err);
      toast({
        title: "Unexpected error",
        description: err instanceof Error ? err.message : "An unknown error occurred",
        variant: "destructive"
      });
    }
  };

  // Fetch data from Supabase on component mount
  useEffect(() => {
    // Ensure supabase client is initialized
    if (!supabase) {
      setError("Supabase client is not configured. Check environment variables.");
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // 1. Fetch Categories with correct column names
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('ai_tool_categories')
          .select('category_name, description, use_case, skill_level, key_characteristics, display_order'); // Added display_order

        if (categoriesError) throw new Error(`Error fetching categories: ${categoriesError.message}`);
        if (!categoriesData || categoriesData.length === 0) throw new Error("No categories found.");

        // 2. Fetch Tools with correct column names
        const { data: toolsData, error: toolsError } = await supabase
          .from('ai_tools')
          .select('id, name, category, description, logo, website, is_active');

        if (toolsError) throw new Error(`Error fetching tools: ${toolsError.message}`);
        if (!toolsData || toolsData.length === 0) throw new Error("No tools found.");

        console.log(`Total tools fetched: ${toolsData.length}`);

        // 3. Process and Structure Data
        const processedData: Record<string, CategoryInfo> = {};

        // Initialize categories from fetched data
        categoriesData.forEach((cat: DbCategory) => {
          const categoryKey = cat.category_name;
          const categoryId = categoryKey.toLowerCase().replace(/[^a-z0-9]+/g, '-');
          processedData[categoryKey] = {
            id: categoryId,
            title: categoryKey,
            description: cat.description || 'No description available.',
            use_case: cat.use_case || 'N/A',
            skill_level: cat.skill_level || 'N/A',
            displayOrder: cat.display_order || 999, // Use display_order from DB
            tools: []
          };
        });

        // Create a default "Uncategorized" category
        processedData["Uncategorized"] = {
          id: "uncategorized",
          title: "Uncategorized",
          description: "Tools without a specific category",
          use_case: "Various",
          skill_level: "Various",
          displayOrder: 1000, // Ensure it appears last
          tools: []
        };

        // Assign tools to categories
        let assignedCount = 0;
        toolsData.forEach((tool: DbTool) => {
          if (tool.is_active === false) {
            console.log(`Skipping inactive tool: ${tool.name}`);
            return;
          }

          const toolInfo = {
            id: String(tool.id),
            name: tool.name,
            logoUrl: tool.logo,
            description: tool.description || 'No description available.',
            website: tool.website
          };

          if (tool.category && processedData[tool.category]) {
            processedData[tool.category].tools.push(toolInfo);
            assignedCount++;
          } else {
            processedData["Uncategorized"].tools.push(toolInfo);
            console.warn(`Tool "${tool.name}" has missing or invalid category "${tool.category}". Added to Uncategorized.`);
          }
        });

        console.log(`Assigned ${assignedCount} tools to categories. ${toolsData.length - assignedCount} tools in Uncategorized.`);

        // Remove empty categories (including Uncategorized if it ends up empty)
        Object.keys(processedData).forEach(key => {
          if (processedData[key].tools.length === 0) {
            console.log(`Removing empty category: ${key}`);
            delete processedData[key];
          }
        });

        setCategorizedData(processedData);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError(err instanceof Error ? err.message : "An unknown error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []); // Empty dependency array ensures this runs only once on mount

  // Function to handle tool selection changes
  const handleCheckedChange = (toolId: string, checked: boolean) => {
    let updatedSelection: string[];
    if (checked) {
      updatedSelection = selectedTools.includes(toolId) ? selectedTools : [...selectedTools, toolId];
    } else {
      updatedSelection = selectedTools.filter(id => id !== toolId);
    }
    onSelectionChange(updatedSelection);
  };

  // Helper to get first letter for Avatar fallback
  const getInitials = (name: string) => {
    return name?.charAt(0).toUpperCase() || '?';
  }

  // --- Render Loading State ---
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64 w-full">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">Loading AI Tools...</span>
      </div>
    );
  }

  // --- Render Error State ---
  if (error) {
    return (
       <div className="flex flex-col justify-center items-center h-64 w-full text-destructive bg-destructive/10 border border-destructive rounded-md p-4">
         <AlertCircle className="h-8 w-8 mb-2" />
         <p className="font-semibold">Error loading tools:</p>
         <p className="text-sm text-center">{error}</p>
         <p className="text-xs mt-2">Please check the console for more details or try again later.</p>
       </div>
    );
  }

  // --- Render Main Content ---
  return (
    <div className="space-y-6">
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>Step 3: How do you intend to build?</CardTitle>
          
        </CardHeader>
        <CardContent>
          <TooltipProvider delayDuration={200}>
            {/* Horizontal scrolling container for categories */}
            <div className="flex flex-nowrap gap-6 pb-4 overflow-x-auto">
              {Object.values(categorizedData)
                // Sort categories by displayOrder, then alphabetically by title
                .sort((a, b) => (a.displayOrder || 999) - (b.displayOrder || 999) || a.title.localeCompare(b.title))
                .map((categoryData) => (
                  <div key={categoryData.id} className="min-w-[340px] max-w-[380px] flex-shrink-0">
                    <div className="bg-muted/30 p-4 rounded-lg border h-full flex flex-col">
                      <h3 className="text-lg font-semibold mb-2">{categoryData.title}</h3>
                      <p className="text-sm text-muted-foreground mb-4">{categoryData.description}</p>
                      
                      {/* Tools grid within each category card */}
                      <div className="flex flex-wrap gap-2 mt-auto">
                        {categoryData.tools
                          .sort((a, b) => a.name.localeCompare(b.name))
                          .map((tool) => (
                            <button
                              key={tool.id}
                              type="button"
                              onClick={() => handleCheckedChange(tool.id, !selectedTools.includes(tool.id))}
                              className={cn(
                                "flex flex-col items-center p-1 border rounded-lg transition-colors duration-150 text-center w-20 h-20",
                                selectedTools.includes(tool.id)
                                  ? "border-primary bg-primary/10"
                                  : "border-border hover:bg-muted/50 hover:border-primary/50"
                              )}
                            >
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="w-full h-full flex flex-col items-center justify-between cursor-pointer">
                                    <div className="flex-grow flex items-center justify-center">
                                      {tool.logoUrl ? (
                                        <img
                                          src={tool.logoUrl}
                                          alt={`${tool.name} logo`}
                                          className="h-10 w-10 object-contain"
                                          onError={(e) => {
                                            const target = e.target as HTMLImageElement;
                                            target.style.display = 'none';
                                          }}
                                        />
                                      ) : (
                                        <div className="h-10 w-10 flex items-center justify-center bg-muted text-muted-foreground font-medium rounded-md">
                                          {getInitials(tool.name)}
                                        </div>
                                      )}
                                    </div>
                                    <Label
                                      className="font-medium cursor-pointer text-card-foreground text-center text-xs line-clamp-1 w-full"
                                    >
                                      {tool.name}
                                    </Label>
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent side="top" className="max-w-xs"> {/* Changed to top */}
                                  <div className="space-y-1">
                                    <p className="font-medium">{tool.name}</p>
                                    <p className="text-xs">{tool.description}</p>
                                    {tool.website && (
                                      <a
                                        href={tool.website}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-xs text-blue-500 hover:underline block"
                                        onClick={(e) => e.stopPropagation()}
                                      >
                                        Visit Website
                                      </a>
                                    )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </button>
                          ))}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </TooltipProvider>
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-6">
          <Button 
            variant="outline" 
            onClick={() => {
              if (isEditMode) {
                // In edit mode, fetch the last step to return to
                const fetchLastStep = async () => {
                  try {
                    const { data } = await supabase
                      .from("projects")
                      .select("last_creation_step")
                      .eq("id", projectId)
                      .single();
                    
                    const lastStep = data?.last_creation_step || 4;
                    navigateToStep(lastStep);
                  } catch (error) {
                    console.error("Error fetching last step:", error);
                    navigateToStep(4); // Default to step 4
                  }
                };
                
                fetchLastStep();
              } else {
                // Normal flow - go back to previous step
                navigateToStep(2);
              }
            }}
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> {isEditMode ? "Cancel" : "Back"}
          </Button>
          <Button
            onClick={handleNext}
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
              </>
            ) : isEditMode ? (
              <>
                Save Changes
              </>
            ) : (
              <>
                Next <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
