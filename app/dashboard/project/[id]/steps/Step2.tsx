import { useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON>, Plus, X } from "lucide-react"
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>eader,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase-client"
import { useAuth } from "@/components/auth/auth-provider"
import { useProjectStore } from '@/lib/store/project';
import { extractProductDetails, ProductDetails } from "../../utils/extractProductDetails";

// -----------------------------------------------------------------------------
// Types
// -----------------------------------------------------------------------------

export type ClarifyingQuestion = {
  question: string;
  suggestedAnswer: string;
  userAnswer?: string;
  dimension?: string;
}

export type Step2Output = {
  isValid: boolean;
  reason?: string;
  productDetails: ProductDetails;
  clarifyingQuestions: ClarifyingQuestion[];
}

export type Step2Props = {
  projectId: string;
  navigateToStep: (step: number) => void;
  ideaText: string;
  isTestUser?: boolean;
  isEditMode?: boolean; // Add this prop
}

export default function Step2({ 
  projectId, 
  navigateToStep, 
  ideaText, 
  isTestUser = false,
  isEditMode = false // Default to false
}: Step2Props) {
  const { toast } = useToast()
  const { user, refreshUser } = useAuth()

  const [questions, setQuestions] = useState<ClarifyingQuestion[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [saving, setSaving] = useState<boolean>(false)
  const [generatingIdx, setGeneratingIdx] = useState<number | null>(null)
  const [productDetails, setProductDetails] = useState<ProductDetails>({
    targetAudience: "",
    keyFeatures: [],
    frontendTech: [],
    backendTech: [],
    usp: []
  })
  const [isGeneratingQuestions, setIsGeneratingQuestions] = useState<boolean>(false)

  // Add these state variables at the top of your component
  const [newFeature, setNewFeature] = useState("");
  const [newFrontendTech, setNewFrontendTech] = useState("");
  const [newBackendTech, setNewBackendTech] = useState("");
  const [newUSP, setNewUSP] = useState("");


  // Add this function to initialize state with default values
  const initializeProductDetails = () => {
    setProductDetails({
      targetAudience: "Software developers and product teams looking for efficient project documentation",
      keyFeatures: [
        "AI-powered document generation",
        "Customizable templates",
        "Real-time collaboration"
      ],
      frontendTech: [
        "React",
        "Next.js",
        "Tailwind CSS"
      ],
      backendTech: [
        "Node.js",
        "Supabase",
        "PostgreSQL"
      ],
      usp: [
        "10x faster documentation",
        "Consistent quality across documents",
        "Reduces technical debt"
      ]
    });
    
    toast({ 
      title: "Initialized with sample data", 
      description: "You can now edit these values or proceed with the samples."
    });
  };

  // Add this function at the top of your component
  const extractClarifyingQuestions = (data: any): ClarifyingQuestion[] => {
    // Default empty array
    if (!data) return [];
    
    console.log("Extracting clarifying questions from:", JSON.stringify(data, null, 2));
    
    try {
      // Case 1: Direct array of questions
      if (Array.isArray(data)) {
        console.log("Found direct array of questions");
        return data.map(q => ({
          question: q.question || "",
          suggestedAnswer: q.suggestedAnswer || "",
          userAnswer: q.userAnswer || q.suggestedAnswer || "",
          dimension: q.dimension || "General"
        }));
      }
      
      // Case 2: Nested in clarifyingQuestions property
      if (data.clarifyingQuestions && Array.isArray(data.clarifyingQuestions)) {
        console.log("Found questions in clarifyingQuestions property");
        return data.clarifyingQuestions.map(q => ({
          question: q.question || "",
          suggestedAnswer: q.suggestedAnswer || "",
          userAnswer: q.userAnswer || q.suggestedAnswer || "",
          dimension: q.dimension || "General"
        }));
      }
      
      // Case 3: Nested in product_details.clarifyingQuestions
      if (data.product_details && 
          data.product_details.clarifyingQuestions && 
          Array.isArray(data.product_details.clarifyingQuestions)) {
        console.log("Found questions in product_details.clarifyingQuestions");
        return data.product_details.clarifyingQuestions.map(q => ({
          question: q.question || "",
          suggestedAnswer: q.suggestedAnswer || "",
          userAnswer: q.userAnswer || q.suggestedAnswer || "",
          dimension: q.dimension || "General"
        }));
      }
      
      // Case 4: Nested in clarifying_questions.clarifyingQuestions
      if (data.clarifying_questions && 
          data.clarifying_questions.clarifyingQuestions && 
          Array.isArray(data.clarifying_questions.clarifyingQuestions)) {
        console.log("Found questions in clarifying_questions.clarifyingQuestions");
        return data.clarifying_questions.clarifyingQuestions.map(q => ({
          question: q.question || "",
          suggestedAnswer: q.suggestedAnswer || "",
          userAnswer: q.userAnswer || q.suggestedAnswer || "",
          dimension: q.dimension || "General"
        }));
      }
      
      // Case 5: Direct property in database row
      if (data.clarifying_questions && Array.isArray(data.clarifying_questions)) {
        console.log("Found questions in clarifying_questions property");
        return data.clarifying_questions.map(q => ({
          question: q.question || "",
          suggestedAnswer: q.suggestedAnswer || "",
          userAnswer: q.userAnswer || q.suggestedAnswer || "",
          dimension: q.dimension || "General"
        }));
      }
      
      console.warn("Could not find clarifying questions in data");
      return [];
    } catch (e) {
      console.error("Error extracting clarifying questions:", e);
      return [];
    }
  };

  // ---------------------------------------------------------------------------
  // Fetch clarifying_questions and product_details once
  // ---------------------------------------------------------------------------
  useEffect(() => {
    console.log(`Step2: Component mounted with projectId: ${projectId}, isEditMode: ${isEditMode}`);
    
    if (!projectId) {
      console.log("Step2: No projectId provided, skipping data fetch");
      setLoading(false);
      return;
    }

    setLoading(true);
    
    (async () => {
      try {
        // Fetch the project data
        const { data, error } = await supabase
          .from("projects")
          .select("*")
          .eq("id", projectId)
          .maybeSingle();
        
        if (error) throw error;
        
        if (!data) {
          console.log(`No data found for project ID ${projectId}`);
          setLoading(false);
          return;
        }
        
        console.log("Step2: Project data loaded:", data);
        
        // Extract clarifying questions using our robust function
        const existingQuestions = extractClarifyingQuestions(data);
        
        // Process product details using the enhanced extraction function
        const extractedDetails = extractProductDetails(data);
        console.log("Extracted product details:", extractedDetails);
        setProductDetails(extractedDetails);
        
        // If we have existing questions, use them
        if (existingQuestions.length > 0) {
          console.log("Using existing questions:", existingQuestions);
          setQuestions(existingQuestions);
          setLoading(false);
        } else {
          // Otherwise, generate new questions
          console.log("No existing questions found, generating new ones...");
          await generateClarifyingQuestions(data.idea || ideaText);
        }
      } catch (err) {
        console.error("Error loading project data:", err);
        toast({ 
          title: "Failed to load project data", 
          description: err instanceof Error ? err.message : "An unexpected error occurred", 
          variant: "destructive" 
        });
        setLoading(false);
      }
    })();
  }, [projectId, toast, isEditMode, ideaText]);

  // Function to generate clarifying questions
  const generateClarifyingQuestions = async (idea: string) => {
    if (!idea) {
      toast({ title: "No idea text provided" });
      setLoading(false);
      return;
    }

    // Check if we're already generating questions (using state instead of ref)
    if (isGeneratingQuestions) {
      console.log("Already generating questions, skipping duplicate call");
      return;
    }

    setIsGeneratingQuestions(true);
    
    try {
      console.log(`Generating clarifying questions for idea: "${idea.substring(0, 50)}..." with projectId: ${projectId || 'null'}`);
      
      // Call the API to generate clarifying questions
      const response = await fetch("/api/generate-clarifying-questions", {
        method: "POST",
        headers: {
           "Content-Type": "application/json",
           "x-user-id": user?.id || ""
        },
        body: JSON.stringify({ 
          idea,
          enhancedIdea: idea, // Use the same value if not enhanced
          projectId // Explicitly pass the projectId to the API
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to generate questions: ${response.status}`);
      }
      
      const data = await response.json();

      // Log the full API response for debugging
      console.log("API Response:", JSON.stringify(data, null, 2));

      // Extract questions using our robust function if the response has a nested structure
      const extractedQuestions = data.clarifyingQuestions || 
                                (data.data && data.data.clarifyingQuestions) || 
                                [];

      if (extractedQuestions.length > 0) {
        console.log(`Received ${extractedQuestions.length} clarifying questions`);
        
        // Format the questions with UI state properties
        const formattedQuestions = extractedQuestions.map((q: APIQuestion) => ({
          question: q.question || "",
          suggestedAnswer: q.suggestedAnswer || "",
          userAnswer: q.userAnswer || q.suggestedAnswer || "",
          dimension: q.dimension || "General"
        }));
        
        console.log("Formatted questions:", JSON.stringify(formattedQuestions, null, 2));
        
        // Update state
        setQuestions(formattedQuestions);
        
        // Extract product details
        const extractedProductDetails = data.productDetails || 
                                      (data.data && data.data.productDetails) || 
                                      {};
        
        // If we have product details in the response, use them
        if (Object.keys(extractedProductDetails).length > 0) {
          console.log("Setting product details from API response:", JSON.stringify(extractedProductDetails, null, 2));
          setProductDetails({
            targetAudience: extractedProductDetails.targetAudience || "",
            keyFeatures: Array.isArray(extractedProductDetails.keyFeatures) ? extractedProductDetails.keyFeatures : [],
            frontendTech: Array.isArray(extractedProductDetails.frontendTech) ? extractedProductDetails.frontendTech : [],
            backendTech: Array.isArray(extractedProductDetails.backendTech) ? extractedProductDetails.backendTech : [],
            usp: Array.isArray(extractedProductDetails.usp) ? extractedProductDetails.usp : [],
            problemSolved: extractedProductDetails.problemSolved || ""
          });
        }
        
        // Save questions to Supabase
        if (projectId && !isTestUser) {
          console.log(`Saving clarifying questions to Supabase for project ${projectId}`);
          try {
            // Create the Step2Output structure
            const step2Output = {
              isValid: true,
              productDetails: extractedProductDetails || productDetails,
              clarifyingQuestions: formattedQuestions
            };

            const { error } = await supabase
              .from("projects")
              .update({ 
                clarifying_questions: formattedQuestions,
                product_details: step2Output,
                // Also update individual fields for backward compatibility
                tg: extractedProductDetails.targetAudience || "",
                features: Array.isArray(extractedProductDetails.keyFeatures) ? extractedProductDetails.keyFeatures : [],
                usp: Array.isArray(extractedProductDetails.usp) ? extractedProductDetails.usp : [],
                // Add these fields
                problems: extractedProductDetails.problemSolved || "",
                frontendTech: Array.isArray(extractedProductDetails.frontendTech) ? extractedProductDetails.frontendTech : [],
                backendTech: Array.isArray(extractedProductDetails.backendTech) ? extractedProductDetails.backendTech : []
              })
              .eq("id", projectId);
              
            if (error) throw error;
            
            console.log("Successfully saved clarifying questions to Supabase");
          } catch (error) {
            console.error("Failed to save clarifying questions:", error);
          }
        }
      } else {
        console.warn("No clarifying questions returned from API");
        toast({ title: "No questions generated", description: "Please try again or proceed with manual input" });
      }
    } catch (error) {
      console.error("Error generating clarifying questions:", error);
      toast({ 
        title: "Failed to generate questions", 
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive" 
      });
    } finally {
      setIsGeneratingQuestions(false);
      setLoading(false);
    }
  };

  // ---------------------------------------------------------------------------
  // Helpers
  // ---------------------------------------------------------------------------
  // Add this function to standardize the product details structure
  const createStandardProductDetails = (details: ProductDetails, questions: ClarifyingQuestion[]): any => {
    return {
      isValid: true,
      productDetails: details,
      clarifyingQuestions: questions
    };
  };

  const persistQuestions = async (updated: ClarifyingQuestion[]) => {
    if (!projectId) return;
    
    // Create the full output structure
    const outputData = createStandardProductDetails(productDetails, updated);

    console.log("Persisting Step2 output:", JSON.stringify(outputData, null, 2));

    try {
      const { error } = await supabase
        .from("projects")
        .update({ 
          clarifying_questions: updated,
          product_details: outputData,
          // Also update individual fields for backward compatibility
          tg: productDetails.targetAudience || "",
          features: productDetails.keyFeatures || [],
          usp: productDetails.usp || [],
          // Update last_creation_step
          last_creation_step: 2
        })
        .eq("id", projectId);

      if (error) throw error;
      setQuestions(updated);
    } catch (error) {
      console.error("Failed to persist questions:", error);
      toast({ title: "Failed to save answers", variant: "destructive" });
    }
  }

  const generateAIAnswer = async (idx: number) => {
    setGeneratingIdx(idx);
    try {
      const q = questions[idx];

      // Call backend – adjust credits etc. here if desired
      const response = await fetch("/api/gemini-answer", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          idea: ideaText,
          question: q.question,
          suggestedAnswer: q.suggestedAnswer,
        }),
      });

      if (!response.ok) {
        const err = await response.json();
        throw new Error(err.error ?? `Gemini failed (${response.status})`);
      }

      const { answer } = await response.json();
      const updated = [...questions];
      updated[idx] = { ...q, userAnswer: answer };
      
      // Generate the full output structure with updated questions
      const outputData: Step2Output = {
        isValid: true,
        productDetails: productDetails, // Use the current productDetails state instead of empty values
        clarifyingQuestions: updated
      };
      
      console.log("Generated Step2 output with AI answer:", JSON.stringify(outputData, null, 2));
      
      // Update both clarifying_questions and product_details
      const { error } = await supabase
        .from("projects")
        .update({ 
          clarifying_questions: updated,
          product_details: outputData
        })
        .eq("id", projectId);
      
      if (error) throw error;
      setQuestions(updated);
      toast({ title: "AI answer inserted" });
    } catch (err: any) {
      toast({ title: "AI generation failed", description: err.message, variant: "destructive" });
    } finally {
      setGeneratingIdx(null);
      // Optional: refresh user credits if you deduct here
      if (!isTestUser) await refreshUser();
    }
  }

  const handleChange = (idx: number, value: string) => {
    const updated = [...questions]
    updated[idx] = { ...updated[idx], userAnswer: value }
    setQuestions(updated)
  }

  const addItem = (field: keyof ProductDetails, value: string) => {
    if (!value.trim()) return;
    
    setProductDetails(prev => {
      const updatedField = [...(prev[field] || []), value.trim()];
      return { ...prev, [field]: updatedField };
    });
  }

  const removeItem = (field: keyof ProductDetails, index: number) => {
    setProductDetails(prev => {
      const updatedField = [...(prev[field] || [])];
      updatedField.splice(index, 1);
      return { ...prev, [field]: updatedField };
    });
  }

  // Add this function to save product details in a consistent format
  const handleSave = async () => {
    if (!projectId) {
      toast({ title: "No project ID provided" });
      return false;
    }
    
    setSaving(true);
    
    try {
      // Create the full output structure
      const outputData = createStandardProductDetails(productDetails, questions);
      
      console.log("Saving Step2 output:", JSON.stringify(outputData, null, 2));
      
      const { error } = await supabase
        .from("projects")
        .update({ 
          product_details: outputData,
          // Also update individual fields for backward compatibility
          tg: productDetails.targetAudience || "",
          features: Array.isArray(productDetails.keyFeatures) ? productDetails.keyFeatures : [],
          usp: Array.isArray(productDetails.usp) ? productDetails.usp : [],
          // Add these fields
          problems: productDetails.problemSolved || "",
          frontendTech: Array.isArray(productDetails.frontendTech) ? productDetails.frontendTech : [],
          backendTech: Array.isArray(productDetails.backendTech) ? productDetails.backendTech : [],
          // Update last_creation_step only if not in edit mode
          ...(isEditMode ? {} : { last_creation_step: 2 }),
          clarifying_questions: questions // Make sure to save the questions too
        })
        .eq("id", projectId);
        
      if (error) throw error;
      
      toast({ title: "Product details saved" });
      
      // Return success instead of navigating
      return true;
      
    } catch (err) {
      console.error("Failed to save product details:", err);
      toast({ 
        title: "Failed to save product details", 
        description: err instanceof Error ? err.message : "An unexpected error occurred", 
        variant: "destructive" 
      });
      return false;
    } finally {
      setSaving(false);
    }
  };

  // Add a separate function for the Next button
  const handleNext = async () => {
    const success = await handleSave();
    if (success) {
      if (isEditMode) {
        console.log("Edit mode save complete, navigating back");
        // Get the last completed step from the database
        const { data: projectData, error: fetchError } = await supabase
          .from("projects")
          .select("last_creation_step")
          .eq("id", projectId)
          .single();
          
        if (fetchError) {
          console.error("Error fetching last step:", fetchError);
          toast({ title: "Error fetching last step", variant: "destructive" });
          return;
        }
        
        const lastStep = projectData?.last_creation_step || 4; // Default to step 4 if not found
        navigateToStep(lastStep);
      } else {
        // Normal flow - go to next step
        navigateToStep(3);
      }
    }
  };

  // Add a debug button to the UI (temporary)
  const DebugButton = () => (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={() => {
        console.log("Current product details state:", productDetails);
        toast({ 
          title: "Product Details State", 
          description: "Check console for details" 
        });
      }}
    >
      Debug
    </Button>
  );

  // Add this useEffect to save product details when they change
  useEffect(() => {
    if (!loading && projectId) {
      const saveProductDetails = async () => {
        const outputData = {
          isValid: true,
          productDetails: productDetails,
          clarifyingQuestions: questions
        };
        
        console.log("Auto-saving product details:", JSON.stringify(outputData, null, 2));
        
        try {
          const { error } = await supabase
            .from("projects")
            .update({ 
              product_details: outputData,
              // Also update individual fields for backward compatibility
              tg: productDetails.targetAudience || "",
              features: productDetails.keyFeatures || [],
              usp: productDetails.usp || [],
              // Add these fields
              problems: productDetails.problemSolved || "",
              frontendTech: productDetails.frontendTech || [],
              backendTech: productDetails.backendTech || []
            })
            .eq("id", projectId);
          
          if (error) {
            console.error("Error saving product details:", error);
          }
        } catch (error) {
          console.error("Failed to auto-save product details:", error);
        }
      };
      
      // Use a timeout to debounce the save
      const timeoutId = setTimeout(saveProductDetails, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [productDetails, projectId, loading, questions]);

  // Add debug logging to help identify issues
  useEffect(() => {
    console.log("Step2 render with props:", {
      projectId,
      isEditMode,
      productDetailsLoaded: !!productDetails && Object.keys(productDetails).length > 0,
      questionsLoaded: questions.length
    });
  }, [projectId, isEditMode, productDetails, questions]);

  // ---------------------------------------------------------------------------
  // Render
  // ---------------------------------------------------------------------------
  if (loading) {
    return (
      <div className="flex h-full w-full items-center justify-center p-10">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    )
  }

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="pb-4">
        <CardTitle>Clarify Your Project Details</CardTitle>
        <CardDescription>
          Answer these questions to help define your project more clearly. You can use the suggested answers or provide your own.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {loading ? (
          // Loading state UI
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Problem Solved</h3>
              <Skeleton className="h-24 w-full" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Target Audience</h3>
              <Skeleton className="h-24 w-full" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Key Features</h3>
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Frontend Technologies</h3>
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Backend Technologies</h3>
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Unique Selling Points</h3>
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Clarifying Questions</h3>
              <div className="space-y-4">
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-32 w-full" />
              </div>
            </div>
          </div>
        ) : (
          // Actual content when loaded
          <>
            {/* Problem Solved Section */}
            <div>
              <h3 className="text-lg font-medium">Problem Solved</h3>
              <Textarea
                value={productDetails.problemSolved || ""}
                onChange={(e) => setProductDetails({...productDetails, problemSolved: e.target.value})}
                placeholder="What problem does your product solve?"
                className="min-h-[100px]"
              />
            </div>

            {/* Target Audience Section */}
            <div>
              <h3 className="text-lg font-medium">Target Audience</h3>
              <Textarea
                value={productDetails.targetAudience || ""}
                onChange={(e) => setProductDetails({...productDetails, targetAudience: e.target.value})}
                placeholder="Who will use your product?"
                className="min-h-[100px]"
              />
            </div>
            
            {/* Key Features Section */}
            <div>
              <h3 className="text-lg font-medium">Key Features</h3>
              <div className="space-y-2">
                {productDetails.keyFeatures?.map((feature, idx) => (
                  <div key={idx} className="flex items-center gap-2">
                    <Input 
                      value={feature} 
                      onChange={(e) => {
                        const updated = [...(productDetails.keyFeatures || [])];
                        updated[idx] = e.target.value;
                        setProductDetails({...productDetails, keyFeatures: updated});
                      }}
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => {
                        const updated = [...(productDetails.keyFeatures || [])];
                        updated.splice(idx, 1);
                        setProductDetails({...productDetails, keyFeatures: updated});
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <div className="flex items-center gap-2">
                  <Input 
                    placeholder="Add a new feature..." 
                    value={newFeature}
                    onChange={(e) => setNewFeature(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newFeature.trim()) {
                        addItem('keyFeatures', newFeature);
                        setNewFeature('');
                      }
                    }}
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      if (newFeature.trim()) {
                        addItem('keyFeatures', newFeature);
                        setNewFeature('');
                      }
                    }}
                  >
                    Add
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Frontend Tech Section */}
            <div>
              <h3 className="text-lg font-medium">Frontend Technologies</h3>
              <div className="space-y-2">
                {productDetails.frontendTech?.map((tech, idx) => (
                  <div key={idx} className="flex items-center gap-2">
                    <Input 
                      value={tech} 
                      onChange={(e) => {
                        const updated = [...(productDetails.frontendTech || [])];
                        updated[idx] = e.target.value;
                        setProductDetails({...productDetails, frontendTech: updated});
                      }}
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => {
                        const updated = [...(productDetails.frontendTech || [])];
                        updated.splice(idx, 1);
                        setProductDetails({...productDetails, frontendTech: updated});
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <div className="flex items-center gap-2">
                  <Input 
                    placeholder="Add a frontend technology..." 
                    value={newFrontendTech}
                    onChange={(e) => setNewFrontendTech(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newFrontendTech.trim()) {
                        addItem('frontendTech', newFrontendTech);
                        setNewFrontendTech('');
                      }
                    }}
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      if (newFrontendTech.trim()) {
                        addItem('frontendTech', newFrontendTech);
                        setNewFrontendTech('');
                      }
                    }}
                  >
                    Add
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Backend Tech Section */}
            <div>
              <h3 className="text-lg font-medium">Backend Technologies</h3>
              <div className="space-y-2">
                {productDetails.backendTech?.map((tech, idx) => (
                  <div key={idx} className="flex items-center gap-2">
                    <Input 
                      value={tech} 
                      onChange={(e) => {
                        const updated = [...(productDetails.backendTech || [])];
                        updated[idx] = e.target.value;
                        setProductDetails({...productDetails, backendTech: updated});
                      }}
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => {
                        const updated = [...(productDetails.backendTech || [])];
                        updated.splice(idx, 1);
                        setProductDetails({...productDetails, backendTech: updated});
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <div className="flex items-center gap-2">
                  <Input 
                    placeholder="Add a backend technology..." 
                    value={newBackendTech}
                    onChange={(e) => setNewBackendTech(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newBackendTech.trim()) {
                        addItem('backendTech', newBackendTech);
                        setNewBackendTech('');
                      }
                    }}
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      if (newBackendTech.trim()) {
                        addItem('backendTech', newBackendTech);
                        setNewBackendTech('');
                      }
                    }}
                  >
                    Add
                  </Button>
                </div>
              </div>
            </div>
            
            {/* USP Section */}
            <div>
              <h3 className="text-lg font-medium">Unique Selling Points</h3>
              <div className="space-y-2">
                {productDetails.usp?.map((point, idx) => (
                  <div key={idx} className="flex items-center gap-2">
                    <Input 
                      value={point} 
                      onChange={(e) => {
                        const updated = [...(productDetails.usp || [])];
                        updated[idx] = e.target.value;
                        setProductDetails({...productDetails, usp: updated});
                      }}
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => {
                        const updated = [...(productDetails.usp || [])];
                        updated.splice(idx, 1);
                        setProductDetails({...productDetails, usp: updated});
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <div className="flex items-center gap-2">
                  <Input 
                    placeholder="Add a unique selling point..." 
                    value={newUSP}
                    onChange={(e) => setNewUSP(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newUSP.trim()) {
                        addItem('usp', newUSP);
                        setNewUSP('');
                      }
                    }}
                  />
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      if (newUSP.trim()) {
                        addItem('usp', newUSP);
                        setNewUSP('');
                      }
                    }}
                  >
                    Add
                  </Button>
                </div>
              </div>
            </div>

            {/* Clarifying Questions Section */}
            <div>
              <h3 className="text-lg font-medium">Clarifying Questions</h3>
              {questions.length > 0 ? (
                <div className="space-y-4">
                  {questions.map((q, idx) => (
                    <div key={idx} className="space-y-2 border p-4 rounded-md">
                      <div className="flex justify-between items-center mb-2">
                        <p className="font-medium">{q.question}</p>
                        <Button
                          size="sm"
                          variant="ghost"
                          disabled={generatingIdx !== null}
                          onClick={() => generateAIAnswer(idx)}
                          className="text-xs"
                        >
                          {generatingIdx === idx ? (
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                          ) : (
                            <Sparkles className="mr-1 h-3 w-3" />
                          )}
                          {generatingIdx === idx ? "Generating…" : "AI Answer"}
                        </Button>
                      </div>
                      <Textarea
                        value={q.userAnswer || q.suggestedAnswer || ""}
                        onChange={(e) => handleChange(idx, e.target.value)}
                        placeholder="Your answer..."
                        className="min-h-[100px]"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 border rounded-md">
                  {isGeneratingQuestions ? (
                    <div className="flex flex-col items-center space-y-2">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <p>Generating questions...</p>
                    </div>
                  ) : (
                    <p>No questions available. Try refreshing or adding more details to your idea.</p>
                  )}
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>

      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 border-t pt-4">
          <h3 className="text-lg font-medium">Debug Tools</h3>
          <div className="flex gap-2 mt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                console.log("Current questions state:", questions);
                console.log("Current productDetails state:", productDetails);
                toast({ title: "State logged to console" });
              }}
            >
              Log State
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={async () => {
                if (!projectId) return;
                const { data, error } = await supabase
                  .from("projects")
                  .select("*")
                  .eq("id", projectId)
                  .single();
                
                if (error) {
                  console.error("Error fetching project data:", error);
                  toast({ title: "Error fetching project data" });
                  return;
                }
                
                console.log("Raw project data:", data);
                toast({ title: "Project data logged to console" });
              }}
            >
              Fetch DB Data
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                if (!ideaText) {
                  toast({ title: "No idea text available" });
                  return;
                }
                
                generateClarifyingQuestions(ideaText);
                toast({ title: "Regenerating questions..." });
              }}
            >
              Regenerate Questions
            </Button>
          </div>
        </div>
      )}

      <CardFooter className="flex justify-between border-t pt-6">
        <Button 
          variant="outline" 
          onClick={() => navigateToStep(1)}
          disabled={isGeneratingQuestions}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Back
        </Button>
        <Button
          disabled={saving || generatingIdx !== null || isGeneratingQuestions}
          onClick={handleNext}
        >
          {saving || isGeneratingQuestions ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> 
              {isGeneratingQuestions ? "Generating..." : "Saving..."}
            </>
          ) : (
            <>
              {isEditMode ? "Save Changes" : "Next"} {!isEditMode && <ArrowRight className="ml-2 h-4 w-4" />}
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
