// Step1.tsx — Idea capture + inline interactive refinement (Q&A)
// v0.5 – Updated with clarifying questions feature

"use client";

import React, { forwardRef, useImperativeHandle, useState, useEffect } from "react";
import type { UseFormReturn } from "react-hook-form";
import { supabase } from "@/lib/supabase-client";
import { useProjectStore } from '@/lib/store/project';

// --- UI components (shadcn/ui) -------------------------
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";

// --- Icons & helpers ----------------------------------
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Spa<PERSON><PERSON>, Loader2, ArrowRight, X, <PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";
import { AudioRecorder } from "@/components/project/audio-recorder";
import { toast } from "@/hooks/use-toast";

// -------------------------------------------------------
// Types -------------------------------------------------
export type IdeaFormData = {
  idea: string;
  projectName?: string;
};

export type Step1Props = {
  ideaForm: UseFormReturn<IdeaFormData>;
  isRecording: boolean;
  toggleRecording: () => void;
  handleTranscription: (text: string, audioUrl: string) => void;
  navigateToStep: (step: number) => void;
}

// Clarifying question type
type ClarifyingQuestion = {
  question: string;
  suggestedAnswer: string;
  userAnswer?: string;
  isEditing?: boolean;
  isDeleted?: boolean;
};

type APIQuestion = {
  question: string;
  suggestedAnswer: string;
  dimension?: string;
};

type APIResponse = {
  clarifyingQuestions: APIQuestion[];
  productDetails?: {
    targetAudience?: string;
    keyFeatures?: string[];
    frontendTech?: string[];
    backendTech?: string[];
    usp?: string[];
  };
};

// -------------------------------------------------------
const Step1 = ({ ideaForm, isRecording, toggleRecording, handleTranscription, navigateToStep }: Step1Props) => {
  const {
    projectId,
    isTestUser,
    isRefining,
    isGeneratingQuestions,
    refineIdea,
    generateClarifyingQuestions,
    setProjectName,
    setRefinedIdea,
    setClarifyingQuestions
  } = useProjectStore();

  const handleRefineIdea = async () => {
    const ideaValue = ideaForm.getValues("idea");
    if (!ideaValue) {
      toast({ title: "Please enter an idea first" });
      return;
    }

    try {
      // Call the refineIdea function from the store
      await refineIdea(ideaValue);
      
      // Get the refined idea from the store
      const refinedIdea = useProjectStore.getState().refinedIdea;
      ideaForm.setValue("idea", refinedIdea);
      
      // Check if we have a product name from the API response
      const productName = useProjectStore.getState().projectName;
      if (productName && productName !== "Untitled Project") {
        // Set the project name in the form
        ideaForm.setValue("projectName", productName);
        
        // If we have a project ID, update the name in the database
        if (projectId && !isTestUser) {
          const { error } = await supabase
            .from("projects")
            .update({ name: productName })
            .eq("id", projectId);
            
          if (error) {
            console.error("Failed to save project name to database:", error);
          } else {
            console.log(`Project name "${productName}" saved to database for project ID: ${projectId}`);
          }
        }
      }
      
      toast({ title: "Idea refined successfully" });
    } catch (error) {
      toast({ title: "Failed to refine idea", variant: "destructive" });
    }
  };

  const handleNext = async () => {
    const ideaValue = ideaForm.getValues("idea");
    
    // Validate idea before proceeding
    if (!ideaValue || ideaValue.length < 10) {
      toast({ title: "Please enter a more detailed idea (at least 10 characters)" });
      return;
    }
    
    // Save project name if available
    if (projectId && !isTestUser) {
      try {
        const projectName = ideaForm.getValues("projectName");
        if (projectName) {
          await setProjectName(projectName);
        }
        
        // Save the idea to the database
        const { error } = await supabase
          .from("projects")
          .update({ 
            idea: ideaValue,
            last_creation_step: 1 // Update last_creation_step
          })
          .eq("id", projectId);
          
        if (error) {
          console.error("Failed to save idea:", error);
          toast({ title: "Failed to save idea", variant: "destructive" });
          // Continue despite error
        }
      } catch (error) {
        console.error("Failed to save project name:", error);
        toast({ title: "Failed to save project name", variant: "destructive" });
        // Continue despite error
      }
    }

    // Simply navigate to next step - questions will be generated there
    navigateToStep(2);
  };

  return (
    <Card className="w-full shadow-md">
      <CardHeader className="pb-4">
        <CardTitle>What do you want to build?</CardTitle>
        <CardDescription>
          Describe your product idea in detail. Try to answer - What problem does it solve? Who is the target auidence? What makes it unique? &nbsp;You can type or record audio, then let our AI refine it and generate clarifying questions.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <Form {...ideaForm}>
          <div className="space-y-6">
            <FormField
              control={ideaForm.control}
              name="idea"
              render={({ field }) => (
                <FormItem>
                  <div className="relative">
                    <FormControl>
                      <Textarea
                        placeholder="Describe your product idea in detail…"
                        className="resize-none pr-[180px] min-h-[150px]" // Minimum height of 150px
                        {...field}
                      />
                    </FormControl>
                    <div className="absolute right-2 top-2 flex flex-col gap-2">
                      {/* Voice recording */}
                      <Button
                        type="button"
                        size="sm"
                        variant={isRecording ? "destructive" : "outline"}
                        onClick={toggleRecording}
                      >
                        {isRecording ? (
                          <>
                            <MicOff className="mr-1 h-4 w-4" /> Stop Recording
                          </>
                        ) : (
                          <>
                            <Mic className="mr-1 h-4 w-4" /> Record
                          </>
                        )}
                      </Button>

                      {/* AI refine */}
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={handleRefineIdea}
                        disabled={isRefining || isGeneratingQuestions || !field.value || field.value.length < 10}
                      >
                        {isRefining || isGeneratingQuestions ? (
                          <>
                            <Loader2 className="mr-1 h-4 w-4 animate-spin" /> 
                            {isRefining ? "Refining…" : "Generating…"}
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-1 h-4 w-4" /> Refine
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  {isRecording && (
                    <div className="mt-4 rounded-md border p-4">
                      <p className="mb-2 text-sm text-muted-foreground">Recording audio…</p>
                      <AudioRecorder onTranscription={handleTranscription} />
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </Form>

        {/* Removing the Clarifying Questions Section */}
      </CardContent>

      <CardFooter className="flex justify-between">
        <Button variant="outline" asChild>
          <Link href="/dashboard/project">Cancel</Link>
        </Button>
        <Button
          type="button"
          onClick={handleNext}
          disabled={!ideaForm.getValues("idea") || isGeneratingQuestions}
        >
          {isGeneratingQuestions ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
            </>
          ) : (
            <>
              Next <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default Step1;
