// /app/dashboard/project/[id]/steps/Step4.tsx
// v2.2 – April 18 2025
// -----------------------------------------------------------------------------
// • Displays ALL inputs collected so far: raw idea, refined idea, selected tools,
//   and each clarifying‑question ⇢ answer pair.
// • Added edit buttons that jump to prior steps.
// • Fetches the raw `idea` as well, and passes both `idea` & `refinedIdea` to
//   `/api/gemini-plan`.
// • Extra console logs for each render section.
// -----------------------------------------------------------------------------

import { useEffect, useState } from "react"
import { <PERSON>L<PERSON>t, ArrowRight, Edit2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ev<PERSON><PERSON><PERSON> } from "lucide-react"
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase-client"
import { useAuth } from "@/components/auth/auth-provider"
import RichTextViewer from "@/components/dashboard/rich-text-viewer"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { MarkdownRenderer } from "@/components/ui/markdown-renderer"
import { useProjectStore } from '@/lib/store/project';
// Import the prompt from the file
import { prompt as outlinePrompt } from '@/lib/prompts/outline/v1';

// -----------------------------------------------------------------------------
// Types
// -----------------------------------------------------------------------------
export type ClarifyingQuestion = {
  question: string
  suggestedAnswer: string
  userAnswer?: string
}

export type Step4Props = {
  projectId: string
  selectedTools: string[]
  projectPlan: string
  setProjectPlan: (plan: string) => void
  isGeneratingPlan: boolean
  setIsGeneratingPlan: (b: boolean) => void
  navigateToStep: (step: number) => void
  isTestUser?: boolean
  isEditMode?: boolean
  onSaveComplete?: () => void
}

// helper: Convert camelCase or snake_case → Title Case
function toTitleCase(str: string) {
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/_/g, ' ')
    .replace(/\b\w/g, c => c.toUpperCase())
    .trim()
}

// helper: Pick render strategy based on key & value
function renderValue(key: string, value: any) {
  if (key === 'clarifyingQuestions' && Array.isArray(value)) {
    return (
      <div className="space-y-4">
        {value.map((q: any, i: number) => (
          <div key={i} className="space-y-1">
            <p className="font-medium">{q.question}</p>
            <p className="text-sm text-muted-foreground whitespace-pre-wrap">
              {q.userAnswer ?? q.suggestedAnswer}
            </p>
          </div>
        ))}
      </div>
    )
  }
  if (Array.isArray(value)) {
    return (
      <ul className="list-disc pl-5 text-sm text-muted-foreground">
        {value.map((v, i) => <li key={i}>{String(v)}</li>)}
      </ul>
    )
  }
  if (typeof value === 'object' && value !== null) {
    return <pre className="text-sm text-muted-foreground whitespace-pre-wrap">
      {JSON.stringify(value, null, 2)}
    </pre>
  }
  return <p className="text-sm text-muted-foreground">{String(value)}</p>
}

export default function Step4({
  projectId,
  selectedTools,
  projectPlan,
  setProjectPlan,
  isGeneratingPlan,
  setIsGeneratingPlan,
  navigateToStep,
  isTestUser,
  isEditMode,
  onSaveComplete
}: Step4Props) {
  const { toast } = useToast()
  const { user, refreshUser } = useAuth()

  const [loading, setLoading] = useState(true)
  const [idea, setIdea] = useState("")
  const [refinedIdea, setRefinedIdea] = useState("")
  const [questions, setQuestions] = useState<ClarifyingQuestion[]>([])
  const [isSavingPlan, setIsSavingPlan] = useState(false)
  const [productDetails, setProductDetails] = useState<Record<string, any>>({})
  const [planHtml, setPlanHtml] = useState<string | null>(null);

  // ---------------------------------------------------------------------------
  // Fetch project data (still needed for API calls)
  // ---------------------------------------------------------------------------
  useEffect(() => {
    console.log(`Step4: Loading project data for projectId: ${projectId}, projectPlan prop length: ${projectPlan?.length || 0}`);
    
    // Clear local component state when projectId changes to prevent data leakage between projects
    setPlanHtml(null);
    setIdea("");
    setRefinedIdea("");
    setQuestions([]);
    setProductDetails({});
    setLoading(true);
    
    if (!projectId) {
      console.log("Step4: No projectId provided, skipping data fetch");
      setLoading(false);
      return;
    }

    (async () => {
      try {
        const { data, error } = await supabase
          .from("projects")
          .select("id, name, idea, refined_idea, clarifying_questions, project_plan, product_details")
          .eq("id", projectId)
          .single();

        if (error) {
          toast({ title: "Failed to load project data", description: error.message, variant: "destructive" });
          setLoading(false);
          return;
        }
        
        console.log(`Step4: Successfully loaded project data for ID ${projectId}`);
        
        // Update local component state with fetched data
        setIdea(data.idea ?? "");
        setRefinedIdea(data.refined_idea ?? "");
        
        // Process clarifying_questions
        if (data.clarifying_questions) {
          if (Array.isArray(data.clarifying_questions.clarifyingQuestions)) {
            setQuestions(data.clarifying_questions.clarifyingQuestions);
          } else if (Array.isArray(data.clarifying_questions)) {
            setQuestions(data.clarifying_questions);
          } else {
            setQuestions([]);
          }
        } else {
          setQuestions([]);
        }
        
        // Process product_details
        if (data.product_details) {
          if (typeof data.product_details === 'object') {
            setProductDetails(data.product_details);
          } else {
            setProductDetails({});
          }
        } else {
          setProductDetails({});
        }
        
        // Simplified plan handling logic - prioritize database plan for consistency
        if (data?.project_plan) {
          console.log(`Step4: Using database plan for project ${projectId}`);
          setPlanHtml(data.project_plan);
          setProjectPlan(data.project_plan);
        } else {
          console.log(`Step4: No plan available for project ${projectId}, clearing plan state`);
          setPlanHtml(null);
          setProjectPlan(""); // Clear parent state too
        }
      } catch (err) {
        toast({ 
          title: "Failed to load project data", 
          description: err instanceof Error ? err.message : "An unexpected error occurred", 
          variant: "destructive" 
        });
        console.error(`Step4: Unexpected error loading project data:`, err);
      } finally {
        setLoading(false);
      }
    })();
  }, [projectId, toast, setProjectPlan]);

  // ---------------------------------------------------------------------------
  // Gemini plan generation
  // ---------------------------------------------------------------------------
  const generateGeminiPlan = async (isRegeneration = false) => {
    try {
      console.log(`generateGeminiPlan called for project ID: ${projectId} with isRegeneration: ${isRegeneration}`);
      
      if (!projectId) {
        console.error("Cannot generate plan: No project ID provided");
        toast({ 
          title: "Cannot generate plan", 
          description: "Project ID is missing", 
          variant: "destructive" 
        });
        return null;
      }

      // Fetch the latest project data directly from the database to ensure we have current data
      const { data: projectData, error: projectError } = await supabase
        .from("projects")
        .select("id, name, idea, refined_idea, clarifying_questions, product_details, selected_tools, project_plan")
        .eq("id", projectId)
        .single();
      
      if (projectError) {
        console.error("Error fetching project data for plan generation:", projectError);
        toast({ 
          title: "Failed to load project data", 
          description: projectError.message, 
          variant: "destructive" 
        });
        return null;
      }
      
      // Use the freshly fetched data instead of potentially cleared state
      const currentIdea = projectData.idea || "";
      const currentRefinedIdea = projectData.refined_idea || "";
      const currentProductDetails = projectData.product_details || {};
      const currentSelectedTools = projectData.selected_tools || [];
      
      
      // Extract questions from the database data
      let currentQuestions = questions;
      if (projectData.clarifying_questions) {
        if (Array.isArray(projectData.clarifying_questions.clarifyingQuestions)) {
          currentQuestions = projectData.clarifying_questions.clarifyingQuestions;
        } else if (Array.isArray(projectData.clarifying_questions)) {
          currentQuestions = projectData.clarifying_questions;
        }
      }
      
      // Now use these current values for the rest of the function
      let currentPlan = "";
      
      setIsGeneratingPlan(true);
      
      // Determine which task_slug to use based on whether this is a regeneration
      const taskSlug = isRegeneration ? 'plan_regen' : 'plan_gen';
      console.log(`Using task_slug: ${taskSlug} for ${isRegeneration ? 'regeneration' : 'generation'}`);
      
      // Always fetch the latest plan from the database when regenerating or in edit mode
      if ((isRegeneration || isEditMode) && projectId && !isTestUser) {
        // Fetch the latest plan from the database
        const { data, error } = await supabase
          .from('projects')
          .select('project_plan')
          .eq('id', projectId)
          .single();
        
        if (error) {
          console.error("Error fetching current plan:", error);
        } else if (data?.project_plan) {
          currentPlan = data.project_plan;
          console.log("Retrieved current plan from database, length:", data.project_plan.length);
        } else {
          console.log("No project_plan found in database");
        }
      }
      
      // Get user's subscription tier
      const { data: userData, error: userError } = await supabase
        .from("profiles")
        .select("subscription_tier")
        .eq("id", user?.id || "")
        .maybeSingle();
      
      // Default to 'free' tier if user not found or error
      const userTier = userData?.subscription_tier || "free";
      console.log(`User tier: ${userTier}`);
      
      // Use the imported prompt instead of fetching from the database
      const promptData = {
        content: outlinePrompt.aiPrompt,
        free_model: "gpt-4o",
        pro_model: "gpt-4o",
        enterprise_model: "gpt-4o",
        temperature: 0.7,
        max_tokens: 4000
      };
      
      // Select model based on user tier
      const modelKey = `${userTier}_model`;
      const model = promptData[modelKey] || promptData.free_model || "gpt-4o";
      const temperature = promptData.temperature || 0.7;
      const maxTokens = promptData.max_tokens || 4000;
      
      console.log(`Using model: ${model}, temperature: ${temperature}, maxTokens: ${maxTokens}`);
      
      // Format the questions for the prompt
      const questionsText = currentQuestions.length > 0
        ? currentQuestions.map(q => `Q: ${q.question}\nA: ${q.userAnswer || q.suggestedAnswer || 'No answer provided'}`).join('\n\n')
        : 'No clarifying questions answered';

      // Format the tools for the prompt
      const toolsText = currentSelectedTools && currentSelectedTools.length > 0 
        ? `Selected tools: ${currentSelectedTools.join(', ')}` 
        : 'No specific tools selected';
      
      // Prepare a text dump of your collected product details
      const detailsText = JSON.stringify(currentProductDetails, null, 2);

      // Replace placeholders in the prompt from the file
      const prompt = promptData.content
        .replace('{{refinedIdea}}', currentRefinedIdea || currentIdea || '')
        .replace('{{productDetails}}', detailsText);
      
      console.log("Prompt after replacement:", prompt.substring(0, 200) + "...");
      console.log("Calling /api/gemini-plan endpoint...");
      
      const response = await fetch('/api/gemini-plan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user?.id || '', // Add user ID to headers
        },
        body: JSON.stringify({
          taskSlug,
          projectId, // Add projectId for logging
          model,
          temperature,
          maxTokens,
          prompt,
          idea: currentIdea || "",
          refinedIdea: currentRefinedIdea || "",
          tools: currentSelectedTools || [],
          currentPlan, // This will be the freshly fetched plan from the database
          productDetails: currentProductDetails // Add product details to the request
        }),
      });
      
      console.log("API response status:", response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to generate plan: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log("API response data received");
      const planText = data.plan;
      
      if (!planText) {
        throw new Error('Received empty response from AI service');
      }
      
      console.log(`Generated plan with ${planText.length} characters`);
      setPlanHtml(planText);
      setProjectPlan(planText);
      
      // Save to database if not in test mode
      if (projectId && !isTestUser) {
        const { error: updateError } = await supabase
          .from('projects')
          .update({ project_plan: planText })
          .eq('id', projectId);
          
        if (updateError) {
          console.error('Error saving plan to database:', updateError);
        }
      }
      
      return planText;
    } catch (error) {
      console.error('Error generating project plan:', error);
      toast({
        title: "Error generating plan",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive"
      });
      return null;
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  // Handle saving plan changes manually
  const handleSavePlan = async () => {
    if (!planHtml) {
      toast({ title: "No plan to save", variant: "destructive" });
      return;
    }
    
    setIsSavingPlan(true);
    
    try {
      // Update parent state (prop function)
      setProjectPlan(planHtml); 

      // Save to database
      if (projectId && !isTestUser) {
        const { error: updateError } = await supabase
          .from('projects')
          .update({ project_plan: planHtml })
          .eq('id', projectId);
        
        if (updateError) {
          toast({ title: "Failed to save plan changes", description: updateError.message, variant: "destructive" });
          throw updateError; 
        } else {
           toast({ title: "Plan changes saved" });
           if (onSaveComplete) onSaveComplete();
        }
      } else {
         toast({ title: "Plan changes saved (local only)" });
      }
    } catch (error) {
      console.error('Error saving plan:', error);
    } finally {
      setIsSavingPlan(false);
    }
  };

  // ---------------------------------------------------------------------------
  // Render
  // ---------------------------------------------------------------------------
  if (loading) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Simplified UI */}
      <Card className="border shadow-sm">
        {/*}
        <CardHeader>
          <CardTitle>Project Plan</CardTitle>
          <CardDescription>
            Generate a detailed outline for your project
          </CardDescription>
        </CardHeader>
        */}

        <CardContent>
          {planHtml ? (
            <div className="space-y-4">
              <div className=" p-4 rounded-md">
                <h3 className="text-lg font-medium mb-2">Product Outline</h3>
                <p className="text-sm text-muted-foreground">
                  This outline will be used to generate detailed documents in the next step.
                </p>
              </div>
              
              <div className="prose prose-sm max-w-none dark:prose-invert border p-2 rounded-md bg-card">
                <MarkdownRenderer>{planHtml}</MarkdownRenderer>
              </div>
              
              <div className="flex justify-end gap-2 mt-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  disabled={isGeneratingPlan || isSavingPlan} 
                  onClick={() => generateGeminiPlan(true)}
                >
                  {isGeneratingPlan ? 
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Regenerating…</> : 
                    "Regenerate Plan"}
                </Button>
                <Button 
                  size="sm" 
                  disabled={isGeneratingPlan || isSavingPlan} 
                  onClick={handleSavePlan}
                >
                  {isSavingPlan ? 
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving…</> : 
                    "Save Changes"}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="bg-muted/50 p-6 rounded-md text-center">
                <h3 className="text-lg font-medium mb-2">Generate Outline</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  The detailed product outline that will be used to create documents in the final step.
                </p>
                <Button 

      
                  className="w-full max-w-md" 
                  disabled={isGeneratingPlan} 
                  onClick={() => generateGeminiPlan(false)}
                >
                  {isGeneratingPlan ? 
                    <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating Outline…</> : 
                    "Generate Project Outline"}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => navigateToStep(3)}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <Button 
            disabled={!planHtml || isGeneratingPlan || isSavingPlan} 
            onClick={() => navigateToStep(5)}
          >
            Next <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}

// -----------------------------------------------------------------------------
// Helpers – credit log
// -----------------------------------------------------------------------------
async function updateUserCredits(userId: string, newCreditAmount: number) {
  console.log("Step4: updating user credits →", newCreditAmount)
  const { supabase } = await import("@/lib/supabase-client")
  await supabase.from("profiles").update({ credits_remaining: newCreditAmount }).eq("id", userId)
}

async function logCreditUsage(userId: string, projectId: string, action: string, creditsUsed: number) {
  console.log("Step4: inserting credit_usage_log row")
  const { supabase } = await import("@/lib/supabase-client")
  await supabase.from("credit_usage_log").insert([{ user_id: userId, project_id: projectId, action, credits_used: creditsUsed }])
}
