// /app/dashboard/project/[id]/steps/Step5.tsx
// v2.1 - Replaced basic editor with Markdown renderer
// v2.2 - Refactored into smaller components

"use client";

import { useState, useEffect, useRef, useMemo, useCallback } from "react"
import { Loader2, Refresh<PERSON><PERSON>, XCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Toolt<PERSON>,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase-client";
import { useRouter, useSearchParams } from "next/navigation";

// Import new sub-components
import { DocumentSidebar, UIDocumentType } from "./step5components/DocumentSidebar";
import { DocumentViewer } from "./step5components/DocumentViewer";
import { DownloadModal } from "./step5components/DownloadModal";

// Import Lexical Editor and related components
import { Editor } from "@/components/blocks/editor-x/editor";
import { EditorContent } from "@/components/blocks/editor-x/plugins";
import { SerializedEditorState, createEditor, $getRoot } from 'lexical'; // Added $getRoot
import { $convertFromMarkdownString, $convertToMarkdownString, TRANSFORMERS } from '@lexical/markdown';
import { TableNode, TableCellNode, TableRowNode } from '@lexical/table';
import { ListNode, ListItemNode } from '@lexical/list';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { CodeNode, CodeHighlightNode } from '@lexical/code';
import { LinkNode } from '@lexical/link';

// Define a constant for the default content
// const DEFAULT_CONTENT = "Start typing here..."; // Will be replaced by DEFAULT_LEXICAL_STATE

// Helper function to ensure we always have valid content
const DEFAULT_LEXICAL_STATE: SerializedEditorState = {
  root: { children: [{ children: [], direction: null, format: '', indent: 0, type: 'paragraph', version: 1 }], direction: null, format: '', indent: 0, type: 'root', version: 1 },
};

const LEXICAL_NODES = [TableNode, TableCellNode, TableRowNode, ListNode, ListItemNode, HeadingNode, QuoteNode, CodeNode, CodeHighlightNode, LinkNode];

// -----------------------------------------------------------------------------
// Types & Constants
// -----------------------------------------------------------------------------
const WIZARD_STAGES = [
  { id: 1, label: "Idea" },
  { id: 2, label: "Tools" },
  { id: 3, label: "Details" },
  { id: 4, label: "Plan" },
  { id: 5, label: "Generate" },
] as const

type GenerationStatus = "idle" | "generating" | "done" | "error"
// -----------------------------------------------------------------------------
// Props
// -----------------------------------------------------------------------------
interface UserType {
  credits_remaining?: number
  id?: string
}

interface Step5Props {
  user: UserType | null
  navigateToStep: (step: number) => void
  projectId: string
  projectPlan: string
  refinedIdea?: string
  markProjectAsCompleted?: () => Promise<void>
  isEditMode?: boolean // Keep this prop if needed for layout differences
  // New props for modal control from page.tsx
  googleDriveConnectedProp?: boolean;
  notionConnectedProp?: boolean;
  setGoogleDriveExplanationOpen?: (isOpen: boolean) => void;
  setNotionExplanationOpen?: (isOpen: boolean) => void;
  setGoogleDriveExportSuccessOpen?: (isOpen: boolean) => void;
  setNotionExportSuccessOpen?: (isOpen: boolean) => void;
  setExportedFolderUrl?: (url: string) => void;
  setNotionExportUrl?: (url: string) => void;
  // New props for parent-driven export actions
  onGoogleDriveAction?: () => void;
  onNotionAction?: () => void;
}

// -----------------------------------------------------------------------------
// Component
// -----------------------------------------------------------------------------
export default function Step5({
  user,
  navigateToStep,
  projectId,
  projectPlan,
  refinedIdea,
  markProjectAsCompleted,
  isEditMode, // Keep using this if layout depends on it
  googleDriveConnectedProp,
  notionConnectedProp,
  setGoogleDriveExplanationOpen,
  setNotionExplanationOpen,
  setGoogleDriveExportSuccessOpen,
  setNotionExportSuccessOpen,
  setExportedFolderUrl,
  setNotionExportUrl,
  onGoogleDriveAction: onGoogleDriveActionProp,
  onNotionAction: onNotionActionProp,
}: Step5Props) {
  const { toast } = useToast()
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // ------------------------------------------------------------------------------------------------
  // Local State (Moved to top for proper initialization before useEffect hooks)
  // ------------------------------------------------------------------------------------------------
  const [docStatus, setDocStatus] = useState<Record<string, GenerationStatus>>({});
  const [docContent, setDocContent] = useState<Record<string, SerializedEditorState | null>>({}) // Store Lexical state
  const [activeDocId, setActiveDocId] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isSaving, setIsSaving] = useState(false); // Add saving state for potential future use
  const [isLoadingDocLexicalContent, setIsLoadingDocLexicalContent] = useState<Record<string, boolean>>({}); // For lexical conversion loading
  // State for the download modal
  const [isDownloadModalOpen, setIsDownloadModalOpen] = useState(false);
  const [selectedDocForDownload, setSelectedDocForDownload] = useState<string | null>(null);
  const [selectedDownloadFormat, setSelectedDownloadFormat] = useState("markdown"); // Default format

  const [hasMarkedCompleted, setHasMarkedCompleted] = useState(false)
  const [hasGeneratedAnyDocument, setHasGeneratedAnyDocument] = useState(false)

  const [fetchedDocumentTypes, setFetchedDocumentTypes] = useState<UIDocumentType[]>([]);
  const [isLoadingDocTypes, setIsLoadingDocTypes] = useState(true);
  const [allFetchedProjectDocuments, setAllFetchedProjectDocuments] = useState<any[]>([]);

  // --- State and Ref for Floating Elements ---
  const [floatingAnchorElem, setFloatingAnchorElem] = useState<HTMLDivElement | null>(null);

  // Add state for project name
  const [projectName, setProjectName] = useState<string>("");

  // State for sidebar collapse
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // --- Export/Integration state ---
  // Use prop for initial state, allow local override if needed or rely solely on prop
  const [googleDriveConnected, setGoogleDriveConnected] = useState(googleDriveConnectedProp ?? false);
  const [isExportingGoogleDrive, setIsExportingGoogleDrive] = useState(false);
  const [notionConnected, setNotionConnected] = useState(notionConnectedProp ?? false);
  const [isExportingNotion, setIsExportingNotion] = useState(false);

  // Callback ref to set the anchor element when the content div mounts/unmounts
  const onContentRef = useCallback((node: HTMLDivElement | null) => {
    setFloatingAnchorElem(node); // Update state when the ref changes
  }, []); // Empty dependency array means this callback is stable

  // Key for Lexical editor to force re-render
  const [editorViewKey, setEditorViewKey] = useState(`editor-step5-initial`);

  // Helper to convert Markdown to Lexical's SerializedEditorState
  const convertMarkdownToLexical = async (content: any): Promise<SerializedEditorState> => {
    if (!content) return DEFAULT_LEXICAL_STATE;
    
    try {
      // If content is already a SerializedEditorState
      if (typeof content === 'object' && content.root) {
        return content as SerializedEditorState;
      }

      // If content is a string but represents a JSON SerializedEditorState
      if (typeof content === 'string') {
        try {
          const parsed = JSON.parse(content);
          if (parsed.root && typeof parsed.root === 'object') {
            return parsed as SerializedEditorState;
          }
        } catch {
          // If parsing fails, treat it as markdown
        }
      }

      // Convert markdown to Lexical state
      const tempEditor = createEditor({
        nodes: LEXICAL_NODES,
        onError: (error) => {
          console.error("Lexical conversion error:", error);
        },
      });

      await tempEditor.update(() => {
        const markdown = typeof content === 'string' ? content : '';
        $convertFromMarkdownString(markdown, TRANSFORMERS);
      });

      return tempEditor.getEditorState().toJSON();
    } catch (error) {
      console.error("Error in convertMarkdownToLexical:", error);
      return DEFAULT_LEXICAL_STATE;
    }
  };

  // State to track selection of custom (non-type) documents
  const [activeCustomDoc, setActiveCustomDoc] = useState<any | null>(null);

  // Handler for selecting a document (type or custom instance)
  const handleDocumentSelection = (item: UIDocumentType | any, isCustom: boolean) => {
    if (isCustom) { // Custom document instance
      setActiveCustomDoc(item);
      setActiveDocId(null); // Clear type-based activeDocId
      router.push(`/dashboard/project/${projectId}?selectedDocId=${item.id}`, { scroll: false });
    } else { // Document type
      const docType = item as UIDocumentType;
      setActiveCustomDoc(null); // Clear custom selection
      setActiveDocId(docType.id);

      if (docStatus[docType.id] === "done") {
        const latestInstance = allFetchedProjectDocuments
          .filter(pd => pd.type === docType.id && pd.project_id === projectId)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
        if (latestInstance) {
          router.push(`/dashboard/project/${projectId}?selectedDocId=${latestInstance.id}`, { scroll: false });
        }
      }
    }
  };

  const handleComplete = () => {
    if (!hasMarkedCompleted && markProjectAsCompleted) {
      markProjectAsCompleted()
        .then(() => {
          setHasMarkedCompleted(true);
          window.location.href = "/dashboard/project";
        })
        .catch(err => {
          console.error("Error completing project:", err);
          toast({
            title: "Error completing project",
            description: "There was an error completing your project, but your documents were generated successfully.",
            variant: "destructive"
          });
        });
    } else {
      window.location.href = "/dashboard/project";
    }
  };

  // Effect to sync local connection state if props change
  useEffect(() => {
    setGoogleDriveConnected(googleDriveConnectedProp ?? false);
    console.log(`[Step5 Effect] googleDriveConnectedProp: ${googleDriveConnectedProp}, local googleDriveConnected set to: ${googleDriveConnectedProp ?? false}`);
  }, [googleDriveConnectedProp]);

  useEffect(() => {
    setNotionConnected(notionConnectedProp ?? false);
    console.log(`[Step5 Effect] notionConnectedProp: ${notionConnectedProp}, local notionConnected set to: ${notionConnectedProp ?? false}`);
  }, [notionConnectedProp]);
  // Effect to initialize and react to query parameter for active document
  // This effect ensures the displayed document instance matches the URL's selectedDocId (instance UUID)
  // or sets a default instance and updates the URL accordingly.
  useEffect(() => {
    // Clear custom doc selection each time query or types change
    setActiveCustomDoc(null);

    const queryInstanceId = searchParams.get('selectedDocId');

    // Conditions to wait for necessary data before proceeding
    if (isLoadingDocTypes || (queryInstanceId && allFetchedProjectDocuments.length === 0 && fetchedDocumentTypes.length > 0)) {
      if (isLoadingDocTypes || (queryInstanceId && fetchedDocumentTypes.length > 0 && allFetchedProjectDocuments.length === 0)) {
        return; // Waiting for document types or all project documents to load
      }
    }

    if (queryInstanceId) {
      const targetInstance = allFetchedProjectDocuments.find(doc => doc.id === queryInstanceId);
      if (targetInstance) {
        const typeId = targetInstance.type;
        if (fetchedDocumentTypes.some(dt => dt.id === typeId)) {
          if (activeDocId !== typeId) {
            setActiveDocId(typeId); // Set active type for sidebar highlighting
          }
          // Convert and load content
          setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: true }));
          convertMarkdownToLexical(targetInstance.content).then(lexicalState => {
            setDocContent(prev => ({ ...prev, [typeId]: lexicalState }));
            setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: false }));
            setEditorViewKey(`editor-step5-${typeId}-${Date.now()}`);
          });

          const instanceStatus = targetInstance.status === 'completed' ? 'done' : targetInstance.status as GenerationStatus;
          
          // setDocContent(prev => ({ ...prev, [typeId]: instanceContentProcessed })); // Content set by async conversion
          setDocStatus(prev => ({ ...prev, [typeId]: instanceStatus }));
          return; // Successfully processed instance from URL
        } else {
          // Custom document instance selected: display directly
          setActiveCustomDoc(targetInstance);
          return;
        }
      } else if (!isLoadingDocTypes && (allFetchedProjectDocuments.length > 0 || fetchedDocumentTypes.length > 0)) {
        // Instance ID from URL not found, but types or project documents have loaded.
        console.warn(`Instance ID ${queryInstanceId} from URL not found. Displaying default.`);
        // Let default logic run.
      } else {
          // Still loading initial data.
          return;
      }
    }

    // Defaulting Logic:
    // This runs if:
    // 1. No queryInstanceId was in the URL.
    // 2. The queryInstanceId was invalid (instance not found or its type was unknown).
    if (fetchedDocumentTypes.length > 0) {
      const typeToMakeActive = (activeDocId && fetchedDocumentTypes.some(dt => dt.id === activeDocId))
                               ? activeDocId // Use current active type if valid
                               : fetchedDocumentTypes[0]?.id; // Otherwise, default to the first type

      if (typeToMakeActive) {
        if (activeDocId !== typeToMakeActive) {
          setActiveDocId(typeToMakeActive);
        }

        const latestInstance = allFetchedProjectDocuments
          .filter(pd => pd.type === typeToMakeActive && pd.project_id === projectId)
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];

        if (latestInstance) {
          setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeToMakeActive]: true }));
          convertMarkdownToLexical(latestInstance.content).then(lexicalState => {
            setDocContent(prev => ({ ...prev, [typeToMakeActive]: lexicalState }));
            setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeToMakeActive]: false }));
            setEditorViewKey(`editor-step5-${typeToMakeActive}-${Date.now()}`);
          });
          // setDocContent(prev => ({ ...prev, [typeToMakeActive]: processMarkdownContent(latestInstance.content) }));
          setDocStatus(prev => ({ ...prev, [typeToMakeActive]: latestInstance.status === 'completed' ? 'done' : latestInstance.status as GenerationStatus }));
          
          // If we are defaulting (i.e., no valid queryInstanceId was processed), update URL to reflect the instance being shown.
          if (searchParams.get('selectedDocId') !== latestInstance.id) {
            router.push(`/dashboard/project/${projectId}?selectedDocId=${latestInstance.id}`, { scroll: false });
          }
        } else {
          // No instance exists for this type yet.
          setDocContent(prev => ({ ...prev, [typeToMakeActive]: DEFAULT_LEXICAL_STATE }));
          setDocStatus(prev => ({ ...prev, [typeToMakeActive]: "idle" }));
          // If an invalid queryInstanceId led here, consider clearing it from URL.
          // For now, if no instance, URL won't point to one.
          if (queryInstanceId && searchParams.get('selectedDocId')) {
             // router.push(`/dashboard/project/${projectId}`, { scroll: false }); // Optional: clear invalid ID
          }
        }
      }
    } else if (!isLoadingDocTypes && fetchedDocumentTypes.length === 0) {
        setActiveDocId(null);
    }
  }, [
    searchParams, 
    fetchedDocumentTypes, 
    isLoadingDocTypes, 
    allFetchedProjectDocuments, 
    activeDocId, 
    projectId, 
    router
    // docContent and docStatus are intentionally omitted to prevent infinite loops,
    // as this effect is responsible for setting them based on URL/defaults.
  ]);

  // Fetch document types from database
  useEffect(() => {
    const fetchDocTypesFromDB = async () => {
      setIsLoadingDocTypes(true);
      try {
        const { data, error } = await supabase
          .from("document_types") // Corrected table name
          .select("id, title, icon, cost, description") // Adjust columns as needed
          .eq("active", true) // Assuming you have an 'active' flag
          .order("display_order", { ascending: true }); // Assuming 'display_order' for sorting

        if (error) throw error;

        setFetchedDocumentTypes(data || []);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "An unknown error occurred.";
        console.error("Error fetching document types:", err); // Log the full error object for debugging
        toast({
          title: "Error loading document types",
          description: `Could not load document types: ${errorMessage}. Please try again.`,
          variant: "destructive",
        });
      } finally {
        setIsLoadingDocTypes(false);
      }
    };
    fetchDocTypesFromDB();
  }, [toast]);

  // Fetch all generated documents when component mounts
  useEffect(() => {
    const fetchGeneratedDocuments = async () => {
      if (!projectId || isLoadingDocTypes) return; // Allow running even if fetchedDocumentTypes is empty initially

      try {
        console.log("Fetching generated documents for project:", projectId);
        
        const { data, error } = await supabase
          .from("project_documents")
          .select("*")
          .eq("project_id", projectId)
          .order("created_at", { ascending: false });
        
        if (error) throw error;
        
        console.log("Fetched documents:", data);
        
        setAllFetchedProjectDocuments(data || []); // Store all fetched document instances

        if (data && data.length > 0) {
          // Group documents by type, keeping only the latest version of each type
          const latestDocsByType: Record<string, any> = {};
          
          data.forEach(doc => {
            // If we haven't seen this type yet, or this is newer than what we have
            if (!latestDocsByType[doc.type] || new Date(doc.created_at) > new Date(latestDocsByType[doc.type].created_at)) {
              latestDocsByType[doc.type] = doc;
            }
          });
          
          console.log("Latest docs by type:", latestDocsByType);
          
          // Update document status and content based on latest versions
          const newDocStatus: Record<string, GenerationStatus> = {};
          const newDocContentPromises: Promise<void>[] = [];
          
          Object.keys(latestDocsByType).forEach(type => {
            const doc = latestDocsByType[type];
            newDocStatus[type] = doc.status === "completed" ? "done" : (doc.status as GenerationStatus);
            
            if (doc.content && (doc.status === "completed" || doc.status === "done")) {
              setIsLoadingDocLexicalContent(prev => ({ ...prev, [type]: true }));
              const promise = convertMarkdownToLexical(doc.content).then(lexicalState => {
                setDocContent(prev => ({ ...prev, [type]: lexicalState }));
                setIsLoadingDocLexicalContent(prev => ({ ...prev, [type]: false }));
              }).catch(() => { // Handle conversion error for specific doc
                setDocContent(prev => ({ ...prev, [type]: DEFAULT_LEXICAL_STATE }));
                setIsLoadingDocLexicalContent(prev => ({ ...prev, [type]: false }));
              });
              newDocContentPromises.push(promise);
            } else {
              setDocContent(prev => ({ ...prev, [type]: DEFAULT_LEXICAL_STATE }));
            }
          });

          await Promise.all(newDocContentPromises).then(() => {
            setEditorViewKey(`editor-step5-batch-${Date.now()}`);
          });
          
          setDocStatus(newDocStatus);
          
          const availableDocKeys = Object.keys(latestDocsByType);
          if (availableDocKeys.length > 0) {
            setHasGeneratedAnyDocument(true);
            // activeDocId will be set by the other useEffect based on query params or default
          } else {
            setHasGeneratedAnyDocument(false);
          }
        } else {
          // Initialize all with default if no documents found
          const initialStatus = Object.fromEntries((fetchedDocumentTypes || []).map(d => [d.id, "idle"]));
          const initialContent = Object.fromEntries((fetchedDocumentTypes || []).map(d => [d.id, DEFAULT_LEXICAL_STATE]));
          setDocStatus(initialStatus);
          setDocContent(initialContent);
          setHasGeneratedAnyDocument(false);
          // activeDocId will be set by the other useEffect
        }
      } catch (err) {
        console.error("Error in fetchGeneratedDocuments:", err)
      }
    }
    if (projectId) {
      fetchGeneratedDocuments()
    }
  }, [projectId, fetchedDocumentTypes, isLoadingDocTypes, toast]);

  // Add useEffect to fetch project name when component mounts
  useEffect(() => {
    const fetchProjectName = async () => {
      if (!projectId) return;

      try {
        const { data, error } = await supabase
          .from("projects")
          .select("name")
          .eq("id", projectId)
          .single();

        if (error) {
          console.error("Error fetching project name:", error);
          return;
        }

        if (data && data.name) {
          setProjectName(data.name);
        }
      } catch (err) {
        console.error("Error in fetchProjectName:", err);
      }
    };

    fetchProjectName();
  }, [projectId]);

  // --- Helper: Download All Documents ---
  // Moved inside the component to access state and props
  const handleDownloadAllDocuments = async () => {
    // Only proceed if we have generated at least one document
    if (!hasGeneratedAnyDocument) return;

    try {
      // Create a list of document IDs that have been generated
      const generatedDocIds = Object.entries(docStatus)
        .filter(([_, status]) => status === "done")
        .map(([id]) => id);

      if (generatedDocIds.length === 0) {
        toast({
          title: "No documents to download",
          description: "Generate at least one document first.",
          variant: "destructive",
        });
        return;
      }

      console.log("Downloading documents:", generatedDocIds);

      // Call the API to get the zip file
      const response = await fetch(`/api/projects/${projectId}/download-all`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          documentIds: generatedDocIds,
          format: "markdown"
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Download API error:", response.status, errorText);
        throw new Error(`Failed to download documents: ${response.status} ${errorText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();
      
      if (!blob || blob.size === 0) {
        throw new Error("Received empty file from server");
      }

      console.log("Received blob:", blob.type, blob.size);

      // Get the filename from the Content-Disposition header if available
      let filename = `project-documents.zip`;
      
      // Use project name if available
      if (projectName) {
        filename = `${projectName.replace(/[^a-z0-9]/gi, '-').toLowerCase()}-documents.zip`;
      }
      
      // Try to get filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+?)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      // Create a download link and trigger the download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }, 100);

      toast({
        title: "Download started",
        description: "All generated documents are being downloaded as a zip file.",
      });
    } catch (error) {
      console.error("Error downloading all documents:", error);
      toast({
        title: "Download failed",
        description: error instanceof Error ? error.message : "Could not download the documents. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Export project documents to Notion
  const handleExportToNotion = async () => {
    setIsExportingNotion(true);
    if (!hasGeneratedAnyDocument) {
      setIsExportingNotion(false);
      return;
    }

    console.log("[Step5] handleExportToNotion: Prop notionConnectedProp:", notionConnectedProp, "Local notionConnected state:", notionConnected);
    console.log("[Step5] handleExportToNotion: setNotionExplanationOpen prop exists:", !!setNotionExplanationOpen);

    if (!notionConnectedProp) { // Use prop directly for the check
      console.log("[Step5] Notion not connected (checked via prop). Delegating to parent.");
      if (onNotionActionProp) {
        onNotionActionProp();
        setIsExportingNotion(false);
        return;
      }
      if (setNotionExplanationOpen) {
        setNotionExplanationOpen(true);
        setIsExportingNotion(false);
        return;
      }
    }

    console.log("[Step5] Notion connected. Proceeding with export API call.");

    const documentInstanceIdsToExport = allFetchedProjectDocuments
      .filter(doc => doc.status === 'completed') // Assuming 'completed' is the final DB status
      .map(doc => doc.id);

    try {
      const response = await fetch(`/api/projects/${projectId}/export-to-notion`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          documentIds: documentInstanceIdsToExport,
          projectName: projectName,
        }),
      });
      if (!response.ok) {
        console.error("Notion export API call failed with status:", response.status);
        const errorText = await response.text().catch(() => "Could not read error response.");
        throw new Error(`Export to Notion failed: ${response.status} ${errorText}`);
      }
      console.log("Notion export API call successful. Status:", response.status);
      const result = await response.json();
      console.log("Notion Export API Response Data:", result);

      if (result.pageUrl && setNotionExportSuccessOpen && setNotionExportUrl) {
        console.log("Attempting to open Notion success dialog with URL:", result.pageUrl);
        setNotionExportUrl(result.pageUrl);
        setNotionExportSuccessOpen(true);
      } else {
        console.log(
          "Notion export: Dialog not shown. Missing pageUrl, setNotionExportSuccessOpen, or setNotionExportUrl.",
          "pageUrl:", result.pageUrl,
          "setNotionExportSuccessOpen exists:", !!setNotionExportSuccessOpen,
          "setNotionExportUrl exists:", !!setNotionExportUrl
        );
        toast({
          title: "Export to Notion Successful",
          description: "Documents exported. You can view them in Notion."
        });
      }
    } catch (err: any) {
      // setNotionConnected(false); // Connection status is managed by page.tsx
      console.error("Export to Notion error:", err);
      toast({ title: "Export to Notion Failed", description: err.message || "Please check your connection and try again.", variant: "destructive" });
    } finally {
      setIsExportingNotion(false);
    }
  };

  // Export project documents to Google Drive
  const handleExportToGoogleDrive = async () => {
    setIsExportingGoogleDrive(true);
    if (!hasGeneratedAnyDocument) {
      setIsExportingGoogleDrive(false);
      return;
    }

    console.log("[Step5] handleExportToGoogleDrive: Prop googleDriveConnectedProp:", googleDriveConnectedProp, "Local googleDriveConnected state:", googleDriveConnected);
    console.log("[Step5] handleExportToGoogleDrive: setGoogleDriveExplanationOpen prop exists:", !!setGoogleDriveExplanationOpen);

    if (!googleDriveConnectedProp) { // Use prop directly for the check
      console.log("[Step5] Google Drive not connected (checked via prop). Delegating to parent.");
      if (onGoogleDriveActionProp) {
        onGoogleDriveActionProp();
        setIsExportingGoogleDrive(false);
        return;
      }
      if (setGoogleDriveExplanationOpen) {
        setGoogleDriveExplanationOpen(true);
        setIsExportingGoogleDrive(false);
        return;
      }
    }

    console.log("[Step5] Google Drive connected. Proceeding with export API call.");

    const documentInstanceIdsToExport = allFetchedProjectDocuments
      .filter(doc => doc.status === 'completed') // Assuming 'completed' is the final DB status
      .map(doc => doc.id);

    try {
      const response = await fetch(`/api/projects/${projectId}/export-to-drive`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          documentIds: documentInstanceIdsToExport,
          projectName: projectName,
        }),
      });
      if (!response.ok) {
        console.error("Google Drive export API call failed with status:", response.status);
        const errorText = await response.text().catch(() => "Could not read error response.");
        throw new Error(`Export to Google Drive failed: ${response.status} ${errorText}`);
      }
      console.log("Google Drive export API call successful. Status:", response.status);
      const result = await response.json();
      console.log("Google Drive Export API Response Data:", result);

      if (result.folderUrl && setGoogleDriveExportSuccessOpen && setExportedFolderUrl) {
        console.log("Attempting to open Google Drive success dialog with URL:", result.folderUrl);
        setExportedFolderUrl(result.folderUrl);
        setGoogleDriveExportSuccessOpen(true);
      } else {
        console.log(
          "Google Drive export: Dialog not shown. Missing folderUrl, setGoogleDriveExportSuccessOpen, or setExportedFolderUrl.",
          "folderUrl:", result.folderUrl,
          "setGoogleDriveExportSuccessOpen exists:", !!setGoogleDriveExportSuccessOpen,
          "setExportedFolderUrl exists:", !!setExportedFolderUrl
        );
        toast({
          title: "Export to Google Drive Successful",
          description: "Documents exported. You can view them in Google Drive."
        });
      }
    } catch (err: any) {
      console.error("Export to Google Drive error:", err);
      toast({ title: "Export to Google Drive Failed", description: err.message || "Please check your connection and try again.", variant: "destructive" });
    } finally {
      setIsExportingGoogleDrive(false);
    }
  };


  // Debug useEffect
  useEffect(() => {
    if (activeDocId) {
      // console.log(`Active document changed to: ${activeDocId}`);
      console.log(`Content available: ${!!docContent[activeDocId]}`);
      // console.log(`Content preview: ${JSON.stringify(docContent[activeDocId]).substring(0, 100)}...`);
      console.log(`Document status: ${docStatus[activeDocId]}`);
    }
  }, [activeDocId, docContent, docStatus]);

  // ------------------------------------------------------------------------------------------------
  // Helpers
  // ------------------------------------------------------------------------------------------------
  const creditsRemaining = user?.credits_remaining ?? 0
  const generationInProgress = useMemo(() => Object.values(docStatus).some((s) => s === "generating"), [docStatus])

  // Documents linked to project but not among fetched types
  const customDocuments = allFetchedProjectDocuments.filter(pd =>
    !fetchedDocumentTypes.some(dt => dt.id === pd.type)
  );

  // Function to handle the actual download based on format
  const initiateDownload = async (docId: string, format: string) => {
     if (!docId || docStatus[docId] !== "done") {
      toast({ title: "Download Failed", description: "Document is not ready for download.", variant: "destructive" });
      return;
    }
    const docDetails = fetchedDocumentTypes.find(d => d.id === docId);
    try {
      let contentToDownload = "";
      let filename = "";

      // Fetch the raw content regardless of desired format
      const { data, error } = await supabase
        .from("project_documents")
        .select("content")
        .eq("project_id", projectId)
        .eq("type", docId)
        .single();

      if (error || !data?.content) {
        throw error || new Error("No content found");
      }

      // Determine content and filename based on format
      if (format === "markdown") {
        // Assume content in DB is markdown or can be stringified to markdown
         if (typeof data.content === 'string') {
           contentToDownload = data.content;
         } else if (typeof data.content === 'object') {
           // Fallback to stringifying if not a string - might need proper conversion
           contentToDownload = JSON.stringify(data.content, null, 2); // Pretty print for objects
         } else {
           // Handle other potential types or just use default
           console.warn("Unexpected content type for markdown download:", typeof data.content);
           contentToDownload = typeof data.content === 'string' ? data.content : JSON.stringify(data.content);
         }
         filename = `${docDetails?.title.replace(/\s+/g, "-").toLowerCase() || docId}.md`;

      } else if (format === "pdf") {
          // --- Placeholder for PDF download logic ---
          // This would likely require a separate API endpoint call or client-side rendering library
          // For now, we'll just show a toast that it's not supported.
          toast({ title: "PDF Download Not Supported", description: "PDF download is not yet implemented.", variant: "info" });
          setIsDownloadModalOpen(false); // Close modal
          return; // Exit function
      } else {
        // Unknown format
        toast({ title: "Unsupported Format", description: `Download format "${format}" is not supported.`, variant: "destructive" });
        setIsDownloadModalOpen(false); // Close modal
        return; // Exit function
      }

      const blob = new Blob([contentToDownload], { type: `text/${format};charset=utf-8` }); // Adjust mime type for format
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast({ title: "Download Started", description: `${filename}` });

    } catch (e) {
      console.error("Error downloading document:", e);
      toast({
        title: "Download Failed",
        description: "Could not download the document",
        variant: "destructive"
      });
    } finally {
        setIsDownloadModalOpen(false); // Always close modal after attempt
        setSelectedDocForDownload(null); // Clear selection
        setSelectedDownloadFormat("markdown"); // Reset format selection
    }
  };

  // Download document button handler - now opens modal
  const handleDownloadDocumentClick = () => {
    if (!activeDocId || docStatus[activeDocId] !== "done") return; // Only open modal if document is done

    setSelectedDocForDownload(activeDocId);
    setIsDownloadModalOpen(true);
  };

  // Handle format selection and trigger download
  const handleFormatSelection = (format: string) => {
    setSelectedDownloadFormat(format);
  };

  const handleModalDownload = () => {
      if (selectedDocForDownload && selectedDownloadFormat) {
          initiateDownload(selectedDocForDownload, selectedDownloadFormat);
      }
  };

  // Download document as markdown file
  const handleDownloadDocument = async () => {
    if (!activeDocId || !docContent[activeDocId]) return;
    
    try {
      // Fetch the raw markdown content from the database
      const { data, error } = await supabase
        .from("project_documents")
        .select("content")
        .eq("project_id", projectId)
        .eq("type", activeDocId)
        .single();
      
      if (error) throw error;
      
      // Get the markdown content
      let markdownContent = "";
      if (data && data.content) {
        if (typeof data.content === 'string') {
          markdownContent = data.content;
        } else if (typeof data.content === 'object') {
          markdownContent = JSON.stringify(data.content);
        }
      }
      
      // Create download
      const blob = new Blob([markdownContent], { type: "text/markdown;charset=utf-8" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      const docType = fetchedDocumentTypes.find(d => d.id === activeDocId);
      const filename = `${docType?.title.replace(/\s+/g, "-").toLowerCase() || activeDocId}.md`;
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast({ title: "Download Started", description: `${filename}` });
    } catch (e) {
      console.error("Error downloading document:", e);
      toast({ 
        title: "Download Failed", 
        description: "Could not download the document", 
        variant: "destructive" 
      });
    }
  };

  // Regenerate document - updated to work with markdown content
  const handleRegenerateDocument = async () => {
    if (!activeDocId || docStatus[activeDocId] === "generating" || generationInProgress) return;

    const docDetails = fetchedDocumentTypes.find(d => d.id === activeDocId);
    setIsGenerating(true); // Set global generating flag
    setDocStatus(prev => ({ ...prev, [activeDocId]: "generating" }));

    try {
      const res = await fetch(`/api/projects/${projectId}/documents`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: 'include',
        body: JSON.stringify({ selectedDocuments: [activeDocId], projectPlan, refinedIdea, userId: user?.id }), // Include refinedIdea and userId
      });

      if (!res.ok) {
         const errorData = await res.json().catch(() => ({ message: "Unknown error" }));
         throw new Error(errorData.message || `Failed to regenerate document (${res.status})`);
      }
      // Expecting the API to return the markdown content upon regeneration
      // Assuming API returns { document: { id: 'uuid', type: 'type_id', content: '...' } }
      const { document: regeneratedDocInstance } = await res.json();

      if (regeneratedDocInstance && regeneratedDocInstance.id && regeneratedDocInstance.content) {
        const instanceId = regeneratedDocInstance.id; // UUID
        const typeId = regeneratedDocInstance.type; // Should match activeDocId (type)
        
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: true }));
        const lexicalState = await convertMarkdownToLexical(regeneratedDocInstance.content);
        setDocContent(prev => ({ ...prev, [typeId]: lexicalState }));
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: false }));
        setEditorViewKey(`editor-step5-${typeId}-${Date.now()}`);

        setDocStatus(prev => ({ ...prev, [typeId]: "done" }));
        // Update allFetchedProjectDocuments with the regenerated document
        setAllFetchedProjectDocuments(prevDocs => 
          prevDocs.map(pd => pd.id === instanceId ? { ...pd, ...regeneratedDocInstance, status: 'completed' } : pd)
        );

        toast({ title: "Document Regenerated", description: `${docDetails?.title} regenerated.` });
        router.push(`/dashboard/project/${projectId}?selectedDocId=${instanceId}`, { scroll: false });
      } else {
        throw new Error("Invalid response from regeneration API. Missing document data.");
      }

    } catch (err: any) {
      console.error("Regeneration failed:", err);
      setDocStatus(prev => ({ ...prev, [activeDocId]: "error" }));
      toast({
        title: "Regeneration failed",
        description: err.message || "Please try again.",
        variant: "destructive"
      });
    } finally {
       setIsGenerating(false); // Clear global generating flag
    }
  };

  // Load single document content - updates state with SerializedEditorState
  const loadDocContent = async (docId: string) => {
    if (!projectId) return;

    try {
      // Set loading state for this specific document
      setIsLoadingDocLexicalContent(prev => ({ ...prev, [docId]: true }));
      console.log(`Loading content for document type: ${docId}`);
      
      const { data, error } = await supabase
        .from("project_documents")
        .select("content, status, created_at")
        .eq("project_id", projectId)
        .eq("type", docId)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (data && data.length > 0) {
        const doc = data[0];
        const currentStatus = doc.status === "completed" ? "done" : (doc.status as GenerationStatus);
        console.log(`Document ${docId} found with status: ${currentStatus}`);

        setDocStatus(prev => ({ ...prev, [docId]: currentStatus }));

        if (doc.content && (currentStatus === "done")) {
          try {
            const lexicalState = await convertMarkdownToLexical(doc.content as string);
            setDocContent(prev => ({ ...prev, [docId]: lexicalState }));
            setEditorViewKey(`editor-step5-${docId}-${Date.now()}`);
          } catch (e) {
            console.error(`Error processing loaded content for ${docId}:`, e);
            setDocContent(prev => ({ ...prev, [docId]: DEFAULT_LEXICAL_STATE }));
          }
        } else if (currentStatus !== "done") {
          // Ensure content is default if status is not 'done'
          setDocContent(prev => ({ ...prev, [docId]: DEFAULT_LEXICAL_STATE }));
        }
      } else {
        console.log(`No document found for type: ${docId}`);
        // Ensure status is idle and content is default if no record found
        setDocStatus(prev => ({ ...prev, [docId]: "idle" }));
        setDocContent(prev => ({ ...prev, [docId]: DEFAULT_LEXICAL_STATE }));
      }
    } catch (err) {
      console.error(`Error loading document ${docId}:`, err);
      toast({
        title: "Error loading document",
        description: "Could not load the document content.",
        variant: "destructive"
      });
    } finally {
      // Clear loading state
      setIsLoadingDocLexicalContent(prev => ({ ...prev, [docId]: false }));
    }
  };

  // Debug flag to track function calls (set to true to see all calls)
  const DEBUG_DUPLICATE_CALLS = true;
  
  // Global request counter to track API calls
  const [requestCounter, setRequestCounter] = useState(0);
  
  // Enhance handleGenerateDocument to better handle content loading
  const handleGenerateDocument = async (docId: string) => {
    if (DEBUG_DUPLICATE_CALLS) {
      console.log(`🔍 handleGenerateDocument called for: ${docId} at ${new Date().toISOString()}`);
      console.log(`🔍 Current docStatus[${docId}]:`, docStatus[docId]);
      console.log(`🔍 generationInProgress:`, generationInProgress);
      console.log('🔍 Stack trace:', new Error().stack);
    }
    
    // Prevent generation calls for existing instance IDs
    if (!fetchedDocumentTypes.some((d) => d.id === docId)) {
      console.warn(`handleGenerateDocument called with non-type id: ${docId}`);
      return;
    }
    if (docStatus[docId] === "generating" || generationInProgress) {
      console.log(`❌ Ignoring duplicate call for ${docId} - already generating`);
      return;
    }
    
    // Add logging to track duplicate calls
    console.log(`✅ Processing generation request for: ${docId}`);

    // Credits check
    const docType = fetchedDocumentTypes.find((d) => d.id === docId);
    if (!docType || creditsRemaining < docType.cost) {
      toast({
        title: "Insufficient credits",
        description: "You don't have enough credits to generate this document.",
        variant: "destructive",
      })
      return
    }

    setIsGenerating(true); // Set global flag
    setDocStatus(prev => ({ ...prev, [docId]: "generating" }));
    setActiveDocId(docId); // Set active document while generating
    setDocContent(prev => ({ ...prev, [docId]: DEFAULT_LEXICAL_STATE })); // Clear content while generating

    try {
      const currentCount = requestCounter + 1;
      setRequestCounter(currentCount);
      const requestId = `${docId}-${Date.now()}-${currentCount}`;
      console.log(`🚀 Making API request #${currentCount} for ${docId} (Request ID: ${requestId})`);
      
      const res = await fetch(`/api/projects/${projectId}/documents`, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "X-Request-ID": requestId // Add custom header to track requests
        },
        credentials: 'include',
        body: JSON.stringify({
          selectedDocuments: [docId],
          projectPlan,
          refinedIdea,
          userId: user?.id,
          requestId, // Add to body as well
        }),
      });
      
      console.log(`📡 API response received for ${docId} (Request ID: ${requestId})`, res.status);

      if (!res.ok) {
         const errorData = await res.json().catch(() => ({ message: "Unknown error" }));
         throw new Error(errorData.message || `Failed to trigger generation (${res.status})`);
      }
      
      // Enhanced response handling for various API shapes
      const responseJson = await res.json();

      // Handle simple success response shape { success, content, documentType }
      if (responseJson.success && typeof responseJson.content === 'string') {
        console.log("Legacy API shape detected, processing content from 'content' field.");
        const typeId = responseJson.documentType || docId;
        const rawContent = responseJson.content;
        
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: true }));
        const lexicalState = await convertMarkdownToLexical(rawContent);
        setDocContent(prev => ({ ...prev, [typeId]: lexicalState }));
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: false }));
        setEditorViewKey(`editor-step5-${typeId}-${Date.now()}`);

        setDocStatus(prev => ({ ...prev, [typeId]: "done" }));

        // Fetch the latest document instance from DB to get its ID
        try {
          const { data: latest, error: fetchError } = await supabase
            .from("project_documents")
            .select("id, type, content, status, created_at")
            .eq("project_id", projectId)
            .eq("type", typeId)
            .order("created_at", { ascending: false })
            .limit(1);

          if (!fetchError && latest && latest.length > 0) {
            const inst = latest[0];
            // Update state array
            setAllFetchedProjectDocuments(prev =>
              [...prev.filter(pd => pd.id !== inst.id), { ...inst, status: 'completed' }]
            );
            // Update URL to the new instance
            router.push(`/dashboard/project/${projectId}?selectedDocId=${inst.id}`, { scroll: false });
          }
        } catch (e) {
          console.error("Error fetching latest instance after legacy response:", e);
        }

        toast({ title: "Document Generated", description: `${docType?.title || typeId} generated successfully.` });
        setIsGenerating(false);
        return;
      }

      let generatedDocInstance = responseJson?.document;
      // Fallbacks: try to find the document instance in other response keys
      if (
        !generatedDocInstance &&
        responseJson &&
        typeof responseJson === "object"
      ) {
        // Some APIs may return { projectDocuments: [{...}] }
        if (
          Array.isArray(responseJson.projectDocuments) &&
          responseJson.projectDocuments.length > 0 &&
          responseJson.projectDocuments[0].id &&
          responseJson.projectDocuments[0].content
        ) {
          generatedDocInstance = responseJson.projectDocuments[0];
        }
      }
      // Insert new fallback for { data: { id, content, ... } }
      else if (
        responseJson.data &&
        typeof responseJson.data === "object" &&
        responseJson.data.id &&
        responseJson.data.content
      ) {
        generatedDocInstance = responseJson.data;
      }
      // Check if we have a valid instance now
      if (generatedDocInstance && generatedDocInstance.id && generatedDocInstance.content) {
        const instanceId = generatedDocInstance.id; // UUID
        const typeId = generatedDocInstance.type; // Should match the input docId (type)

        console.log(`Received content for ${typeId} (instance ${instanceId}), processing...`);
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: true }));
        const lexicalState = await convertMarkdownToLexical(generatedDocInstance.content);
        setDocContent(prev => ({ ...prev, [typeId]: lexicalState }));
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: false }));
        setEditorViewKey(`editor-step5-${typeId}-${Date.now()}`);
        
        setDocStatus(prev => ({ ...prev, [typeId]: "done" }));
        
        // Update allFetchedProjectDocuments with the new document
        setAllFetchedProjectDocuments(prevDocs => [...prevDocs.filter(pd => pd.id !== instanceId), { ...generatedDocInstance, status: 'completed' }]);
        
        router.push(`/dashboard/project/${projectId}?selectedDocId=${instanceId}`, { scroll: false });
        toast({ title: "Document Generated", description: `${docType.title} generated successfully.` });
      } else if (
        Array.isArray(responseJson.projectDocuments) &&
        responseJson.projectDocuments.length > 0 &&
        responseJson.projectDocuments[0].id &&
        responseJson.projectDocuments[0].content
      ) {
        // Fallback: treat projectDocuments[0] as the instance
        const fallbackInstance = responseJson.projectDocuments[0];
        const instanceId = fallbackInstance.id;
        const typeId = fallbackInstance.type;
        
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: true }));
        const lexicalState = await convertMarkdownToLexical(fallbackInstance.content);
        setDocContent(prev => ({ ...prev, [typeId]: lexicalState }));
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: false }));
        setEditorViewKey(`editor-step5-${typeId}-${Date.now()}`);
        setDocStatus(prev => ({ ...prev, [typeId]: "done" }));
        setAllFetchedProjectDocuments(prevDocs => [...prevDocs.filter(pd => pd.id !== instanceId), { ...fallbackInstance, status: 'completed' }]);
        router.push(`/dashboard/project/${projectId}?selectedDocId=${instanceId}`, { scroll: false });
        toast({ title: "Document Generated", description: `${docType.title} generated successfully.` });
      } else if (
        responseJson.data &&
        typeof responseJson.data === "object" &&
        responseJson.data.id &&
        responseJson.data.content
      ) {
        // New fallback: { data: { id, content } }
        const fallbackInstance = responseJson.data;
        const instanceId = fallbackInstance.id;
        const typeId = fallbackInstance.type || docId;

        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: true }));
        const lexicalState = await convertMarkdownToLexical(fallbackInstance.content);
        setDocContent(prev => ({ ...prev, [typeId]: lexicalState }));
        setIsLoadingDocLexicalContent(prev => ({ ...prev, [typeId]: false }));
        setEditorViewKey(`editor-step5-${typeId}-${Date.now()}`);
        setDocStatus(prev => ({ ...prev, [typeId]: "done" }));
        setAllFetchedProjectDocuments(prevDocs => [...prevDocs.filter(pd => pd.id !== instanceId), { ...fallbackInstance, status: 'completed' }]);
        router.push(`/dashboard/project/${projectId}?selectedDocId=${instanceId}`, { scroll: false });
        toast({ title: "Document Generated", description: `${docType.title} generated successfully.` });
      } else {
        console.error(`Generation API for ${docId} did not return expected document instance data. Response was:`, responseJson);
        setDocStatus(prev => ({ ...prev, [docId]: "error" }));
        toast({ title: "Generation Incomplete", description: "Could not retrieve generated document details.", variant: "destructive" });
        // Fallback to load the latest content from DB
        await loadDocContent(docId);
        return;
      }

      const isFirstGeneration = !hasGeneratedAnyDocument;
      setHasGeneratedAnyDocument(true);

      if (isFirstGeneration && !hasMarkedCompleted && markProjectAsCompleted) {
        try {
          await markProjectAsCompleted();
          setHasMarkedCompleted(true);
          console.log("Project marked as completed after first document generation");
        } catch (err) {
          console.error("Failed to mark project as completed:", err);
        }
      }
    } catch (err: any) {
      console.error("Generation failed:", err);
      setDocStatus(prev => ({ ...prev, [docId]: "error" }));
      toast({ title: "Generation failed", description: err.message || "Please try again.", variant: "destructive" });
    } finally {
      setIsGenerating(false); // Clear global flag
    }
  }

  // Real-time listener - needs to handle SerializedEditorState
  useEffect(() => {
    if (!projectId) return

    const channel = supabase
      .channel(`doc-updates-${projectId}`) // Unique channel per project
      .on(
        "postgres_changes",
        { event: "UPDATE", schema: "public", table: "project_documents", filter: `project_id=eq.${projectId}` },
        async (payload) => {
          console.log("Real-time update received:", payload.new);
          const { type, status, content } = payload.new as { type: string; status: string; content: any } // Use 'any' for content initially
          const mappedStatus = status === "completed" ? "done" : (status as GenerationStatus);

          setDocStatus((prev) => ({ ...prev, [type]: mappedStatus }));

          if (mappedStatus === "done" && content) {
             try {
               setIsLoadingDocLexicalContent(prev => ({ ...prev, [type]: true }));
               convertMarkdownToLexical(content as string).then(lexicalState => {
                 setDocContent((prev) => ({ ...prev, [type]: lexicalState }));
                 setIsLoadingDocLexicalContent(prev => ({ ...prev, [type]: false }));
                 setEditorViewKey(`editor-step5-${type}-${Date.now()}`);
               });

               // Mark project completed on first 'done' status via real-time
               const anyDocDone = Object.values(docStatus).some(s => s === 'done') || mappedStatus === 'done';
               if (anyDocDone && !hasMarkedCompleted && markProjectAsCompleted) {
                 try {
                    // Ensure hasGeneratedAnyDocument is also set if not already
                    if (!hasGeneratedAnyDocument) setHasGeneratedAnyDocument(true);
                   await markProjectAsCompleted();
                   setHasMarkedCompleted(true);
                   console.log("Project marked as completed after real-time document update");
                 } catch (err) {
                   console.error("Failed to mark project as completed via real-time:", err);
                 }
               }
             } catch (e) {
                console.error(`Error processing real-time content for ${type}:`, e);
                setDocContent(prev => ({ ...prev, [type]: DEFAULT_LEXICAL_STATE }));
             }
          } else if (mappedStatus !== 'done') {
             // Reset content if status is not 'done'
             setDocContent((prev) => ({ ...prev, [type]: DEFAULT_LEXICAL_STATE }));
          }
        }
      )
      .subscribe((status, err) => {
         if (status === 'SUBSCRIBED') {
           console.log(`Subscribed to real-time updates for project ${projectId}`);
         }
         if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
           console.error(`Real-time subscription error for project ${projectId}:`, err || status);
           // Optionally try to resubscribe or notify user
         }
      });

    return () => {
      console.log(`Unsubscribing from real-time updates for project ${projectId}`);
      supabase.removeChannel(channel);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId, hasMarkedCompleted, markProjectAsCompleted]) // Dependencies adjusted


  // ------------------------------------------------------------------------------------------------
  // Render helpers
  // ------------------------------------------------------------------------------------------------
  // --- Save Document Content Handler (accepts editor state) ---
  const saveDocumentContent = async (editorStateToSave: SerializedEditorState | null) => {
    const currentInstanceId = searchParams.get('selectedDocId');
    if (!currentInstanceId) {
      toast({ 
        title: "Cannot save", 
        description: "No document instance selected for saving.", 
        variant: "destructive" 
      });
      return;
    }
    if (!editorStateToSave) {
      toast({
        title: "Cannot save",
        description: "No content available for the active document.",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);
    try {
      let markdownContentToSave: string | undefined;
      
      // Convert SerializedEditorState to Markdown string
      const { createEditor } = await import('lexical'); // Ensure createEditor is imported if not already at top
      const tempEditor = createEditor({
        namespace: 'tempMarkdownConversionStep5', // Unique namespace
        onError: (error) => {
          console.error("Markdown conversion error (Step5):", error);
        },
        nodes: LEXICAL_NODES // Ensure LEXICAL_NODES is defined in Step5.tsx
      });

      if (editorStateToSave && typeof editorStateToSave === 'object' && editorStateToSave.root) {
        tempEditor.setEditorState(tempEditor.parseEditorState(editorStateToSave));
        await tempEditor.update(() => { // Make sure this update is awaited if $convertToMarkdownString is async or relies on async state
          markdownContentToSave = $convertToMarkdownString(TRANSFORMERS);
        });
      } else {
        console.error("Invalid SerializedEditorState provided for Markdown conversion (Step5).");
        toast({ title: "Save Error", description: "Invalid document format for saving.", variant: "destructive" });
        setIsSaving(false);
        return;
      }

      if (markdownContentToSave === undefined) {
        console.error("Failed to prepare content for saving (Step5). Content is undefined.");
        toast({ title: "Save Error", description: "Could not prepare document for saving.", variant: "destructive" });
        setIsSaving(false);
        return;
      }

      const { error } = await supabase
        .from("project_documents")
        .update({
          content: markdownContentToSave,
          status: 'completed',
          updated_at: new Date().toISOString(),
        })
        .eq("id", currentInstanceId);

      if (error) throw error;
      
      setAllFetchedProjectDocuments(prevDocs => 
        prevDocs.map(doc => 
          doc.id === currentInstanceId 
            ? {...doc, content: markdownContentToSave, updated_at: new Date().toISOString(), status: 'completed'} 
            : doc
      ));
      
      toast({ title: "Document Saved", description: `${fetchedDocumentTypes.find(d => d.id === activeDocId)?.title || 'Document'} saved successfully.` });
      console.log("Document saved as markdown (Step5)");
    } catch (err: any) {
      console.error("Failed to save document:", err);
      toast({ 
        title: "Save Failed", 
        description: err.message || "Could not save document.", 
        variant: "destructive" 
      });
    } finally {
      setIsSaving(false);
    }
  };


  // ------------------------------------------------------------------------------------------------
  // JSX
  // ------------------------------------------------------------------------------------------------
return (
    <TooltipProvider>
      <div className="space-y-6 w-full min-h-screen">
      <Card className="shadow-sm rounded-none border-none">
        <CardContent className="p-0 flex flex-col h-[calc(100vh-66px)]"> {/* Remove padding from CardContent; flex column for full height */}
          <div className="flex gap-0 flex-1 min-h-0"> {/* Remove gap; make main area grow and shrink */}
            <DocumentSidebar
              isSidebarCollapsed={isSidebarCollapsed}
              setIsSidebarCollapsed={setIsSidebarCollapsed}
              isLoadingDocTypes={isLoadingDocTypes}
              fetchedDocumentTypes={fetchedDocumentTypes}
              customDocuments={customDocuments}
              activeDocId={activeDocId}
              activeCustomDoc={activeCustomDoc}
              docStatus={docStatus}
              generationInProgress={generationInProgress}
              creditsRemaining={creditsRemaining}
              isEditMode={isEditMode ?? false}
              hasGeneratedAnyDocument={hasGeneratedAnyDocument}
              isExportingGoogleDrive={isExportingGoogleDrive}
              googleDriveConnected={googleDriveConnected}
              isExportingNotion={isExportingNotion}
              notionConnected={notionConnected}
              onDocumentSelect={handleDocumentSelection}
              onGenerateDocument={handleGenerateDocument}
              onDownloadDocument={handleDownloadDocument} // For individual downloads from sidebar
              onDownloadAllDocuments={handleDownloadAllDocuments}
              onExportToGoogleDrive={handleExportToGoogleDrive}
              onExportToNotion={handleExportToNotion}
              projectId={projectId}
              allFetchedProjectDocuments={allFetchedProjectDocuments}
              toast={toast}
            />

            {/* -------------------------------------------------------------------- */}
            {/* Main Content Area */}
            {/* -------------------------------------------------------------------- */}
            <main className="flex flex-1 flex-col min-h-0">
              <DocumentViewer
                activeDocId={activeDocId}
                activeCustomDoc={activeCustomDoc}
                docContent={docContent}
                docStatus={docStatus}
                isLoadingDocLexicalContent={isLoadingDocLexicalContent}
                editorViewKey={editorViewKey}
                onContentRef={onContentRef}
                DEFAULT_LEXICAL_STATE={DEFAULT_LEXICAL_STATE}
                floatingAnchorElem={floatingAnchorElem}
                onGenerateDocument={handleGenerateDocument}
                generationInProgress={generationInProgress}
                onSaveDocument={saveDocumentContent}
                isSaving={isSaving} // Pass the saving state to DocumentViewer
              />
            </main>
          </div>
        </CardContent>
      </Card>

      <DownloadModal
        isOpen={isDownloadModalOpen}
        onOpenChange={setIsDownloadModalOpen}
        selectedDocForDownload={selectedDocForDownload}
        selectedDownloadFormat={selectedDownloadFormat}
        onFormatSelection={handleFormatSelection}
        onModalDownload={handleModalDownload}
      />
      </div>
    </TooltipProvider>
  )
}
