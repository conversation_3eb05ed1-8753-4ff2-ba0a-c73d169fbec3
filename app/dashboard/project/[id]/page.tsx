"use client";

import dynamic from "next/dynamic";
import { useChat } from "./components/InlineChatProvider";
const ChatProvider = dynamic(() => import("./components/InlineChatProvider").then(m => m.InlineChatProvider), {
  ssr: false,
  loading: () => (
    <div className="p-2 flex justify-center">
      <Loader2 className="h-4 w-4 animate-spin" />
    </div>
  ),
});
// Lazy load chat sidebar - only when opened using next/dynamic
const InlineChatSidebar = dynamic(() => import("./components/InlineChatProvider").then(m => m.InlineChatSidebar), {
  ssr: false,
  loading: () => (
    <div className="fixed right-0 top-0 h-full w-[400px] bg-background border-l flex items-center justify-center">
      <Loader2 className="h-6 w-6 animate-spin" />
    </div>
  ),
});
import { MessageSquare } from "lucide-react";


import { useState, useRef, useEffect, memo, useMemo, useCallback } from "react";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/auth/auth-provider";
import { supabase } from "@/lib/supabase-client";

// Import custom hooks
import { useProjectData } from './hooks/useProjectData';
import { useOAuthConnections } from './hooks/useOAuthConnections';
import { useProjectDocuments } from './hooks/useProjectDocuments';
import { useExportHandlers } from './hooks/useExportHandlers';
// Removed Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, Progress from here as they are now in ProjectHeader
import { Button } from "@/components/ui/button"; // Keep Button if used elsewhere
import { ArrowDown, ArrowLeft, CheckCircle2, Loader2, AlertCircle, ChevronRight, Slash, FileText } from "lucide-react"; // Removed Pencil, Check, X as they are in ProjectHeader
// Removed format from here as it's in ProjectHeader
//import { Accordion, AccordionContent, AccordionItem, AccordionTrigger,} from "@/components/ui/accordion";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"; // Added Alert components
import { Textarea } from "@/components/ui/textarea";
import { CompletedStepsAccordion } from './components/CompletedStepsAccordion'; // Import the new accordion component
import { format } from "date-fns";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ExternalLink, FolderOpen } from "lucide-react";

// Dynamic step imports - load only when needed using next/dynamic
const Step1 = dynamic(() => import("./steps/Step1"), {
  loading: () => <LoadingComponent />,
  ssr: false,
});
const Step2 = dynamic(() => import("./steps/Step2"), {
  loading: () => <LoadingComponent />,
  ssr: false,
});
const Step3 = dynamic(() => import("./steps/Step3"), {
  loading: () => <LoadingComponent />,
  ssr: false,
});
const Step4 = dynamic(() => import("./steps/Step4"), {
  loading: () => <LoadingComponent />,
  ssr: false,
});
// Import Step1Ref for typing - this is lightweight
import { Step1Ref } from "./steps/Step1";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useProjectStore } from '@/lib/store/project';
import { extractProductDetails } from '../utils/extractProductDetails';
import LoadingComponent from "./loading"; // Import the loading component
import { ProjectHeader } from './components/ProjectHeader'; // Import the new header component
import { CurrentStepDisplay } from './components/CurrentStepDisplay'; // Import the new step display component

// Lazy load dialog components for better performance

const GoogleDriveExportSuccessDialog = dynamic(() => import('./components/dialogs/GoogleDriveExportSuccessDialog').then(m => m.GoogleDriveExportSuccessDialog), {
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
      <Loader2 className="h-4 w-4 animate-spin" />
    </div>
  ),
});
const NotionExportSuccessDialog = dynamic(() => import('./components/dialogs/NotionExportSuccessDialog').then(m => m.NotionExportSuccessDialog), {
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
      <Loader2 className="h-4 w-4 animate-spin" />
    </div>
  ),
});
const NotionExplanationDialog = dynamic(() => import('./components/dialogs/NotionExplanationDialog').then(m => m.NotionExplanationDialog), {
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
      <Loader2 className="h-4 w-4 animate-spin" />
    </div>
  ),
});
const GoogleDriveExplanationDialog = dynamic(() => import('./components/dialogs/GoogleDriveExplanationDialog').then(m => m.GoogleDriveExplanationDialog), {
  ssr: false,
  loading: () => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
      <Loader2 className="h-4 w-4 animate-spin" />
    </div>
  ),
});

// Define the form schema for the idea
const ideaSchema = z.object({
  idea: z.string().min(10, "Idea must be at least 10 characters"),
});

type IdeaFormData = z.infer<typeof ideaSchema>;


const ProjectPageContent = memo(function ProjectPageContent() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const { user } = useAuth();
  const searchParams = useSearchParams();

  // --- Form ---
  const ideaForm = useForm<IdeaFormData>({
    resolver: zodResolver(ideaSchema),
    defaultValues: {
      idea: "",
    },
  });

  // --- Custom Hooks for cleaner component ---
  const projectData = useProjectData({ ideaForm });
  const oauthConnections = useOAuthConnections();
  const documents = useProjectDocuments(projectData.storeProjectId, projectData.step4Complete);
  
  // --- Zustand Store Actions ---
  const storeRefinedIdea = useProjectStore((state) => state.refinedIdea);
  const setSelectedToolsInStore = useProjectStore((state) => state.setSelectedTools);
  const setProjectPlanInStore = useProjectStore((state) => state.setProjectPlan);
  const setVoiceNoteUrlInStore = useProjectStore((state) => state.setVoiceNoteUrl);
  const markProjectAsCompleted = useProjectStore((state) => state.markProjectAsCompleted);

  // --- Local Component State ---
  // Chat sidebar open state
  const [isChatOpen, setIsChatOpen] = useState(false);

  // Initialize chat context if needed
  const {
    setSelectedProject, // from useChat context
  } = useChat();
  
  const [projectStatus, setProjectStatus] = useState<"Draft" | "In Progress" | "Completed">("Draft");
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAccordionOpen, setIsAccordionOpen] = useState(true); // Default to open
  const [googleDriveExportSuccessOpen, setGoogleDriveExportSuccessOpen] = useState(false);
  const [exportedFolderUrl, setExportedFolderUrl] = useState("");
  const [notionExportUrl, setNotionExportUrl] = useState<string | null>(null);
  const [notionExportSuccessOpen, setNotionExportSuccessOpen] = useState(false);
  const [notionExplanationOpen, setNotionExplanationOpen] = useState(false);
  const [googleDriveExplanationOpen, setGoogleDriveExplanationOpen] = useState(false);
  const [isOpeningInV0, setIsOpeningInV0] = useState(false);
  const [pageViewMode, setPageViewMode] = useState(false); // false = Edit Mode, true = View Mode
  
  // --- Refs ---
  const step1Ref = useRef<Step1Ref>(null);
  const step4Ref = useRef<{ generateGeminiPlan: () => Promise<string> }>(null);

  // --- Helper Functions ---

  // Memoized progress calculation to prevent recalculation on every render
  const calculateProgress = useMemo(() => {
    let progress = 0;
    if (projectData.step1Complete) progress += 25;
    if (projectData.step2Complete) progress += 25;
    if (projectData.step3Complete) progress += 25;
    if (projectData.step4Complete) progress += 25;
    return progress;
  }, [projectData.step1Complete, projectData.step2Complete, projectData.step3Complete, projectData.step4Complete]);

  // --- Effects ---
  // Determine project status based on step completion
  // Determine project status and accordion state based on step completion
  useEffect(() => {
    let newStatus: "Draft" | "In Progress" | "Completed" = "Draft";
    if (projectData.step4Complete) {
      newStatus = "Completed";
      setIsAccordionOpen(false); // Auto-close for completed projects
    } else if (projectData.step1Complete || projectData.step2Complete || projectData.step3Complete) {
      newStatus = "In Progress";
      // setIsAccordionOpen(true); // Optionally re-open if it was closed and project becomes incomplete again
    }
    setProjectStatus(newStatus);

  }, [projectData.step1Complete, projectData.step2Complete, projectData.step3Complete, projectData.step4Complete]);

  // --- Memoized Handlers ---
  // Stable navigation handler to prevent child re-renders
  const navigateToStep = useCallback(async (targetStep: number) => {
    console.log(`Navigating to step ${targetStep} from current display step ${projectData.currentDisplayStep}`);
    
    // If moving backward to edit a previous step, we don't need validation
    if (targetStep < projectData.currentDisplayStep) {
      console.log(`Moving backward to edit step ${targetStep}`);
      setIsEditMode(true); // Set edit mode when moving backward
      projectData.setCurrentDisplayStep(targetStep);
      return;
    }
    
    // If we're in edit mode and moving forward, we're saving changes
    if (isEditMode && targetStep > projectData.currentDisplayStep) {
      console.log(`Saving changes from edit mode and moving to step ${targetStep}`);
      const success = await projectData.handleStepComplete(projectData.currentDisplayStep);
      if (!success) {
        console.log(`Validation failed for step ${projectData.currentDisplayStep}, not navigating`);
        return; // Don't navigate if saving/validation failed
      }
      setIsEditMode(false); // Exit edit mode after saving
      projectData.setCurrentDisplayStep(targetStep);
      return;
    }
    
    // Normal forward navigation
    const success = await projectData.handleStepComplete(projectData.currentDisplayStep);
    if (!success) {
      console.log(`Validation failed for step ${projectData.currentDisplayStep}, not navigating`);
      return; // Don't navigate if saving/validation failed
    }
    
    // Set the current step
    projectData.setCurrentDisplayStep(targetStep);
  }, [projectData.currentDisplayStep, projectData.handleStepComplete, projectData.setCurrentDisplayStep, isEditMode]);


  // Handle project name update - delegate to custom hook
  const handleProjectNameUpdate = () => {
    setIsEditingName(false);
    return projectData.handleProjectNameUpdate();
  };

  // Handle enhanced idea
  const handleEnhancedIdea = async () => {
    const ideaValue = ideaForm.getValues("idea");
    if (!ideaValue || ideaValue.length < 10) {
      toast({ title: "Idea must be at least 10 characters", variant: "destructive" });
      return;
    }
    
    setIsRefining(true);
    try {
      const refineAction = useProjectStore.getState().refineIdea;
      if (refineAction) {
        const enhanced = await refineAction(ideaValue);
        if (enhanced) {
          ideaForm.setValue("idea", enhanced);
          toast({ title: "Idea refined successfully" });
        } else {
          throw new Error("Refining did not return an enhanced idea.");
        }
      } else {
        throw new Error("refineIdea action not found in store.");
      }
    } catch (error: any) {
      console.error("Error enhancing idea:", error);
      toast({
        title: "Failed to enhance idea",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsRefining(false);
    }
  };

  // Handle Step 4 save complete
  const handleStep4SaveComplete = () => {
    projectData.setStep4Complete(true);
    navigateToStep(5);
  };

  // Handle problems save
  const handleProblemsSave = async () => {
    console.log("handleProblemsSave called");
    try {
      // Implementation details...
    } catch (error) {
      console.error("Error saving problems:", error);
    }
  };

  // Export handlers - use custom hook
  const exportHandlers = useExportHandlers({
    projectId: projectData.storeProjectId,
    projectName: projectData.projectName,
    documents: documents.documents,
    setExportedFolderUrl,
    setGoogleDriveExportSuccessOpen,
    setNotionExportUrl,
    setNotionExportSuccessOpen
  });

  const handleNotionAction = () => {
    console.log("handleNotionAction called, notionConnected:", oauthConnections.notionConnected);
    
    if (!oauthConnections.notionConnected) {
      // Show explanation modal first instead of redirecting immediately
      setNotionExplanationOpen(true);
      return;
    }
    
    // If connected, export to Notion
    console.log("Notion connected, exporting to Notion");
    exportHandlers.handleExportToNotion();
  };

  // Handle Google Drive action (connect or export)
  const handleGoogleDriveAction = () => {
    console.log("handleGoogleDriveAction called, googleDriveConnected:", oauthConnections.googleDriveConnected);
    
    if (!oauthConnections.googleDriveConnected) {
      // Show explanation modal first instead of redirecting immediately
      setGoogleDriveExplanationOpen(true);
      return;
    }
    
    // If connected, export to Google Drive
    console.log("Google Drive connected, exporting to Google Drive");
    exportHandlers.handleExportToGoogleDrive();
  };

  const handleV0Action = () => {
    if (!projectData.storeProjectId) {
      toast({
        title: "Project ID Missing",
        description: "Cannot open in v0 without a project ID.",
        variant: "destructive",
      });
      return;
    }
    // Server-side redirect will handle opening v0
    window.open(`/api/projects/${projectData.storeProjectId}/open-in-v0`, '_blank');
  };
  // --- Render ---
  // Show loading state
  if (projectData.isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4 text-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Loading Project</h3>
        <p className="text-sm text-muted-foreground">Please wait while we fetch your project data...</p>
      </div>
    );
  }

  if (projectData.error) {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-1 text-center">
          <Alert variant="destructive" className="max-w-lg w-full">
            <AlertCircle className="h-5 w-5" />
            <AlertTitle className="text-lg font-semibold">Error Loading Project</AlertTitle>
            <AlertDescription className="mt-2">{projectData.error}</AlertDescription>
          </Alert>
          <Button variant="outline" onClick={() => router.push("/dashboard")} className="mt-6">
            Go to Dashboard
          </Button>
        </div>
        {!isChatOpen && (
          <div className="fixed bottom-6 right-6 z-[100]">
            <button
              className="rounded-full h-14 w-14 shadow-lg bg-gradient-to-br from-purple-400 to-blue-200 flex items-center justify-center"
              onClick={() => {
                setIsChatOpen(true);
                setSelectedProject(projectData.storeProjectId);
              }}
            >
              <MessageSquare className="h-6 w-6" />
            </button>
          </div>
        )}
        {isChatOpen && (
          <InlineChatSidebar isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
        )}
      </>
    );
  }

  return (
    <>
      {/* This root div for ProjectPageContent should fill the DashboardShell.
          DashboardShell is flex flex-col flex-1, so this should be h-full. */}
      <div className="flex flex-col h-full w-full p-0">
        {/* This inner div organizes the header and the main content area. It also needs to be h-full. */}
        <div className={`flex flex-col h-full gap-0 transition-all duration-300 ${isChatOpen ? "mr-[400px]" : ""}`}>
          {/* ProjectHeader component */}
          <div className="flex-none">
            <ProjectHeader
              projectName={projectData.projectName}
              isEditingName={isEditingName}
              tempProjectName={projectData.tempProjectName}
              setTempProjectName={projectData.setTempProjectName}
              handleProjectNameUpdate={handleProjectNameUpdate}
              setIsEditingName={setIsEditingName}
              createdAt={projectData.createdAt}
              updatedAt={projectData.updatedAt}
              projectStatus={projectData.step4Complete ? "Completed" : "Draft"}
              onActionSelect={(action) => {
                console.log("Selected action:", action);
                if (action === "edit") {
                  setIsEditingName(true);
                } else if (action === "duplicate") {
                  console.log("Duplicate project triggered");
                } else if (action === "delete") {
                  console.log("Delete project triggered");
                }
              }}
              notionConnected={oauthConnections.notionConnected}
              onNotionAction={handleNotionAction}
              isExportingNotion={exportHandlers.isExportingNotion}
              onOpenEditor={() => {
                router.push(`/dashboard/project/${projectData.storeProjectId}`);
              }}
              documents={documents.documents || []}
              accordionTrigger={
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Input Summary</span>
                  <span className="text-muted-foreground text-xs">
                    ({(projectData.step1Complete ? 1 : 0) + (projectData.step2Complete ? 1 : 0) + (projectData.step3Complete ? 1 : 0) + (projectData.step4Complete ? 1 : 0)} completed steps)
                  </span>
                </div>
              }
              accordionContent={
                <CompletedStepsAccordion
                  step1Complete={projectData.step1Complete}
                  step2Complete={projectData.step2Complete}
                  step3Complete={projectData.step3Complete}
                  step4Complete={projectData.step4Complete}
                  currentDisplayStep={projectData.currentDisplayStep}
                  refinedIdea={projectData.idea || ideaForm.getValues().idea || ""}
                  productDetails={projectData.productDetails}
                  codingTools={{
                    selectedTools: projectData.selectedTools || []
                  }}
                  projectPlan={projectData.projectPlan}
                  navigateToStep={(step) => {
                    if (step < projectData.currentDisplayStep) {
                      setIsEditMode(true);
                    }
                    projectData.setCurrentDisplayStep(step);
                  }}
                  setIsEditMode={setIsEditMode}
                  projectId={params.id as string}
                  ideaText={ideaForm.getValues().idea || ""}
                  isTestUser={projectData.isTestUser}
                  calculateProgress={calculateProgress}
                />
              }
              isAccordionOpen={isAccordionOpen}
              toggleAccordion={() => setIsAccordionOpen(!isAccordionOpen)}
              documents={documents.documents}
              onDocumentSelect={(docId) => { // When on Step 5 (document generation/viewing step),
                // selecting a document from the header should update the main project URL
                // with the selectedDocId. Step5.tsx will pick this up.
                // This also works for other steps if document selection is desired there.
                router.push(`/dashboard/project/${projectData.storeProjectId}?selectedDocId=${docId}`); }}
              googleDriveConnected={oauthConnections.googleDriveConnected}
              onGoogleDriveAction={handleGoogleDriveAction}
              isExportingGoogleDrive={exportHandlers.isExportingGoogleDrive}
              notionConnected={oauthConnections.notionConnected}
              onNotionAction={handleNotionAction}
              isExportingNotion={exportHandlers.isExportingNotion}
              pageViewMode={pageViewMode}
              setPageViewMode={setPageViewMode}
              onV0Action={handleV0Action}
            />
          </div>
          {/* Main content area for the CURRENT step */}
          {/* This div takes the remaining space after the header and handles its own scrolling. min-h-0 is crucial for flex children. */}
          <div className="flex-1 overflow-y-auto min-h-0">
            {(() => {
              let extraStepProps: Record<string, any> = {};
              if (projectData.currentDisplayStep !== 5) {
                extraStepProps = {
                  navigateToStep: navigateToStep,
                  markProjectAsCompleted: async () => {
                    if (projectData.storeProjectId) {
                      await markProjectAsCompleted(projectData.storeProjectId);
                    } else {
                      console.error("Project ID is missing, cannot mark as completed.");
                    }
                  }
                };
              } else {
                extraStepProps = {
                  navigateToStep: (step: number) => router.push(`/dashboard/project/${projectData.storeProjectId}/steps/${step}`),
                };
              }

              if (pageViewMode && projectData.currentDisplayStep < 5) {
                // In View Mode for steps 1-4, show the CompletedStepsAccordion or a similar summary
                // For simplicity, we'll reuse the accordion here. You might want a dedicated read-only summary component.
                return (
                  <div className="p-4">
                    <CompletedStepsAccordion
                      step1Complete={projectData.step1Complete}
                      step2Complete={projectData.step2Complete}
                      step3Complete={projectData.step3Complete}
                      step4Complete={projectData.step4Complete}
                      currentDisplayStep={projectData.currentDisplayStep} // This might be less relevant in a pure view mode
                      refinedIdea={projectData.idea || ideaForm.getValues().idea || ""}
                      productDetails={projectData.productDetails}
                      codingTools={{ selectedTools: projectData.selectedTools || [] }}
                      projectPlan={projectData.projectPlan}
                      navigateToStep={(step) => { /* Navigation might be disabled or different in view mode */ }}
                      setIsEditMode={setIsEditMode} // This is the step-specific edit mode
                      projectId={params.id as string}
                      ideaText={ideaForm.getValues().idea || ""}
                      isTestUser={projectData.isTestUser}
                      calculateProgress={calculateProgress}
                    />
                  </div>
                );
              } else {
                // In Edit Mode, or for Step 5 (which always shows its content)
                return (
                  <CurrentStepDisplay
                    currentDisplayStep={projectData.currentDisplayStep}
                    ideaForm={ideaForm}
                    isRecording={isRecording}
                    toggleRecording={() => setIsRecording(!isRecording)}
                    handleTranscription={(text, audioUrl) => {
                      ideaForm.setValue("idea", text);
                      projectData.setVoiceNoteUrl(audioUrl);
                      setVoiceNoteUrlInStore(audioUrl);
                    }}
                    isRefining={useProjectStore.getState().isRefining}
                    handleRefineIdea={handleEnhancedIdea}
                    projectId={projectData.storeProjectId}
                    isTestUser={projectData.isTestUser}
                    projectName={projectData.projectName}
                    onProjectNameChange={projectData.setProjectName || (() => {})}
                    clarifyingQuestionsData={useProjectStore.getState().clarifyingQuestions}
                    ideaText={ideaForm.getValues("idea")}
                    selectedTools={projectData.selectedTools}
                    onSelectionChange={(newSelection) => { projectData.setSelectedTools(newSelection); setSelectedToolsInStore(newSelection); }}
                    projectPlan={projectData.projectPlan}
                    setProjectPlan={(plan) => { projectData.setProjectPlan(plan); setProjectPlanInStore(plan); }}
                    isGeneratingPlan={isGeneratingPlan}
                    setIsGeneratingPlan={setIsGeneratingPlan}
                    onSaveComplete={handleStep4SaveComplete}
                    user={user}
                    refinedIdea={storeRefinedIdea}
                    isEditMode={isEditMode} // This is the step-specific edit mode for navigating back
                    setIsEditMode={setIsEditMode}
                    markProjectAsCompleted={async () => { if (projectData.storeProjectId) await markProjectAsCompleted(projectData.storeProjectId); }}
                    // Pass modal control props for Step5 (these are independent of pageViewMode)
                    googleDriveConnectedProp={oauthConnections.googleDriveConnected}
                    notionConnectedProp={oauthConnections.notionConnected}
                    setGoogleDriveExplanationOpen={setGoogleDriveExplanationOpen}
                    setNotionExplanationOpen={setNotionExplanationOpen}
                    setGoogleDriveExportSuccessOpen={setGoogleDriveExportSuccessOpen}
                    setNotionExportSuccessOpen={setNotionExportSuccessOpen}
                    setExportedFolderUrl={setExportedFolderUrl}
                    setNotionExportUrl={setNotionExportUrl}
                    {...extraStepProps}
                  />
                );
              }
            })()}
          </div>
          {/* Lazy loaded dialogs - only render when needed */}
          {notionExplanationOpen && (
            <NotionExplanationDialog
              isOpen={notionExplanationOpen}
              onOpenChange={setNotionExplanationOpen}
              onConnect={() => {
                window.location.href = `/api/auth/notion?projectId=${projectData.storeProjectId}`;
              }}
            />
          )}
          
          {googleDriveExportSuccessOpen && (
            <GoogleDriveExportSuccessDialog
              isOpen={googleDriveExportSuccessOpen}
              onOpenChange={setGoogleDriveExportSuccessOpen}
              folderUrl={exportedFolderUrl}
            />
          )}

          {notionExportSuccessOpen && (
            <NotionExportSuccessDialog
              isOpen={notionExportSuccessOpen}
              onOpenChange={setNotionExportSuccessOpen}
              pageUrl={notionExportUrl}
            />
          )}

          {googleDriveExplanationOpen && (
            <GoogleDriveExplanationDialog
              isOpen={googleDriveExplanationOpen}
              onOpenChange={setGoogleDriveExplanationOpen}
              onConnect={() => {
                window.location.href = `/api/auth/google?projectId=${projectData.storeProjectId}`;
              }}
            />
          )}
        </div>
        {/* Floating Chat Button */}
        {!isChatOpen && (
          <div className="fixed bottom-6 right-6 z-[100]">
            <button
              className="rounded-full h-14 w-14 shadow-lg bg-gradient-to-br from-blue-400 to-blue-200 flex items-center justify-center"
              onClick={() => {
                setIsChatOpen(true);
                setSelectedProject(projectData.storeProjectId);
              }}
            >
              <MessageSquare className="h-6 w-6" />
            </button>
          </div>
        )}
        {/* Lazy loaded Chat Sidebar - only when opened */}
        {isChatOpen && (
          <InlineChatSidebar isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
        )}
      </div>
    </>
  );
});

export default function ProjectPage() {
  const projectName = useProjectStore((state) => state.projectName);
  return (
    <ChatProvider predefinedChatTitle={projectName}>
      <ProjectPageContent />
    </ChatProvider>
  );
}
