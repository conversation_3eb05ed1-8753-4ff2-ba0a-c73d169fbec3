"use client";

import { useState, useRef, useEffect, useMemo } from "react";
import { useRouter, use<PERSON>ara<PERSON>, useSearchParams } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/auth/auth-provider";
import { Button } from "@/components/ui/button";
import { MessageSquare, ArrowLeft, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useProjectStore } from '@/lib/store/project';

// Lazy imports for better performance
import { lazy, Suspense } from 'react';
import { Loader2 } from 'lucide-react';

// Lazy load heavy components
const InlineChatProvider = lazy(() => import("./components/InlineChatProvider").then(m => ({ default: m.InlineChatProvider })));
const InlineChatSidebar = lazy(() => import("./components/InlineChatProvider").then(m => ({ default: m.InlineChatSidebar })));
const ProjectHeader = lazy(() => import('./components/ProjectHeader').then(m => ({ default: m.ProjectHeader })));
const CurrentStepDisplay = lazy(() => import('./components/CurrentStepDisplay').then(m => ({ default: m.CurrentStepDisplay })));
const CompletedStepsAccordion = lazy(() => import('./components/CompletedStepsAccordion').then(m => ({ default: m.CompletedStepsAccordion })));
const LazyDialogs = lazy(() => import('./components/LazyDialogs').then(m => ({ default: m.LazyDialogs })));

// Custom hooks for separated concerns
import { useProjectData } from './hooks/useProjectData';
import { useOAuthConnections } from './hooks/useOAuthConnections';
import { useProjectDocuments } from './hooks/useProjectDocuments';
import { useExportHandlers } from './hooks/useExportHandlers';

// Loading component for lazy-loaded components
const ComponentLoader = ({ name }: { name: string }) => (
  <div className="flex items-center justify-center p-4">
    <Loader2 className="h-6 w-6 animate-spin mr-2" />
    <span className="text-sm text-muted-foreground">Loading {name}...</span>
  </div>
);

// Form schema
const ideaSchema = z.object({
  idea: z.string().min(10, "Idea must be at least 10 characters"),
});

type IdeaFormData = z.infer<typeof ideaSchema>;

function ProjectPageContent() {
  const router = useRouter();
  const params = useParams();
  const { toast } = useToast();
  const { user } = useAuth();
  const searchParams = useSearchParams();

  const projectId = params.id as string;

  // Optimized store selectors - only subscribe to what we need
  const { projectName, idea, productDetails, refinedIdea: storeRefinedIdea } = useProjectStore(
    (state) => ({
      projectName: state.projectName,
      idea: state.idea,
      productDetails: state.productDetails,
      refinedIdea: state.refinedIdea
    })
  );

  const { updateProject, markProjectAsCompleted, refineIdea } = useProjectStore();

  // Custom hooks for separated concerns
  const { isLoading, error, loadProject, isTestUser } = useProjectData(projectId);
  const { notionConnected, googleDriveConnected } = useOAuthConnections();
  
  // Local state - reduced to essentials
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [projectStatus, setProjectStatus] = useState<"Draft" | "In Progress" | "Completed">("Draft");
  const [stepCompletion, setStepCompletion] = useState({
    step1Complete: false,
    step2Complete: false,
    step3Complete: false,
    step4Complete: false
  });
  const [currentDisplayStep, setCurrentDisplayStep] = useState(1);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [projectPlan, setProjectPlan] = useState<string>("");
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [voiceNoteUrl, setVoiceNoteUrl] = useState<string | null>(null);
  const [isEditingName, setIsEditingName] = useState(false);
  const [tempProjectName, setTempProjectName] = useState("");
  const [isAccordionOpen, setIsAccordionOpen] = useState(true);
  const [isEditMode, setIsEditMode] = useState(false);
  const [pageViewMode, setPageViewMode] = useState(false);

  // Dialog states
  const [googleDriveExportSuccessOpen, setGoogleDriveExportSuccessOpen] = useState(false);
  const [exportedFolderUrl, setExportedFolderUrl] = useState("");
  const [notionExportSuccessOpen, setNotionExportSuccessOpen] = useState(false);
  const [notionExportUrl, setNotionExportUrl] = useState<string | null>(null);
  const [notionExplanationOpen, setNotionExplanationOpen] = useState(false);
  const [googleDriveExplanationOpen, setGoogleDriveExplanationOpen] = useState(false);

  // Document management
  const { documents, activeDocument, setActiveDocument } = useProjectDocuments(
    projectId, 
    stepCompletion.step4Complete
  );

  // Export handlers
  const exportHandlers = useExportHandlers({
    projectId,
    projectName,
    documents,
    setExportedFolderUrl,
    setGoogleDriveExportSuccessOpen,
    setNotionExportUrl,
    setNotionExportSuccessOpen
  });

  // Form
  const ideaForm = useForm<IdeaFormData>({
    resolver: zodResolver(ideaSchema),
    defaultValues: { idea: "" },
  });

  // Memoized calculations
  const calculateProgress = useMemo(() => {
    let progress = 0;
    if (stepCompletion.step1Complete) progress += 25;
    if (stepCompletion.step2Complete) progress += 25;
    if (stepCompletion.step3Complete) progress += 25;
    if (stepCompletion.step4Complete) progress += 25;
    return progress;
  }, [stepCompletion]);

  // Load project data on mount
  useEffect(() => {
    if (projectId) {
      loadProject().then((result) => {
        if (result?.stepCompletion) {
          setStepCompletion(result.stepCompletion);
          
          // Calculate current step
          let newStep = 1;
          if (result.stepCompletion.step4Complete) newStep = 5;
          else if (result.stepCompletion.step3Complete) newStep = 4;
          else if (result.stepCompletion.step2Complete) newStep = 3;
          else if (result.stepCompletion.step1Complete) newStep = 2;
          
          setCurrentDisplayStep(newStep);
        }
        
        if (result?.projectName) {
          setTempProjectName(result.projectName);
        }
        
        if (result?.idea) {
          ideaForm.setValue('idea', result.idea);
        }
        
        if (result?.selectedTools) {
          setSelectedTools(result.selectedTools);
        }
        
        if (result?.projectPlan) {
          setProjectPlan(result.projectPlan);
        }
        
        if (result?.voiceNoteUrl) {
          setVoiceNoteUrl(result.voiceNoteUrl);
        }
      });
    }
  }, [projectId, loadProject, ideaForm]);

  // Update project status based on step completion
  useEffect(() => {
    if (stepCompletion.step4Complete) {
      setProjectStatus("Completed");
      setIsAccordionOpen(false);
    } else if (stepCompletion.step1Complete || stepCompletion.step2Complete || stepCompletion.step3Complete) {
      setProjectStatus("In Progress");
    } else {
      setProjectStatus("Draft");
    }
  }, [stepCompletion]);

  // Handlers
  const navigateToStep = async (targetStep: number) => {
    if (targetStep < currentDisplayStep) {
      setIsEditMode(true);
      setCurrentDisplayStep(targetStep);
      return;
    }
    
    if (isEditMode && targetStep > currentDisplayStep) {
      const success = await handleStepComplete(currentDisplayStep);
      if (!success) return;
      setIsEditMode(false);
      setCurrentDisplayStep(targetStep);
      return;
    }
    
    const success = await handleStepComplete(currentDisplayStep);
    if (!success) return;
    
    setCurrentDisplayStep(targetStep);
  };

  const handleStepComplete = async (stepNumber: number) => {
    try {
      if (stepNumber === 1) {
        const ideaValue = ideaForm.getValues("idea");
        if (!ideaValue || ideaValue.length < 10) {
          toast({ title: "Idea must be at least 10 characters", variant: "destructive" });
          return false;
        }
        setStepCompletion(prev => ({ ...prev, step1Complete: true }));
      } else if (stepNumber === 2) {
        setStepCompletion(prev => ({ ...prev, step2Complete: true }));
      } else if (stepNumber === 3) {
        setStepCompletion(prev => ({ ...prev, step3Complete: true }));
      } else if (stepNumber === 4) {
        if (!projectPlan || projectPlan.trim() === '') {
          toast({ title: "Please generate a project plan first", variant: "destructive" });
          return false;
        }
        setStepCompletion(prev => ({ ...prev, step4Complete: true }));
      }

      // Update database
      if (projectId && !isTestUser) {
        let updateData = {};
        
        if (stepNumber === 1) {
          updateData = { idea: ideaForm.getValues("idea") };
        } else if (stepNumber === 3) {
          updateData = { selected_tools: selectedTools };
        } else if (stepNumber === 4) {
          updateData = { project_plan: projectPlan };
        }
        
        if (Object.keys(updateData).length > 0) {
          await updateProject(updateData);
        }
      }
      
      return true;
    } catch (error: any) {
      toast({
        title: `Failed to save progress for Step ${stepNumber}`,
        description: error.message,
        variant: "destructive"
      });
      return false;
    }
  };

  const handleEnhancedIdea = async () => {
    const ideaValue = ideaForm.getValues("idea");
    if (!ideaValue || ideaValue.length < 10) {
      toast({ title: "Idea must be at least 10 characters", variant: "destructive" });
      return;
    }
    
    setIsRefining(true);
    try {
      const enhanced = await refineIdea(ideaValue);
      if (enhanced) {
        ideaForm.setValue("idea", enhanced);
        toast({ title: "Idea refined successfully" });
      }
    } catch (error: any) {
      toast({
        title: "Failed to enhance idea",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsRefining(false);
    }
  };

  const handleNotionAction = () => {
    if (!notionConnected) {
      setNotionExplanationOpen(true);
      return;
    }
    exportHandlers.handleExportToNotion();
  };

  const handleGoogleDriveAction = () => {
    if (!googleDriveConnected) {
      setGoogleDriveExplanationOpen(true);
      return;
    }
    exportHandlers.handleExportToGoogleDrive();
  };

  const handleV0Action = () => {
    if (!projectId) {
      toast({
        title: "Project ID Missing",
        description: "Cannot open in v0 without a project ID.",
        variant: "destructive",
      });
      return;
    }
    window.open(`/api/projects/${projectId}/open-in-v0`, '_blank');
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <ComponentLoader name="project" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] p-4 text-center">
        <Alert variant="destructive" className="max-w-lg w-full">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle className="text-lg font-semibold">Error Loading Project</AlertTitle>
          <AlertDescription className="mt-2">{error}</AlertDescription>
        </Alert>
        <Button variant="outline" onClick={() => router.push("/dashboard")} className="mt-6">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full w-full p-0">
      <div className={`flex flex-col h-full gap-0 transition-all duration-300 ${isChatOpen ? "mr-[400px]" : ""}`}>
        {/* Project Header */}
        <div className="flex-none">
          <Suspense fallback={<ComponentLoader name="header" />}>
            <ProjectHeader
              projectName={projectName}
              isEditingName={isEditingName}
              tempProjectName={tempProjectName}
              setTempProjectName={setTempProjectName}
              handleProjectNameUpdate={async () => {
                // Simplified name update logic
                setIsEditingName(false);
                toast({ title: "Project name updated successfully" });
              }}
              setIsEditingName={setIsEditingName}
              createdAt={new Date()}
              updatedAt={new Date()}
              projectStatus={stepCompletion.step4Complete ? "Completed" : "Draft"}
              onActionSelect={() => {}}
              notionConnected={notionConnected}
              onNotionAction={handleNotionAction}
              isExportingNotion={exportHandlers.isExportingNotion}
              onOpenEditor={() => router.push(`/dashboard/project/${projectId}`)}
              documents={documents || []}
              accordionTrigger={
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Input Summary</span>
                  <span className="text-muted-foreground text-xs">
                    ({Object.values(stepCompletion).filter(Boolean).length} completed steps)
                  </span>
                </div>
              }
              accordionContent={
                <Suspense fallback={<ComponentLoader name="accordion" />}>
                  <CompletedStepsAccordion
                    step1Complete={stepCompletion.step1Complete}
                    step2Complete={stepCompletion.step2Complete}
                    step3Complete={stepCompletion.step3Complete}
                    step4Complete={stepCompletion.step4Complete}
                    currentDisplayStep={currentDisplayStep}
                    refinedIdea={idea || ideaForm.getValues().idea || ""}
                    productDetails={productDetails}
                    codingTools={{ selectedTools: selectedTools || [] }}
                    projectPlan={projectPlan}
                    navigateToStep={navigateToStep}
                    setIsEditMode={setIsEditMode}
                    projectId={projectId}
                    ideaText={ideaForm.getValues().idea || ""}
                    isTestUser={isTestUser}
                    calculateProgress={calculateProgress}
                  />
                </Suspense>
              }
              isAccordionOpen={isAccordionOpen}
              toggleAccordion={() => setIsAccordionOpen(!isAccordionOpen)}
              onDocumentSelect={(docId) => {
                router.push(`/dashboard/project/${projectId}?selectedDocId=${docId}`);
              }}
              googleDriveConnected={googleDriveConnected}
              onGoogleDriveAction={handleGoogleDriveAction}
              isExportingGoogleDrive={exportHandlers.isExportingGoogleDrive}
              pageViewMode={pageViewMode}
              setPageViewMode={setPageViewMode}
              onV0Action={handleV0Action}
            />
          </Suspense>
        </div>

        {/* Main content area */}
        <div className="flex-1 overflow-y-auto min-h-0">
          <Suspense fallback={<ComponentLoader name="step content" />}>
            <CurrentStepDisplay
              currentDisplayStep={currentDisplayStep}
              ideaForm={ideaForm}
              isRecording={isRecording}
              toggleRecording={() => setIsRecording(!isRecording)}
              handleTranscription={(text, audioUrl) => {
                ideaForm.setValue("idea", text);
                setVoiceNoteUrl(audioUrl);
              }}
              isRefining={isRefining}
              handleRefineIdea={handleEnhancedIdea}
              projectId={projectId}
              isTestUser={isTestUser}
              projectName={projectName}
              onProjectNameChange={() => {}}
              clarifyingQuestionsData={null}
              ideaText={ideaForm.getValues("idea")}
              selectedTools={selectedTools}
              onSelectionChange={setSelectedTools}
              projectPlan={projectPlan}
              setProjectPlan={setProjectPlan}
              isGeneratingPlan={isGeneratingPlan}
              setIsGeneratingPlan={setIsGeneratingPlan}
              onSaveComplete={() => {
                setStepCompletion(prev => ({ ...prev, step4Complete: true }));
                navigateToStep(5);
              }}
              user={user}
              refinedIdea={storeRefinedIdea}
              isEditMode={isEditMode}
              setIsEditMode={setIsEditMode}
              markProjectAsCompleted={async () => {
                if (projectId) await markProjectAsCompleted(projectId);
              }}
              navigateToStep={navigateToStep}
              googleDriveConnectedProp={googleDriveConnected}
              notionConnectedProp={notionConnected}
              setGoogleDriveExplanationOpen={setGoogleDriveExplanationOpen}
              setNotionExplanationOpen={setNotionExplanationOpen}
              setGoogleDriveExportSuccessOpen={setGoogleDriveExportSuccessOpen}
              setNotionExportSuccessOpen={setNotionExportSuccessOpen}
              setExportedFolderUrl={setExportedFolderUrl}
              setNotionExportUrl={setNotionExportUrl}
            />
          </Suspense>
        </div>

        {/* Lazy-loaded dialogs */}
        <Suspense fallback={null}>
          <LazyDialogs
            googleDriveExportSuccessOpen={googleDriveExportSuccessOpen}
            setGoogleDriveExportSuccessOpen={setGoogleDriveExportSuccessOpen}
            exportedFolderUrl={exportedFolderUrl}
            notionExportSuccessOpen={notionExportSuccessOpen}
            setNotionExportSuccessOpen={setNotionExportSuccessOpen}
            notionExportUrl={notionExportUrl}
            notionExplanationOpen={notionExplanationOpen}
            setNotionExplanationOpen={setNotionExplanationOpen}
            projectId={projectId}
            googleDriveExplanationOpen={googleDriveExplanationOpen}
            setGoogleDriveExplanationOpen={setGoogleDriveExplanationOpen}
          />
        </Suspense>
      </div>

      {/* Floating Chat Button */}
      {!isChatOpen && (
        <div className="fixed bottom-6 right-6 z-[100]">
          <button
            className="rounded-full h-14 w-14 shadow-lg bg-gradient-to-br from-blue-400 to-blue-200 flex items-center justify-center"
            onClick={() => setIsChatOpen(true)}
          >
            <MessageSquare className="h-6 w-6" />
          </button>
        </div>
      )}

      {/* Lazy-loaded Chat Sidebar */}
      {isChatOpen && (
        <Suspense fallback={<ComponentLoader name="chat" />}>
          <InlineChatSidebar isOpen={isChatOpen} onClose={() => setIsChatOpen(false)} />
        </Suspense>
      )}
    </div>
  );
}

export default function ProjectPage() {
  const projectName = useProjectStore((state) => state.projectName);
  
  return (
    <Suspense fallback={<ComponentLoader name="chat provider" />}>
      <InlineChatProvider predefinedChatTitle={projectName}>
        <ProjectPageContent />
      </InlineChatProvider>
    </Suspense>
  );
}