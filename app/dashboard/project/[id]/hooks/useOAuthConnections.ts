"use client"

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { useAuth } from '@/components/auth/auth-provider';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase-client';

export function useOAuthConnections() {
  const { user } = useAuth();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  
  const [notionConnected, setNotionConnected] = useState(false);
  const [googleDriveConnected, setGoogleDriveConnected] = useState(false);
  const [isCheckingConnections, setIsCheckingConnections] = useState(false);

  // Check both connections in a single effect
  useEffect(() => {
    const checkConnections = async () => {
      if (!user?.id) return;
      
      setIsCheckingConnections(true);
      
      // Handle URL parameters first (for OAuth redirects)
      if (searchParams?.get('notion') === 'connected') {
        setNotionConnected(true);
        toast({
          title: "Notion Connected",
          description: "Your Notion account has been successfully connected.",
        });
      }
      
      if (searchParams?.get('google') === 'connected') {
        setGoogleDriveConnected(true);
        toast({
          title: "Google Drive Connected",
          description: "Your Google Drive account has been successfully connected.",
        });
      }
      
      try {
        // Single query to check both providers
        const { data, error } = await supabase
          .from('user_oauth_tokens')
          .select('provider, access_token')
          .eq('user_id', user.id)
          .in('provider', ['notion', 'google']);
        
        if (!error && data) {
          const providers = new Set(data.filter(token => token.access_token).map(token => token.provider));
          setNotionConnected(providers.has('notion'));
          setGoogleDriveConnected(providers.has('google'));
        }
      } catch (error) {
        console.error("Error checking OAuth connections:", error);
      } finally {
        setIsCheckingConnections(false);
      }
    };
    
    checkConnections();
  }, [user, searchParams, toast]);

  return {
    notionConnected,
    googleDriveConnected,
    isCheckingConnections,
    setNotionConnected,
    setGoogleDriveConnected
  };
}