"use client"

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';

interface UseExportHandlersProps {
  projectId: string | null;
  projectName: string;
  documents: any[];
  setExportedFolderUrl: (url: string) => void;
  setGoogleDriveExportSuccessOpen: (open: boolean) => void;
  setNotionExportUrl: (url: string | null) => void;
  setNotionExportSuccessOpen: (open: boolean) => void;
}

export function useExportHandlers({
  projectId,
  projectName,
  documents,
  setExportedFolderUrl,
  setGoogleDriveExportSuccessOpen,
  setNotionExportUrl,
  setNotionExportSuccessOpen
}: UseExportHandlersProps) {
  const router = useRouter();
  const { toast } = useToast();
  
  const [isExportingGoogleDrive, setIsExportingGoogleDrive] = useState(false);
  const [isExportingNotion, setIsExportingNotion] = useState(false);

  const handleExportToGoogleDrive = async () => {
    if (!documents || documents.length === 0) {
      toast({
        title: "No documents to export",
        description: "This project doesn't have any documents to export.",
        variant: "destructive",
      });
      return;
    }

    try {
      toast({
        title: "Preparing export...",
        description: "Getting your documents ready for Google Drive.",
      });

      setIsExportingGoogleDrive(true);

      const response = await fetch(`/api/projects/${projectId}/export-to-drive`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          documentIds: documents.map(doc => doc.id),
          projectName: projectName
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        if (response.status === 401 && data.needsAuth) {
          toast({
            title: "Google Drive Authorization Required",
            description: data.message || "You need to connect your Google account to export documents.",
            duration: 10000,
          });
          
          // Handle auth redirect
          setTimeout(() => {
            localStorage.setItem("googleAuthRedirectUrl", window.location.href);
            window.location.href = data.authUrl || '/api/auth/google';
          }, 1000);
          return;
        } else if (data.loginUrl) {
          router.push(data.loginUrl);
          return;
        }
        
        throw new Error(data.error || "Failed to export to Google Drive");
      }
      
      toast({
        title: "Export successful! 🎉",
        description: data.folderUrl 
          ? "Your documents have been exported to Google Drive. Click here to view them."
          : "Your documents have been exported to Google Drive.",
        duration: 10000,
      });

      // Open folder if URL available
      if (data.folderUrl) {
        setTimeout(() => {
          window.open(data.folderUrl, '_blank');
        }, 500);
      }

      if (data.folderUrl) {
        setExportedFolderUrl(data.folderUrl);
        setGoogleDriveExportSuccessOpen(true);
      }

      return data;
    } catch (error) {
      console.error("Error exporting to Google Drive:", error);
      toast({
        title: "Export failed",
        description: error instanceof Error ? error.message : "Failed to export to Google Drive",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsExportingGoogleDrive(false);
    }
  };

  const handleExportToNotion = async () => {
    if (!documents || documents.length === 0) {
      toast({
        title: "No documents to export",
        description: "This project doesn't have any documents to export.",
        variant: "destructive",
      });
      return;
    }

    try {
      toast({
        title: "Preparing export to Notion...",
        description: "Getting your documents ready for Notion.",
      });

      setIsExportingNotion(true);

      const response = await fetch(`/api/projects/${projectId}/export-to-notion`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          documentIds: documents.map(doc => doc.id),
          projectName: projectName
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        if (response.status === 401 && data.needsAuth) {
          toast({
            title: "Notion Authorization Required",
            description: data.message || "You need to connect your Notion account to export documents.",
            duration: 10000,
          });
          
          // Handle auth redirect
          setTimeout(() => {
            router.push(data.authUrl || '/dashboard/profile?setup=notion');
          }, 1000);
          return;
        } else if (data.loginUrl) {
          router.push(data.loginUrl);
          return;
        }
        
        throw new Error(data.error || "Failed to export to Notion");
      }
      
      toast({
        title: "Export to Notion successful! 🎉",
        description: data.pageUrl 
          ? "Your documents have been exported to Notion. Opening page..."
          : "Your documents have been exported to Notion.",
        duration: 10000,
      });

      // Open Notion page if URL available
      if (data.pageUrl) {
        setTimeout(() => {
          window.open(data.pageUrl, '_blank');
        }, 500);
      }

      if (data.pageUrl) {
        setNotionExportUrl(data.pageUrl);
        setNotionExportSuccessOpen(true);
      }

      return data;
    } catch (error) {
      console.error("Error exporting to Notion:", error);
      toast({
        title: "Export to Notion failed",
        description: error instanceof Error ? error.message : "Failed to export to Notion",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsExportingNotion(false);
    }
  };

  return {
    handleExportToGoogleDrive,
    handleExportToNotion,
    isExportingGoogleDrive,
    isExportingNotion
  };
}