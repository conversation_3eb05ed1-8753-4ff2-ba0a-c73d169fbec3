"use client"

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase-client';

export function useProjectDocuments(projectId: string | null, step4Complete: boolean) {
  const [documents, setDocuments] = useState<any[]>([]);
  const [activeDocument, setActiveDocument] = useState<string | null>(null);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(false);

  const fetchDocuments = useCallback(async () => {
    if (!projectId || !step4Complete) return;
    
    setIsLoadingDocuments(true);
    
    try {
      const { data, error } = await supabase
        .from('project_documents')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      
      setDocuments(data || []);
      if (data && data.length > 0 && !activeDocument) {
        setActiveDocument(data[0].id);
      }
    } catch (err) {
      console.error('Error fetching documents:', err);
    } finally {
      setIsLoadingDocuments(false);
    }
  }, [projectId, step4Complete, activeDocument]);

  // Only fetch documents when step 4 is completed
  useEffect(() => {
    if (step4Complete) {
      fetchDocuments();
    }
  }, [step4Complete, fetchDocuments]);

  return {
    documents,
    activeDocument,
    setActiveDocument,
    isLoadingDocuments,
    refetchDocuments: fetchDocuments
  };
}