'use client'

import React, { createContext, useContext, useState, useEffect, useCallback, forwardRef, useRef } from 'react'
import { useChat as useVercelChat, type Message } from "ai/react"
import { useAuth } from "@/components/auth/auth-provider"
import { useRouter, usePathname } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import {
  createChatSession,
  getChatMessages,
  saveChatMessage,
  updateChatSessionTitle
} from "@/lib/chat-service"
import { supabase } from "@/lib/supabase-client"
import { agents } from "@/lib/chat/agents"
import { cn } from "@/lib/utils" // Import the cn utility
import { Button } from "@/components/ui/button" // Assuming Button is used in InlineChatSidebar
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card" // Import Card, CardHeader, CardTitle, CardContent
import { X, RotateCcw } from "lucide-react" // Assuming X icon is used in InlineChatSidebar // Import RotateCcw icon
import { MessageList } from "@/components/chat/message-list" // Import MessageList
import { MessageInput } from "@/components/chat/message-input" // Import MessageInput
import { useAutoScroll } from "@/hooks/use-auto-scroll" // Import useAutoScroll
import { PromptSuggestions } from "@/components/chat/inline-prompt-suggestions"

// Define available models
const MODELS = [
  { id: "google/gemini-2.5-flash-preview", name: "Gemini 2.5 Flash"},
  { id: "openai/gpt-4o", name: "GPT-4o" },
  { id: "openai/gpt-4-turbo", name: "GPT-4 Turbo" },
  { id: "openai/gpt-3.5-turbo", name: "GPT-3.5 Turbo" },
  { id: "anthropic/claude-3-5-sonnet", name: "Claude 3.5 Sonnet" },
  { id: "anthropic/claude-3-opus", name: "Claude 3 Opus" },
  { id: "anthropic/claude-3-haiku", name: "Claude 3 Haiku" },
  { id: "google/gemini-1.5-flash", name: "Gemini 1.5 Flash" },
  { id: "google/gemini-1.5-pro", name: "Gemini 1.5 Pro" },
  { id: "google/gemini-2.0-flash-exp:free", name: "Gemini 2.0 Flash" },
]

type ChatContextType = {
  sessionId: string | null
  messages: Message[]
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void
  chatLoading: boolean
  stop: () => void
  append: (message: { role: "user"; content: string }, options?: any) => void
  setInput: (value: string) => void // Add setInput here
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>
  selectedModel: string
  setSelectedModel: React.Dispatch<React.SetStateAction<string>>
  selectedProject: string
  setSelectedProject: React.Dispatch<React.SetStateAction<string>>
  selectedDocType: string
  setSelectedDocType: React.Dispatch<React.SetStateAction<string>>
  projects: Array<{ id: string, name: string }>
  documentTypes: Array<{ id: string, title: string }>
  isLoading: boolean
  selectedAgent: string
  setSelectedAgent: React.Dispatch<React.SetStateAction<string>>
  availableModels: Array<{ id: string, name: string }> // Add availableModels here
  agents: Array<{ id: string, name: string, description: string, icon: string }>
  files: File[]
  setFiles: React.Dispatch<React.SetStateAction<File[]>>
  projectDocuments: Array<{ id: string; name: string; type?: string }> // Add projectDocuments to context
  clearChat: () => void // Add clearChat function to context
  selectedDocument: { id: string; name: string; type?: string } | null
  setSelectedDocument: React.Dispatch<React.SetStateAction<{ id: string; name: string; type?: string } | null>>
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function InlineChatProvider({ children, predefinedChatTitle }: { children: React.ReactNode, predefinedChatTitle?: string }) {
  const { user } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  
  // Extract sessionId from pathname if it exists
  const pathSessionId = pathname.startsWith('/dashboard/chat/') && pathname !== '/dashboard/chat/'
    ? pathname.replace('/dashboard/chat/', '') 
    : null
  
  const [sessionId, setSessionId] = useState<string | null>(pathSessionId)
  const [selectedModel, setSelectedModel] = useState(MODELS[0].id)
  const [selectedProject, setSelectedProject] = useState("all")
  const [selectedDocType, setSelectedDocType] = useState("all")
  const [projects, setProjects] = useState<Array<{ id: string, name: string }>>([
    { id: "all", name: "All Projects" }
  ])
  const [documentTypes, setDocumentTypes] = useState<Array<{ id: string, title: string }>>([
    { id: "all", title: "All Documents" }
  ])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedAgent, setSelectedAgent] = useState<string>("default")
  const [availableAgents, setAvailableAgents] = useState<Array<{ id: string, name: string, description: string, icon: string }>>([
    { id: "default", name: "Default Assistant", description: "General purpose assistant", icon: "🤖" }
  ])
  const [projectDocuments, setProjectDocuments] = useState<Array<{ id: string; name: string; type?: string }>>([]);

  // State for selectedDocument
  const [selectedDocument, setSelectedDocument] = useState<{ id: string; name: string; type?: string } | null>(null);

  // Load agents from database
  useEffect(() => {
    const fetchAgents = async () => {
      if (!user?.id) return;
      
      try {
        const { data: agentsData, error } = await supabase
          .from("chat_agents")
          .select("id, name, description, icon")
          .eq("active", true)
          .order("name", { ascending: true });
          
        if (error) throw error;
        
        // Set agents with "Default Assistant" as first option
        setAvailableAgents([
          { id: "default", name: "Default Assistant", description: "General purpose assistant", icon: "🤖" },
          ...(agentsData || [])
        ]);
      } catch (error) {
        console.error("Error fetching agents:", error);
      }
    };
    
    fetchAgents();
  }, [user?.id]);

  // Configure useChat hook - renamed to useVercelChat to avoid circular dependency
  const { 
    messages, 
    input, 
    handleInputChange, 
    handleSubmit: aiHandleSubmit, 
    isLoading: chatLoading, 
    stop, 
    append: aiAppend, 
    setMessages,
    setInput: aiSetInput // Destructure setInput from useVercelChat
  } = useVercelChat({
    api: '/api/chat',
    headers: {
      'x-user-id': user?.id || '',
    },
    body: {
      model: selectedModel,
      projectId: selectedProject !== "all" ? selectedProject : undefined,
      documentType: selectedDocType !== "all" ? selectedDocType : undefined,
      agentId: selectedAgent !== "default" ? selectedAgent : undefined,
      sessionId: sessionId,
    },
    onFinish: async (message) => {
      if (!sessionId || !user?.id || message.role !== 'assistant') return
      console.log("Message completed, saving to database:", message.id);
      
      // Save the assistant message to the database as a fallback
      try {
        const response = await fetch('/api/chat/save-message', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-user-id': user.id,
          },
          body: JSON.stringify({
            sessionId: sessionId,
            role: 'assistant',
            content: message.content,
            agentId: selectedAgent !== "default" ? selectedAgent : null,
            projectId: selectedProject !== "all" ? selectedProject : null
          }),
        });
        
        if (!response.ok) {
          const error = await response.json();
          console.error("Failed to save assistant message:", error);
        } else {
          console.log("Assistant message saved successfully via client-side fallback");
        }
      } catch (error) {
        console.error("Error saving assistant message:", error);
      }
    },
    onError: (err) => {
      console.error("Chat error:", err);
      toast({ title: "Chat Error", description: err.message, variant: "destructive" })
    },
  });

  // Load chat based on sessionId from URL
  useEffect(() => {
    const loadChat = async () => {
      if (!user?.id) return;

      console.log(`[ChatProvider] Loading chat. Path session ID: ${pathSessionId}`);

      if (pathSessionId) {
        console.log(`[ChatProvider] Existing session ID found: ${pathSessionId}. Fetching messages...`);
        setSessionId(pathSessionId);
        // Fetch messages for this session
        try {
          console.log(`[ChatProvider] Calling getChatMessages for user ${user.id}, session ${pathSessionId}`);
          const messagesResponse = await getChatMessages(user.id, pathSessionId, 1000);
          console.log(`[ChatProvider] Fetched ${messagesResponse.data.length} messages for session ${pathSessionId}.`);
          const formattedMessages: Message[] = messagesResponse.data.map(m => ({
            id: m.id,
            role: m.role,
            content: m.content,
            createdAt: new Date(m.created_at)
          }));
          setMessages(formattedMessages);
        } catch (error) {
          console.error("Error fetching chat messages:", error);
          toast({
            title: "Error",
            description: "Failed to load chat history.",
            variant: "destructive",
          });
        }
      } else {
        // No session ID - this is a new chat
        console.log("[ChatProvider] No session ID in path. Initializing new chat state.");
        setSessionId(null);
        setMessages([]); // Ensure message list is empty for new chat
      }
      
      setIsLoading(false);
    };

    loadChat();
  }, [user?.id, pathSessionId, setMessages]);

  // Wrapper for handleSubmit to create session on first message
  const handleSubmit = useCallback(async (e?: React.FormEvent<HTMLFormElement>) => {
    if (e && e.preventDefault) {
      e.preventDefault(); // Prevent default form submission
    }
    
    // Add debug logging
    console.log("[ChatProvider] Submitting with project ID:", selectedProject !== "all" ? selectedProject : "none");
    
    if (!user?.id) {
      toast({ title: "Error", description: "User not authenticated", variant: "destructive" });
      return;
    }

    let currentSessionId = sessionId;
    let isFirstMessage = !currentSessionId;
    const userMessage = input.trim(); // Get the user's message
    
    if (!userMessage) {
      console.log("[ChatProvider] Empty message, not submitting");
      return;
    }

    // If it's the first message (no session ID yet), create a session first
    if (isFirstMessage) {
      try {
        console.log("[ChatProvider] No active session ID. Creating new session...");
        const newSession = await createChatSession(user.id, predefinedChatTitle);
        currentSessionId = newSession.id; // Use the new ID
        console.log(`[ChatProvider] New session created with ID: ${currentSessionId}. Updating state.`);
        setSessionId(currentSessionId); // Update state
        
        // Add the message to the UI immediately BEFORE changing the URL
        const newUserMessage: Message = {
          id: Date.now().toString(),
          role: 'user',
          content: userMessage,
          createdAt: new Date()
        };
        setMessages(prev => [...prev, newUserMessage]);
        
        // Clear the input field
        handleInputChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
        
        // Save the first message to the database directly
        try {
          console.log(`[ChatProvider] Saving first user message for session ${currentSessionId}`);
          await saveChatMessage(user.id, currentSessionId, 'user', userMessage);
          console.log(`[ChatProvider] First user message saved successfully for session ${currentSessionId}`);
        } catch (error) {
          console.error("Error saving first user message:", error);
          // Continue with the chat even if saving fails
        }
        
        // If this is the first message, automatically name the chat session based on the message content
        try {
          // Generate a title from the first message (truncate if too long)
          const messageTitle = userMessage.length > 50 
            ? `${userMessage.substring(0, 47)}...` 
            : userMessage;
          
          // Update the session title only if predefinedChatTitle is not provided
          if (!predefinedChatTitle) {
            await updateChatSessionTitle(user.id, currentSessionId, messageTitle);
            console.log(`[ChatProvider] Automatically named chat session: "${messageTitle}"`);
          }
        } catch (error) {
          console.error("Error automatically naming chat session:", error);
          // Non-critical error, don't show toast to user
        }
        
        // No redirect for InlineChatProvider
        
        // Now make the API call to get the AI response
        console.log(`[ChatProvider] Calling API for first message response, session ID: ${currentSessionId}`);
        
        // Use the useChat hook's append function to send the message to the API
        // This will trigger the API call without saving the message again
        aiAppend(
          { role: 'user', content: userMessage },
          {
            options: {
              body: {
                sessionId: currentSessionId,
                model: selectedModel,
                projectId: selectedProject !== "all" ? selectedProject : undefined,
                documentType: selectedDocType !== "all" ? selectedDocType : undefined,
                documentId: selectedDocument?.id,
              }
            }
          }
        );
        
        return; // Exit early since we've handled everything
      } catch (error) {
        console.error("Error creating chat session:", error);
        toast({ 
          title: "Error", 
          description: "Failed to create a new chat session", 
          variant: "destructive" 
        });
        return; // Don't proceed if session creation failed
      }
    }

    // For subsequent messages, use the normal flow
    console.log(`[ChatProvider] Submitting message for existing session ID: ${currentSessionId}`);
    aiHandleSubmit(e, {
      options: {
        body: {
          sessionId: currentSessionId,
          model: selectedModel,
          projectId: selectedProject !== "all" ? selectedProject : undefined,
          documentType: selectedDocType !== "all" ? selectedDocType : undefined,
          documentId: selectedDocument?.id,
        }
      }
    });
  }, [
    sessionId, 
    input, 
    user?.id, 
    selectedModel, 
    selectedProject, 
    selectedDocType, 
    router, 
    aiAppend, 
    handleInputChange, 
    setMessages, 
    aiHandleSubmit,
    predefinedChatTitle,
  ]);

  // Wrap the append function to handle session creation
  const append = useCallback(async (message: { role: "user"; content: string }, options?: any) => {
    if (!user?.id) {
      toast({ title: "Error", description: "User not authenticated", variant: "destructive" });
      return;
    }

    let currentSessionId = sessionId;

    // If no session exists yet, create one first
    if (!currentSessionId) {
      try {
        console.log("[ChatProvider] No active session ID. Creating new session for suggestion...");
        const newSession = await createChatSession(user.id, predefinedChatTitle);
        currentSessionId = newSession.id;
        console.log(`[ChatProvider] New session created with ID: ${currentSessionId} for suggestion.`);
        setSessionId(currentSessionId);
        
        // No redirect for InlineChatProvider
        
        // Give state and URL a moment to update
        await new Promise(resolve => setTimeout(resolve, 100)); // Increase timeout to ensure state updates
      } catch (error) {
        console.error("Error creating chat session for suggestion:", error);
        toast({ 
          title: "Error", 
          description: "Failed to create a new chat session", 
          variant: "destructive" 
        });
        return;
      }
    }

    // Now append the message with the session ID
    console.log("[ChatProvider] Using sessionId:", currentSessionId);
    
    // Make sure we're passing the sessionId correctly in the body
    await aiAppend(message, {
      options: {
        body: {
          sessionId: currentSessionId, // Ensure this is a valid string
          model: selectedModel,
          projectId: selectedProject !== "all" ? selectedProject : undefined,
          documentType: selectedDocType !== "all" ? selectedDocType : undefined,
          documentId: selectedDocument?.id,
          agentId: selectedAgent !== "default" ? selectedAgent : undefined,
        },
        ...options?.options
      },
      ...options
    });
  }, [sessionId, user?.id, selectedModel, selectedProject, selectedDocType, selectedAgent, aiAppend, predefinedChatTitle]);

  // Load projects and document types
  useEffect(() => {
    const fetchProjectsAndDocTypes = async () => {
      if (!user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        // Fetch projects from Supabase
        const { data: projectsData, error: projectsError } = await supabase
          .from("projects")
          .select("id, name")
          .eq("user_id", user.id)
          .order("name", { ascending: true });

        if (projectsError) throw projectsError;
        
        // Set projects with "All Projects" as first option
        setProjects([
          { id: "all", name: "All Projects" },
          ...(projectsData || []).map(p => ({ id: p.id, name: p.name }))
        ]);

        // Fetch document types from Supabase
        const { data: docTypesData, error: docTypesError } = await supabase
          .from("document_types")
          .select("id, title")
          .order("title", { ascending: true });

        if (docTypesError) throw docTypesError;
        
        // Set document types with "All Documents" as first option
        setDocumentTypes([
          { id: "all", title: "All Documents" },
          ...(docTypesData || [])
        ]);
      } catch (error) {
        console.error("Error fetching projects or document types:", error);
        toast({ 
          title: "Error", 
          description: "Failed to load projects or document types", 
          variant: "destructive" 
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectsAndDocTypes();
  }, [user?.id, toast]);

  // Fetch project documents based on selectedProject
  useEffect(() => {
    const fetchProjectDocuments = async () => {
      if (!user?.id || !selectedProject || selectedProject === "all") {
        setProjectDocuments([]); // Clear documents if no specific project is selected
        return;
      }

      setIsLoading(true); // Indicate loading state for documents
      try {
        // Assuming you have a 'project_documents' table with 'project_id', 'id', 'title', and 'type'
        const { data: documentsData, error: documentsError } = await supabase
          .from("project_documents")
          .select("id, title, type")
          .eq("project_id", selectedProject)
          .order("title", { ascending: true });

        if (documentsError) throw documentsError;

        setProjectDocuments((documentsData || []).map(doc => ({
          id: doc.id,
          name: doc.title,
          type: doc.type,
        })));
      } catch (error) {
        let detailedErrorMessage = "Failed to load project documents. Please try again.";

        if (error && typeof error === 'object' && 'message' in error) {
          // This handles Supabase PostgrestError and other error-like objects with a message property
          detailedErrorMessage = String((error as { message: string }).message);
        } else if (error instanceof Error) {
          // Standard JavaScript Error
          detailedErrorMessage = error.message;
        }

        console.error("Error fetching project documents: ", detailedErrorMessage, "\nOriginal error details: ", error);
        toast({
          title: "Error",
          description: detailedErrorMessage, // Show more specific error to the user
          variant: "destructive",
        });
        setProjectDocuments([]); // Ensure it's an empty array on error
      } finally {
        setIsLoading(false); // Reset loading state
      }
    };

    fetchProjectDocuments();
  }, [user?.id, selectedProject, toast]);

  // Add state for files for the MessageInput
  const [files, setFiles] = useState<File[]>([]);

  // Use auto-scroll hook for the message list
  const messageListRef = useAutoScroll([messages]);

  // Add clearChat function
  const clearChat = useCallback(() => {
    setMessages([]);
    setSessionId(null);
    // Optionally reset other states if needed, like selectedAgent, selectedModel etc.
    // setSelectedAgent("default");
    // setSelectedModel(MODELS[0].id);
    console.log("[ChatProvider] Chat history cleared and session ID reset.");
  }, [setMessages, setSessionId]);

  const value = {
    sessionId,
    messages,
    input,
    handleInputChange,
    handleSubmit,
    setInput: aiSetInput,
    chatLoading,
    stop,
    append,
    setMessages,
    selectedModel,
    setSelectedModel,
    selectedProject,
    setSelectedProject,
    selectedDocType,
    setSelectedDocType,
    projects,
    documentTypes,
    isLoading,
    selectedAgent,
    setSelectedAgent,
    availableModels: MODELS,
    agents: availableAgents,
    files,
    setFiles,
    projectDocuments, // Provide projectDocuments through context
    clearChat,
    selectedDocument,
    setSelectedDocument,
  }

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>
}

export const useChat = () => {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within an InlineChatProvider')
  }
  return context
}

// Define initial prompt suggestions
const initialPromptSuggestions = [
  "Summarize the key points",
  "Generate ideas for new features",
  "Help me think through user needs",
  "Generate a new document",
  "Provide feedback on this document"
];

// Refactored InlineChatSidebar to use atomic components
export function InlineChatSidebar({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  // Access chat state and functions from the provider
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    chatLoading,
    stop,
    files,
    setFiles,
    append,
    clearChat,
    selectedAgent,
    setSelectedAgent,
    agents,
    selectedDocType,
    projectDocuments, // Consume projectDocuments from context
  } = useChat();

  // Access user data from useAuth
  const { user } = useAuth();
  const userName = user?.user_metadata?.full_name || user?.email?.split('@')[0] || "User"; // Get user name or a fallback

  // Use auto-scroll hook for the message list (will be applied to MessageList container)
  const messageListRef = useAutoScroll([messages]);

  // Check if the chat is empty to show welcome and suggestions
  const isEmpty = messages.length === 0;

  console.log("[InlineChatSidebar] Current selectedDocType:", selectedDocType); // Log to see the value in console

  return (
    <Card className={cn(
      "fixed inset-y-0 right-0 z-40 flex flex-col w-[400px]",
      "transition-all duration-300 ease-in-out",
      "shadow-lg",
      "rounded-none",
      isOpen ? "translate-x-0" : "translate-x-full"
    )}>
      {/* Make CardHeader relative for positioning buttons, ensure it's a flex container for alignment */}
      <CardHeader 
        className="relative flex flex-row items-center border-b px-1 shadow-sm"  // Removed py-1
        style={{ height: "60px" /* Explicit total height */ }}
      >
        {/* Title container: takes up available space and centers its content */}
        <div className="flex-grow flex items-center justify-center h-full"> {/* h-full ensures vertical centering within the 56px CardHeader */}
          <CardTitle className="text-lg font-semibold flex items-center">Project Assistant</CardTitle>
        </div>
        {/* Buttons container: absolutely positioned to the right, vertically centered */}
        <div className="absolute right-1 top-1/2 transform -translate-y-1/2 flex items-center">
          <Button variant="ghost" size="icon" onClick={clearChat} className="h-6 w-6 mr-1">
            <RotateCcw className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={onClose} className="h-6 w-6">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      {/* Main content area: Welcome/Suggestions or Message List */}
      {/* Use CardContent for the scrollable area */}
      <CardContent className="flex-1 overflow-y-auto p-4">
        {isEmpty ? (
          <PromptSuggestions
            label="Try these prompts ✨"
            append={append}
            suggestions={[
            
              "Summarize the key points",
              "Generate ideas for new features",
              "Help me think through user needs",
              "Generate a new document",
              "Provide feedback on this document",
            ]}
          />
        ) : (
          <div ref={messageListRef.containerRef} className="h-full overflow-y-auto">
            <MessageList messages={messages} />
          </div>
        )}
      </CardContent>

      {/* Message Input area - fixed at the bottom */}
      {/* Added padding around the input area here */}
      <div className="p-3">
        <MessageInput
          value={input}
          onChange={handleInputChange}
          onSend={handleSubmit}
          isGenerating={chatLoading}
          allowAttachments={true}
          files={files}
          setFiles={setFiles}
          selectedAgent={selectedAgent}
          setSelectedAgent={setSelectedAgent}
          availableAgents={agents}
          projectDocuments={projectDocuments} // Pass projectDocuments to MessageInput
        />
      </div>

    </Card>
  );
}
