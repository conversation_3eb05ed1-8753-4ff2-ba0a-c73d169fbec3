
import React, { useRef, useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input"; // Keep Input if used elsewhere
import { But<PERSON> } from "@/components/ui/button";
import { Check, X, ChevronDown, Home, ChevronRight, FilePenLine, ChevronsLeftRightEllipsis, CircleCheckBig, ChevronsRight, Layers, Edit3, Brain, Briefcase, FileText, CircleCheck, CircleDashed, CheckCircle2, Slash, LoaderCircle, Download, Loader2, ExternalLink, SlidersHorizontal } from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";
import { useRouter } from "next/navigation";
import { Label } from "@/components/ui/label"; // Added
import { Switch } from "@/components/ui/switch"; // Added
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/components/auth/auth-provider";

interface ProjectHeaderProps {
    projectName: string;
    isEditingName: boolean;
    tempProjectName: string;
    setTempProjectName: (name: string) => void;
    handleProjectNameUpdate: () => void;
    setIsEditingName: (isEditing: boolean) => void;
    createdAt: Date | null;
    updatedAt: Date | null;
    children?: React.ReactNode;
    projectStatus?: "Draft" | "In Progress" | "Completed";
    onActionSelect?: (action: string) => void;
    // New props for accordion integration
    accordionTrigger?: React.ReactNode;
    accordionContent?: React.ReactNode;
    isAccordionOpen?: boolean;
    toggleAccordion?: () => void;
    // New prop for documents
    documents?: Array<{id: string, title: string, type: string}>;
    onDocumentSelect?: (documentId: string) => void;
    // New props for Google Drive integration
    googleDriveConnected?: boolean;
    onGoogleDriveAction?: () => void;
    isExportingGoogleDrive?: boolean;
    // New props for Notion integration
    notionConnected?: boolean;
    onNotionAction?: () => void;
    isExportingNotion?: boolean;
    // V0 integration handled internally
    showTrailingChevron?: boolean;
    onOpenEditor?: () => void; // New prop for "Open Editor" button
    // Props for page-level View/Edit mode switch
    pageViewMode?: boolean;
    setPageViewMode?: (isViewMode: boolean) => void;
}

export const ProjectHeader: React.FC<ProjectHeaderProps> = ({
    projectName,
    isEditingName,
    tempProjectName,
    setTempProjectName,
    handleProjectNameUpdate,
    setIsEditingName,
    createdAt,
    updatedAt,
    children,
    projectStatus,
    onActionSelect,
    // New props with defaults - isAccordionOpen now defaults based on projectStatus
    accordionTrigger,
    accordionContent,
    isAccordionOpen = projectStatus !== "Completed", // Default open for draft projects
    toggleAccordion,
    // Document props
    documents = [],
    onDocumentSelect,
    // Google Drive integration props
    googleDriveConnected = false,
    onGoogleDriveAction,
    isExportingGoogleDrive = false,
    // Notion integration props
    notionConnected = false,
    onNotionAction,
    isExportingNotion = false,
    // V0 integration handled internally
    showTrailingChevron = true,
    onOpenEditor, // Destructure the new prop
    pageViewMode,
    setPageViewMode,
}) => {
    const nameInputRef = useRef<HTMLInputElement>(null);
    const router = useRouter();
    const { user } = useAuth();

    const { id: projectId } = useParams();
    const [isOpeningInV0, setIsOpeningInV0] = useState(false);
    const [showDocumentDropdown, setShowDocumentDropdown] = useState(false);

    // Focus input when editing project name starts
    useEffect(() => {
        if (isEditingName && nameInputRef.current) {
            nameInputRef.current.focus();
        }
    }, [isEditingName]);

    return (
        <Card className="mb-0 rounded-none border-b">
            <CardContent className="p-2 flex items-center" style={{ height: "60px" }}> {/* Explicit height, p-2 for horizontal padding, flex items-center for vertical centering */}
                <div
                    className="flex flex-row justify-between items-center w-full" // w-full to take available space
                    // style={{ height: "40px" }} // Removed fixed height from inner div
                >
                    {/* Left section with Home, chevrons and project name */}
                    <div className="flex items-center gap-2">

                        {/* Home button 

                        <Button
                            variant="ghost"
                            size="icon"
                            asChild
                            className="ml-1"
                            aria-label="Back to dashboard"
                        >
                            <Link href="/dashboard">
                                <Home className="h-4 w-4" />
                            </Link>
                        </Button>
                        
                        <Slash className="h-4 w-4 text-muted-foreground" />

                        */}
                        
                        {/* Project Name - Inline editable */}
                        <div className="flex flex-wrap ml-2 items-center gap-2">
                            {isEditingName ? (
                                <div className="flex items-center gap-1">
                                    <Input
                                        ref={nameInputRef}
                                        value={tempProjectName}
                                        onChange={(e) => setTempProjectName(e.target.value)}
                                        className="h-8 w-64"
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') handleProjectNameUpdate();
                                            if (e.key === 'Escape') {
                                                setIsEditingName(false);
                                                setTempProjectName(projectName);
                                            }
                                        }}
                                        autoFocus
                                    />
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={handleProjectNameUpdate}
                                        className="h-8 w-8"
                                        aria-label="Save project name"
                                    >
                                        <Check className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                            setIsEditingName(false);
                                            setTempProjectName(projectName);
                                        }}
                                        className="h-8 w-8"
                                        aria-label="Cancel editing project name"
                                    >
                                        <X className="h-4 w-4" />
                                    </Button>
                                </div>
                            ) : (
                                <Button 
                                    variant="outline"
                                    className="h-8 px-3 py-1 font-bold hover:text-emerald-600 transition-colors"
                                    onClick={() => {
                                        setTempProjectName(projectName);
                                        setIsEditingName(true);
                                    }}
                                    title={projectName}
                                >
                                    <Briefcase className="h-4 w-4 mr-2" />
                                    <span className="truncate max-w-[200px]">{projectName}</span>
                                </Button>
                            )}
                            
                            <ChevronsRight className="h-4 w-4 text-muted-foreground" />

                            {/* Level 1: Project Brief Accordion Trigger in ProjectHeader */}
                            {accordionTrigger && toggleAccordion && (
                                <TooltipProvider>
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Button 
                                                variant={isAccordionOpen ? "default" : "outline"}
                                                className={`h-8 px-3 py-1 transition-colors ${
                                                    isAccordionOpen 
                                                        ? "bg-emerald-600 text-white hover:bg-emerald-700" 
                                                        : "hover:text-emerald-600"
                                                }`}
                                                onClick={toggleAccordion}
                                                aria-label="Toggle project brief"
                                            >
                                                <SlidersHorizontal className="h-4 w-4 mr-2 flex-shrink-0" />
                                                <span className="truncate text-sm font-medium">Inputs</span>
                                                {projectStatus && (
                                                    <span className="ml-2 flex-shrink-0">
                                                       {/*}
                                                        {projectStatus === "Completed" ? (
                                                            <CircleCheckBig className={`h-4 w-4 ${isAccordionOpen ? "text-white" : "text-green-600"}`} />
                                                        ) : (
                                                            <LoaderCircle className={`h-4 w-4 ${isAccordionOpen ? "text-white" : "text-amber-500"} animate-spin`} />
                                                        )}
                                                        */}
                                                    </span>
                                                )}
                                                <ChevronDown 
                                                    className={`h-4 w-4 ml-2 flex-shrink-0 transition-transform duration-200 ${
                                                        isAccordionOpen ? 'rotate-180' : ''
                                                    }`} 
                                                />
                                            </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            <p>Toggle project brief details</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                            )}
                            {children}
                            {/* Removed standalone projectStatus badge */}
                        </div>

                        {showTrailingChevron && (
                            <ChevronsRight className="h-4 w-4 text-muted-foreground" />
                        )}

                        {/* Document icon dropdown */}
                        {projectStatus === "Completed" && documents.length > 0 && onDocumentSelect && (
                            <div className="relative">
                                <Button
                                    variant="outline"
                                    className="relative h-8 px-3 py-1"
                                    onClick={() => setShowDocumentDropdown(!showDocumentDropdown)}
                                    aria-label="Show documents"
                                >
                                    <FileText className="h-5 w-5 text-blue-600" />
                                    <span className="ml-2 text-sm font-medium">Documents</span>
                                    
                                    <span className="absolute -top-1 -right-1 h-4 w-4 text-[10px] bg-blue-600 text-white rounded-full flex items-center justify-center">
                                        {documents.length}
                                    </span>
                                </Button>
                                {showDocumentDropdown && (
                                    <div className="absolute right-0 mt-2 w-72 rounded-md border bg-white shadow-lg z-50 p-2 space-y-1">
                                        {documents.map(doc => (
                                            <div
                                                key={doc.id}
                                                className="px-2 py-1.5 rounded-md hover:bg-muted cursor-pointer"
                                                onClick={() => {
                                                    setShowDocumentDropdown(false);
                                                    onDocumentSelect?.(doc.id);
                                                }}
                                            >
                                                <div className="text-sm font-medium text-gray-800 truncate">{doc.title}</div>
                                                <div className="text-xs text-gray-500">Updated recently</div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>

                    {/* Right-side export and document controls */}
                    <div className="flex items-center gap-2">
                        
                        
                        
                        {/* V0 Button */}
                        {projectStatus === "Completed" && documents.length > 0 && (
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        {isOpeningInV0 ? (
                                            <div
                                                className="inline-flex items-center justify-center h-8"
                                                style={{ minWidth: '110px' }}
                                                aria-label="Opening in V0"
                                                role="status"
                                            >
                                                <LoaderCircle className="h-5 w-5 animate-spin text-muted-foreground" />
                                            </div>
                                        ) : (
                                            <button
                                                onClick={async () => {
                                                    setIsOpeningInV0(true);
                                                    try {
                                                        // Add auth header with user ID if available
                                                        const headers = new Headers();
                                                        
                                                        // Get the current user from your auth context or state
                                                        // This assumes you have access to the user object in this component
                                                        if (user?.id) {
                                                            headers.append('x-user-id', user.id);
                                                        }
                                                        
                                                        const res = await fetch(`/api/projects/${projectId}/open-in-v0`, {
                                                            headers,
                                                            credentials: 'include' // Important: include cookies
                                                        });
                                                        
                                                        if (!res.ok) {
                                                            const data = await res.json();
                                                            
                                                            // Handle auth errors
                                                            if (res.status === 401 && data.needsAuth) {
                                                                toast({
                                                                    title: "Authentication Required",
                                                                    description: "Please log in to use this feature",
                                                                    variant: "destructive"
                                                                });
                                                                
                                                                if (data.loginUrl) {
                                                                    router.push(data.loginUrl);
                                                                    return;
                                                                }
                                                            }
                                                            
                                                            throw new Error(data.error || 'Failed to fetch v0 link');
                                                        }
                                                        
                                                        const { url } = await res.json();
                                                        window.open(url, '_blank');
                                                    } catch (err) {
                                                        console.error(err);
                                                        toast({
                                                            title: "Error",
                                                            description: err.message || "Could not open in v0",
                                                            variant: "destructive"
                                                        });
                                                    } finally {
                                                        setIsOpeningInV0(false);
                                                    }
                                                }}
                                                className="inline-flex items-center h-8"
                                                aria-label="Open in V0"
                                            >
                                                <img
                                                    src="https://v0.dev/chat-static/button.svg"
                                                    alt="Open in v0"
                                                    style={{
                                                        height: '32px',
                                                        cursor: 'pointer',
                                                        verticalAlign: 'middle',
                                                    }}
                                                />
                                            </button>
                                        )}
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        <p>{isOpeningInV0 ? "Opening..." : "Open project documents in V0"}</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        )}
                        
                    </div>
                </div>
                
          
                
                {/* Accordion content if provided and open */}
                {accordionContent && isAccordionOpen && (
                    <div className="border-t pt-0">
                        {accordionContent}
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
