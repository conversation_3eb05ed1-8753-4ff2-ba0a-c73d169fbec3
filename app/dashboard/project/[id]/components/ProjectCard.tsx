import React from 'react';
import Link from "next/link";
import { format } from "date-fns";
import { <PERSON><PERSON><PERSON>, MoreHorizontal, Eye, Trash2 } from "lucide-react";
import { CheckCircle2 } from "lucide-react";
import CircularProgress from "./CircularProgress";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";

// Document type labels mapping
const DOCUMENT_LABELS: Record<string, string> = {
  prd: "PRD",
  user_flow: "User Flow",
  architecture: "Architecture",
  api_spec: "API Spec",
  schema: "Schema",
};

// Helper function to get a friendly document label
const getDocumentLabel = (type: string): string => {
  return DOCUMENT_LABELS[type] || type.replace(/_/g, " ");
};

interface ProjectCardProps {
  project: any;
  onDelete: (id: string) => void;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ project, onDelete }) => {
  // Calculate completion percentage based on steps
  const completedDocs = project.documents.filter((doc: any) => doc.status === "completed").length;
  const totalDocs = project.documents.length || 1; // Avoid division by zero
  const completionPercent = Math.round((completedDocs / totalDocs) * 100);
  const isCompleted = completionPercent === 100;

  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md hover:-translate-y-1 group flex flex-col h-full">
      <CardHeader className="pb-3 flex-shrink-0">
        <div className="flex justify-between items-center">
          <CardTitle className="line-clamp-1 text-lg font-semibold text-primary">
            {project.name}
          </CardTitle>
          <CircularProgress
            value={completionPercent}
            size={40}
            strokeWidth={4}
            showLabel={false}
            className="stroke-muted"
            progressClassName={isCompleted ? "stroke-green-500" : "stroke-amber-500"}
          />
        </div>
        <CardDescription className="line-clamp-3 mt-2">{project.idea}</CardDescription>
      </CardHeader>
      
      <CardContent className="flex-grow">
        {/* Document badges */}
        <div className="flex flex-wrap gap-2">
          {project.documents.length > 0 ? (
            project.documents.map((doc: any, idx: number) => (
              <Badge
                key={idx}
                variant="secondary"
                className={cn(
                  "rounded-md text-xs font-medium",
                  doc.status === "completed"
                    ? "bg-green-50 text-green-700 border-green-200"
                    : doc.status === "pending"
                      ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                      : "bg-gray-50 text-gray-700 border-gray-200"
                )}
              >
                {getDocumentLabel(doc.type)}
              </Badge>
            ))
          ) : (
            <span className="text-xs text-muted-foreground italic">No documents yet</span>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="border-t bg-muted/20 px-6 py-3 mt-auto">
        <div className="flex w-full items-center justify-between">
          <div className="text-[11px] text-muted-foreground flex flex-col gap-0.5 ml-1">
            <span>Created: {format(new Date(project.created_at), "dd/MM/yyyy")}</span>
            <span>Updated: {format(new Date(project.updated_at || project.created_at), "dd/MM/yyyy")}</span>
          </div>
          
          {/* More compact actions with dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/project/${project.id}`}>
                  <Eye className="mr-2 h-4 w-4" /> View Project
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/project/${project.id}`}>
                  <Pencil className="mr-2 h-4 w-4" /> Edit Project
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="text-red-600 focus:text-red-600" 
                onClick={() => onDelete(project.id)}
              >
                <Trash2 className="mr-2 h-4 w-4" /> Delete Project
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardFooter>
    </Card>
  );
};
