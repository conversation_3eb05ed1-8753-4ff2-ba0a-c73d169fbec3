"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  Di<PERSON>Title,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";

interface GoogleDriveExportSuccessDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  folderUrl: string | null;
}

export function GoogleDriveExportSuccessDialog({
  isOpen,
  onOpenChange,
  folderUrl,
}: GoogleDriveExportSuccessDialogProps) {
  if (!isOpen || !folderUrl) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Documents Exported Successfully</DialogTitle>
          <DialogDescription>
            Your project documents have been exported to Google Drive.
          </DialogDescription>
        </DialogHeader>
        <div className="p-4 bg-blue-50 rounded-md my-4">
          <div className="flex items-center gap-3 mb-3">
            <img
              src="https://fonts.gstatic.com/s/i/productlogos/drive_2020q4/v8/web-64dp/logo_drive_2020q4_color_2x_web_64dp.png"
              alt="Google Drive"
              className="h-8 w-8"
            />
            <div>
              <h4 className="font-medium">Google Drive</h4>
              <p className="text-sm text-muted-foreground">Documents exported as Google Docs</p>
            </div>
          </div>
          <p className="text-sm mb-3">
            You can now view, edit, and share your documents directly in Google Drive.
          </p>
        </div>
        <DialogFooter className="flex sm:justify-between">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
          <Button
            onClick={() => {
              if (folderUrl) window.open(folderUrl, '_blank');
              onOpenChange(false);
            }}
            className="gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open in Google Drive
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}