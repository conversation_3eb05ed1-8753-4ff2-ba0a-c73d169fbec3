"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertCircle, ExternalLink } from "lucide-react";

interface NotionExplanationDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onConnect: () => void;
}

export function NotionExplanationDialog({
  isOpen,
  onOpenChange,
  onConnect,
}: NotionExplanationDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Connect to Notion</DialogTitle>
          <DialogDescription>
            Before connecting your Notion account, please understand how this integration works.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <h4 className="font-medium">How Provibe works with Notion:</h4>
            <ul className="list-disc pl-5 space-y-2 text-sm">
              <li>Provibe will create a page called "Provibe Exports" in your Notion workspace</li>
              <li>Each project export will be saved as a subpage under "Provibe Exports"</li>
              <li>Documents will be saved as pages under your project page</li>
              <li>You can organize, edit, and share these pages in Notion as needed</li>
            </ul>
          </div>
          <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
            <div className="flex items-center gap-2 text-amber-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Important</span>
            </div>
            <p className="text-sm text-amber-700 mt-1">
              Please do not delete the "Provibe Exports" page as it's needed for future exports.
            </p>
          </div>
        </div>
        <DialogFooter className="flex sm:justify-between">
          <Button
            variant="outline"
            onClick={() => {
              window.open('https://notion.so', '_blank');
            }}
            className="gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open Notion
          </Button>
          <Button
            onClick={() => {
              onOpenChange(false);
              onConnect();
            }}
            className="gap-2"
          >
            <img
              src="/assets/img/integrations/notion-logo.svg"
              alt="Notion"
              className="h-4 w-4"
            />
            Connect to Notion
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}