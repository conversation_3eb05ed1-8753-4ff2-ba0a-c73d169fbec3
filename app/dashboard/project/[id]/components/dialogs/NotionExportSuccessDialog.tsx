"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";

interface NotionExportSuccessDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  pageUrl: string | null;
}

export function NotionExportSuccessDialog({
  isOpen,
  onOpenChange,
  pageUrl,
}: NotionExportSuccessDialogProps) {
  if (!isOpen || !pageUrl) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Documents Exported to Notion</DialogTitle>
          <DialogDescription>
            Your project documents have been exported to Notion.
          </DialogDescription>
        </DialogHeader>
        <div className="p-4 bg-gray-50 rounded-md my-4">
          <div className="flex items-center gap-3 mb-3">
            <img
              src="/assets/img/integrations/notion-logo.svg"
              alt="Notion"
              className="h-8 w-8"
            />
            <div>
              <h4 className="font-medium">Notion</h4>
              <p className="text-sm text-muted-foreground">Documents exported as Notion pages</p>
            </div>
          </div>
          <p className="text-sm mb-3">
            You can now view, edit, and share your documents directly in Notion.
          </p>
        </div>
        <DialogFooter className="flex sm:justify-between">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
          <Button
            onClick={() => {
              if (pageUrl) window.open(pageUrl, '_blank');
              onOpenChange(false);
            }}
            className="gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open in Notion
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}