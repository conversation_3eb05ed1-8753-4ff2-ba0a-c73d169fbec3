"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";

interface GoogleDriveExplanationDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onConnect: () => void;
}

export function GoogleDriveExplanationDialog({
  isOpen,
  onOpenChange,
  onConnect,
}: GoogleDriveExplanationDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Connect to Google Drive</DialogTitle>
          <DialogDescription>
            Before connecting your Google Drive account, please understand how this integration works.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <h4 className="font-medium">How Provibe works with Google Drive:</h4>
            <ul className="list-disc pl-5 space-y-2 text-sm">
              <li>Provibe will create a folder called "Provibe Exports" in your Google Drive</li>
              <li>Each project export will be saved as a subfolder under "Provibe Exports"</li>
              <li>Documents will be converted to Google Docs format</li>
              <li>You can edit, share, and collaborate on these documents directly in Google Drive</li>
            </ul>
          </div>
          <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-blue-500" />
              <span className="font-medium text-sm">Privacy Note</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Provibe only requests access to files it creates. We cannot access your existing Google Drive files.
            </p>
          </div>
        </div>
        <DialogFooter className="flex sm:justify-between">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              onOpenChange(false);
              onConnect();
            }}
            className="gap-2"
          >
            <img
              src="https://fonts.gstatic.com/s/i/productlogos/drive_2020q4/v8/web-64dp/logo_drive_2020q4_color_2x_web_64dp.png"
              alt="Google Drive"
              className="h-4 w-4"
            />
            Connect to Google Drive
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}