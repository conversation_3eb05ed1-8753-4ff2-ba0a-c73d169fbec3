import React, { useEffect, lazy, Suspense } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Loader2 } from 'lucide-react';
// Import custom User type instead of Supabase User
import type { User } from '@/components/auth/auth-provider';

// Lazy load step components for better performance
const Step1 = lazy(() => import('../steps/Step1'));
const Step2 = lazy(() => import('../steps/Step2'));
const Step3 = lazy(() => import('../steps/Step3'));
const Step4 = lazy(() => import('../steps/Step4'));
const Step5 = lazy(() => import('../steps/Step5'));

// Import types that are needed immediately
import type { IdeaFormData } from '../steps/Step1';

interface CurrentStepDisplayProps {
    currentDisplayStep: number;
    // Props for Step 1
    ideaForm: UseFormReturn<IdeaFormData>;
    isRecording: boolean;
    toggleRecording: () => void;
    handleTranscription: (text: string, audioUrl: string | null) => void;
    isRefining: boolean;
    handleRefineIdea: () => Promise<void>;
    projectId?: string; // Optional because it might not be set initially
    isTestUser: boolean;
    projectName: string;
    onProjectNameChange: (name: string) => void;
    clarifyingQuestionsData: any; // Use a more specific type if available
    // Props for Step 2
    ideaText: string;
    // Props for Step 3
    selectedTools: string[];
    onSelectionChange: (newSelection: string[]) => void;
    // Props for Step 4
    projectPlan: string;
    setProjectPlan: (plan: string) => void;
    isGeneratingPlan: boolean;
    setIsGeneratingPlan: (isGenerating: boolean) => void;
    onSaveComplete: () => void; // Callback for Step 4 completion
    // Props for Step 5
    user: User | null;
    refinedIdea: string | null;
    markProjectAsCompleted: () => Promise<void>;
    // Common Props
    navigateToStep: (targetStep: number) => Promise<void>;
    // Props for Step5 dialogs that need to be passed through
    googleDriveConnectedProp?: boolean;
    notionConnectedProp?: boolean;
    setGoogleDriveExplanationOpen?: (isOpen: boolean) => void;
    setNotionExplanationOpen?: (isOpen: boolean) => void;
    setGoogleDriveExportSuccessOpen?: (isOpen: boolean) => void;
    setExportedFolderUrl?: (url: string) => void;
    setNotionExportSuccessOpen?: (isOpen: boolean) => void;
    setNotionExportUrl?: (url: string) => void;

    // New action handlers from page.tsx
    onGoogleDriveAction?: () => void;
    onNotionAction?: () => void;

    isEditMode?: boolean; // Add this prop
}

export const CurrentStepDisplay: React.FC<CurrentStepDisplayProps> = ({
    currentDisplayStep,
    ideaForm,
    isRecording,
    toggleRecording,
    handleTranscription,
    navigateToStep,
    isRefining,
    handleRefineIdea,
    projectId,
    isTestUser,
    projectName,
    onProjectNameChange,
    clarifyingQuestionsData,
    ideaText,
    selectedTools,
    onSelectionChange,
    projectPlan,
    setProjectPlan,
    isGeneratingPlan,
    setIsGeneratingPlan,
    onSaveComplete,
    user,
    refinedIdea,
    markProjectAsCompleted,
    googleDriveConnectedProp,
    notionConnectedProp,
    setGoogleDriveExplanationOpen,
    setNotionExplanationOpen,
    setGoogleDriveExportSuccessOpen,
    setExportedFolderUrl,
    setNotionExportSuccessOpen,
    setNotionExportUrl,
    onGoogleDriveAction, // Destructure new props
    onNotionAction,      // Destructure new props
    isEditMode = false, // Default to false
}) => {
    useEffect(() => {
        console.log(`CurrentStepDisplay: Step changed to ${currentDisplayStep}, isEditMode: ${isEditMode}`);
    }, [currentDisplayStep, isEditMode]);

    // Render the appropriate step based on currentDisplayStep with lazy loading
    const renderCurrentStep = () => {
        const stepLoader = (
            <div className="flex items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                <span className="ml-2 text-sm text-muted-foreground">Loading step {currentDisplayStep}...</span>
            </div>
        );

        switch (currentDisplayStep) {
            case 1:
                return (
                    <Suspense fallback={stepLoader}>
                        <Step1
                            ideaForm={ideaForm}
                            isRecording={isRecording}
                            toggleRecording={toggleRecording}
                            handleTranscription={handleTranscription}
                            navigateToStep={() => navigateToStep(2)}
                            isRefining={isRefining}
                            handleRefineIdea={handleRefineIdea}
                            projectId={projectId}
                            isTestUser={isTestUser}
                            projectName={projectName}
                            onProjectNameChange={onProjectNameChange}
                            clarifyingQuestionsData={clarifyingQuestionsData}
                            isEditMode={isEditMode}
                        />
                    </Suspense>
                );
            case 2:
                return (
                    <Suspense fallback={stepLoader}>
                        <Step2
                            projectId={projectId || ""}
                            navigateToStep={navigateToStep}
                            ideaText={ideaText}
                            isTestUser={isTestUser}
                            isEditMode={isEditMode}
                        />
                    </Suspense>
                );
            case 3:
                return (
                    <Suspense fallback={stepLoader}>
                        <Step3
                            selectedTools={selectedTools}
                            onSelectionChange={onSelectionChange}
                            navigateToStep={navigateToStep}
                            projectId={projectId}
                            isEditMode={isEditMode}
                        />
                    </Suspense>
                );
            case 4:
                return (
                    <Suspense fallback={stepLoader}>
                        <Step4
                            projectId={projectId}
                            selectedTools={selectedTools}
                            projectPlan={projectPlan}
                            setProjectPlan={setProjectPlan}
                            isGeneratingPlan={isGeneratingPlan}
                            setIsGeneratingPlan={setIsGeneratingPlan}
                            navigateToStep={navigateToStep}
                            isTestUser={isTestUser}
                            isEditMode={isEditMode}
                            onSaveComplete={onSaveComplete}
                        />
                    </Suspense>
                );
            case 5:
                return (
                    <Suspense fallback={stepLoader}>
                        <Step5
                            user={user}
                            navigateToStep={navigateToStep}
                            projectId={projectId || ""}
                            projectPlan={projectPlan}
                            refinedIdea={refinedIdea}
                            markProjectAsCompleted={markProjectAsCompleted}
                            isEditMode={isEditMode}
                            googleDriveConnectedProp={googleDriveConnectedProp}
                            notionConnectedProp={notionConnectedProp}
                            setGoogleDriveExplanationOpen={setGoogleDriveExplanationOpen}
                            setNotionExplanationOpen={setNotionExplanationOpen}
                            setGoogleDriveExportSuccessOpen={setGoogleDriveExportSuccessOpen}
                            setExportedFolderUrl={setExportedFolderUrl}
                            setNotionExportSuccessOpen={setNotionExportSuccessOpen}
                            setNotionExportUrl={setNotionExportUrl}
                            onGoogleDriveAction={onGoogleDriveAction}
                            onNotionAction={onNotionAction}
                        />
                    </Suspense>
                );
            default:
                return (
                    <div className="flex items-center justify-center py-12">
                        <div className="text-center">
                            <p className="text-muted-foreground">Invalid step: {currentDisplayStep}</p>
                        </div>
                    </div>
                );
        }
    };

    return (
        <div className="space-y-4">
            {renderCurrentStep()}
        </div>
    );
};
