"use client"

import { lazy, Suspense } from 'react';
import { Loader2 } from 'lucide-react';

// Lazy load all dialog components to reduce initial bundle size
const GoogleDriveExportSuccessDialog = lazy(() => 
  import('./dialogs/GoogleDriveExportSuccessDialog').then(module => ({ 
    default: module.GoogleDriveExportSuccessDialog 
  }))
);

const NotionExportSuccessDialog = lazy(() => 
  import('./dialogs/NotionExportSuccessDialog').then(module => ({ 
    default: module.NotionExportSuccessDialog 
  }))
);

const NotionExplanationDialog = lazy(() => 
  import('./dialogs/NotionExplanationDialog').then(module => ({ 
    default: module.NotionExplanationDialog 
  }))
);

const GoogleDriveExplanationDialog = lazy(() => 
  import('./dialogs/GoogleDriveExplanationDialog').then(module => ({ 
    default: module.GoogleDriveExplanationDialog 
  }))
);

// Loading fallback for dialogs
const DialogLoader = () => (
  <div className="flex items-center justify-center p-4">
    <Loader2 className="h-4 w-4 animate-spin" />
  </div>
);

interface LazyDialogsProps {
  // Google Drive Export Success
  googleDriveExportSuccessOpen: boolean;
  setGoogleDriveExportSuccessOpen: (open: boolean) => void;
  exportedFolderUrl: string;
  
  // Notion Export Success
  notionExportSuccessOpen: boolean;
  setNotionExportSuccessOpen: (open: boolean) => void;
  notionExportUrl: string | null;
  
  // Notion Explanation
  notionExplanationOpen: boolean;
  setNotionExplanationOpen: (open: boolean) => void;
  projectId: string | null;
  
  // Google Drive Explanation
  googleDriveExplanationOpen: boolean;
  setGoogleDriveExplanationOpen: (open: boolean) => void;
}

export function LazyDialogs({
  googleDriveExportSuccessOpen,
  setGoogleDriveExportSuccessOpen,
  exportedFolderUrl,
  notionExportSuccessOpen,
  setNotionExportSuccessOpen,
  notionExportUrl,
  notionExplanationOpen,
  setNotionExplanationOpen,
  projectId,
  googleDriveExplanationOpen,
  setGoogleDriveExplanationOpen
}: LazyDialogsProps) {
  return (
    <>
      {/* Only render dialogs when they're actually needed */}
      {googleDriveExportSuccessOpen && (
        <Suspense fallback={<DialogLoader />}>
          <GoogleDriveExportSuccessDialog
            isOpen={googleDriveExportSuccessOpen}
            onOpenChange={setGoogleDriveExportSuccessOpen}
            folderUrl={exportedFolderUrl}
          />
        </Suspense>
      )}

      {notionExportSuccessOpen && (
        <Suspense fallback={<DialogLoader />}>
          <NotionExportSuccessDialog
            isOpen={notionExportSuccessOpen}
            onOpenChange={setNotionExportSuccessOpen}
            pageUrl={notionExportUrl}
          />
        </Suspense>
      )}

      {notionExplanationOpen && (
        <Suspense fallback={<DialogLoader />}>
          <NotionExplanationDialog
            isOpen={notionExplanationOpen}
            onOpenChange={setNotionExplanationOpen}
            onConnect={() => {
              window.location.href = `/api/auth/notion?projectId=${projectId}`;
            }}
          />
        </Suspense>
      )}

      {googleDriveExplanationOpen && (
        <Suspense fallback={<DialogLoader />}>
          <GoogleDriveExplanationDialog
            isOpen={googleDriveExplanationOpen}
            onOpenChange={setGoogleDriveExplanationOpen}
            onConnect={() => {
              window.location.href = `/api/auth/google?projectId=${projectId}`;
            }}
          />
        </Suspense>
      )}
    </>
  );
}