import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"; // AccordionTrigger might be used for the outer accordion
import * as AccordionPrimitive from "@radix-ui/react-accordion"; // Import primitives for custom header
import { CheckCircle2, Edit2, ChevronDown } from "lucide-react";
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useForm } from 'react-hook-form';
import Step1 from '../steps/Step1';
import Step2 from '../steps/Step2';
import Step3 from '../steps/Step3';
import Step4 from '../steps/Step4';

interface CompletedStepsAccordionProps {
    step1Complete: boolean;
    step2Complete: boolean;
    step3Complete: boolean;
    step4Complete: boolean;
    currentDisplayStep: number;
    refinedIdea: string | null;
    productDetails: any | null;
    codingTools: {
        selectedTools: string[];
    };
    projectPlan: string | null;
    navigateToStep?: (step: number) => void;
    setIsEditMode?: (isEditing: boolean) => void;
    projectId: string;
    ideaText: string;
    isTestUser?: boolean;
    // Add new prop for progress calculation
    calculateProgress: () => number;
}

export const CompletedStepsAccordion: React.FC<CompletedStepsAccordionProps> = ({
    step1Complete,
    step2Complete,
    step3Complete,
    step4Complete,
    currentDisplayStep,
    refinedIdea,
    productDetails,
    codingTools,
    projectPlan,
    navigateToStep,
    setIsEditMode,
    projectId,
    ideaText,
    isTestUser = false,
    // Add the new prop to the destructuring
    calculateProgress,
}) => {
    // Add state to track which step is being edited
    const [editingStep, setEditingStep] = useState<number | null>(null);
    
    // Add state to track which accordion items are open
    const [openAccordionItems, setOpenAccordionItems] = useState<string[]>([]);
    
    // Create form instances outside of conditional rendering
    const step1Form = useForm({
        defaultValues: {
            idea: ideaText || ""
        }
    });

    // Function to handle edit button click
    const handleEditClick = (step: number) => {
        // Open the accordion item if it's not already open
        if (!openAccordionItems.includes(`step-${step}`)) {
            setOpenAccordionItems([...openAccordionItems, `step-${step}`]);
        }
        
        if (setIsEditMode) {
            setIsEditMode(true);
        }
        setEditingStep(step);
        if (navigateToStep) {
            navigateToStep(step);
        }
    };

    // Function to handle save/cancel from edit mode
    const handleEditComplete = (step: number) => {
        setEditingStep(null);
        if (setIsEditMode) {
            setIsEditMode(false);
        }
        // If we were editing a step and navigateToStep is available,
        // navigate back to the current display step
        if (navigateToStep && currentDisplayStep > step) {
            navigateToStep(currentDisplayStep);
        }
    };

    // Define step configurations
    const steps = [
        {
            stepNumber: 1,
            title: "Core Idea",
            isComplete: step1Complete,
            data: refinedIdea,
            renderContent: (data: string | null) => (
                editingStep === 1 ? (
                    // Render the full Step1 component when editing
                    <div className="mt-0">
                        <Step1
                            ideaForm={step1Form}
                            isRecording={false}
                            toggleRecording={() => {}}
                            handleTranscription={() => {}}
                            navigateToStep={(step) => {
                                handleEditComplete(1);
                                if (navigateToStep) navigateToStep(step);
                            }}
                            isEditMode={true}
                            projectId={projectId}
                            isTestUser={isTestUser}
                            handleRefineIdea={async () => {
                                // This is a placeholder - the actual refine logic is in the parent
                                console.log("Refine idea called from accordion");
                            }}
                        />
                    </div>
                ) : (
                    <div className="space-y-2">
                        <p>{data || "Not available."}</p>
                    </div>
                )
            ),
        },
        {
            stepNumber: 2,
            title: "Product Details",
            isComplete: step2Complete,
            data: productDetails,
            renderContent: (data: any) => (
                editingStep === 2 ? (
                    // Render the full Step2 component when editing
                    <div className="mt-0">
                        <Step2
                            projectId={projectId}
                            navigateToStep={(step) => {
                                handleEditComplete(2);
                                if (navigateToStep) navigateToStep(step);
                            }}
                            ideaText={ideaText}
                            isTestUser={isTestUser}
                            isEditMode={true}
                        />
                    </div>
                ) : (
                    // Render summary when not editing
                    <div className="space-y-2">
                        {/* Display product details summary */}
                        {data && (
                            <div className="space-y-4">
                                {data.targetAudience && (
                                    <div>
                                        <p className="font-medium">Target Audience:</p>
                                        <p>{data.targetAudience}</p>
                                    </div>
                                )}
                                
                                {data.problemSolved && (
                                    <div>
                                        <p className="font-medium">Problem Solved:</p>
                                        <p>{data.problemSolved}</p>
                                    </div>
                                )}
                                
                                {data.keyFeatures && data.keyFeatures.length > 0 && (
                                    <div>
                                        <p className="font-medium">Key Features:</p>
                                        <ul className="list-disc pl-5">
                                            {data.keyFeatures.map((feature: string, idx: number) => (
                                                <li key={idx}>{feature}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                                
                                {data.usp && data.usp.length > 0 && (
                                    <div>
                                        <p className="font-medium">Unique Selling Points:</p>
                                        <ul className="list-disc pl-5">
                                            {data.usp.map((point: string, idx: number) => (
                                                <li key={idx}>{point}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                                
                                {data.frontendTech && data.frontendTech.length > 0 && (
                                    <div>
                                        <p className="font-medium">Frontend Technologies:</p>
                                        <ul className="list-disc pl-5">
                                            {data.frontendTech.map((tech: string, idx: number) => (
                                                <li key={idx}>{tech}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                                
                                {data.backendTech && data.backendTech.length > 0 && (
                                    <div>
                                        <p className="font-medium">Backend Technologies:</p>
                                        <ul className="list-disc pl-5">
                                            {data.backendTech.map((tech: string, idx: number) => (
                                                <li key={idx}>{tech}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                )
            ),
        },
        {
            stepNumber: 3,
            title: "AI Coding Tool",
            isComplete: step3Complete,
            data: codingTools,
            renderContent: (data: { selectedTools: string[] }) => (
                editingStep === 3 ? (
                    // Render the full Step3 component when editing
                    <div className="mt-0">
                        <Step3
                            selectedTools={data.selectedTools}
                            onSelectionChange={() => {}}
                            navigateToStep={(step) => {
                                handleEditComplete(3);
                                if (navigateToStep) navigateToStep(step);
                            }}
                            projectId={projectId}
                            isEditMode={true}
                        />
                    </div>
                ) : (
                    <div className="space-y-2">
                        {data.selectedTools && data.selectedTools.length > 0 ? (
                            <ul className="list-disc pl-5">
                                {data.selectedTools.map((tool, idx) => (
                                    <li key={idx}>{tool}</li>
                                ))}
                            </ul>
                        ) : (
                            <p>No tools selected.</p>
                        )}
                    </div>
                )
            ),
        },
        {
            stepNumber: 4,
            title: "Project Outline",
            isComplete: step4Complete,
            data: projectPlan,
            renderContent: (data: string | null) => (
                editingStep === 4 ? (
                    // Render the full Step4 component when editing
                    <div className="mt-0">
                        <Step4
                            projectId={projectId}
                            selectedTools={codingTools.selectedTools}
                            projectPlan={data || ""}
                            setProjectPlan={() => {}}
                            isGeneratingPlan={false}
                            setIsGeneratingPlan={() => {}}
                            navigateToStep={(step) => {
                                handleEditComplete(4);
                                if (navigateToStep) navigateToStep(step);
                            }}
                            isTestUser={isTestUser}
                            isEditMode={true}
                        />
                    </div>
                ) : (
                    <div className="space-y-2">
                        <div className="prose prose-sm max-w-none dark:prose-invert">
                            <ReactMarkdown>{data || "No project plan available."}</ReactMarkdown>
                        </div>
                    </div>
                )
            ),
        },
    ];

    // Get all completed steps
    const completedSteps = steps.filter(step => {
        switch (step.stepNumber) {
            case 1: return step1Complete;
            case 2: return step2Complete;
            case 3: return step3Complete;
            case 4: return step4Complete;
            default: return false;
        }
    });

    // Only show steps that are before the current display step
    const completedStepsToShow = completedSteps.filter(step => 
        step.stepNumber < currentDisplayStep
    );

    // Always include Step 1 if it's complete
    if (step1Complete && !completedStepsToShow.some(step => step.stepNumber === 1)) {
        const step1 = steps.find(step => step.stepNumber === 1);
        if (step1) {
            completedStepsToShow.push(step1);
        }
    }

    // Sort steps by step number
    completedStepsToShow.sort((a, b) => a.stepNumber - b.stepNumber);

    // Only show the master accordion if there are completed steps
    if (completedStepsToShow.length === 0) {
        return null;
    }

    console.log("Completed steps to show:", completedStepsToShow.map(s => s.stepNumber));

    // Add the getStageName function that was in ProjectHeader
    const getStageName = () => {
        if (!step1Complete) return "Core Idea";
        if (!step2Complete) return "Product Details";
        if (!step3Complete) return "AI Coding Tools";
        if (!step4Complete) return "Project Plan";
        return "Document Generation";
    };

    return (
        <Accordion 
            type="single" 
            collapsible 
            className="w-full mb-0" 
            defaultValue="input-summary"
            data-accordion-level="progress-summary"
        >
            <AccordionItem value="input-summary" className="border-0 shadow-none bg-transparent">
                <AccordionTrigger 
                    className="px-3 py-1 text-sm hover:no-underline h-10 flex items-center justify-center"
                    aria-label="Toggle progress summary"
                >
                    <div className="w-2/3 flex items-center pt-2">
                        <div className="w-full">
                            <div className="flex justify-between text-xs">
                                {/* Step labels */}
                                <div className="flex w-full justify-between px-1">
                                    <span className={`${step1Complete ? "text-emerald-600 font-medium" : "text-muted-foreground"}`}>
                                        Idea
                                    </span>
                                    <span className={`${step2Complete ? "text-emerald-600 font-medium" : "text-muted-foreground"}`}>
                                        Details
                                    </span>
                                    <span className={`${step3Complete ? "text-emerald-600 font-medium" : "text-muted-foreground"}`}>
                                        Tools
                                    </span>
                                    <span className={`${step4Complete ? "text-emerald-600 font-medium" : "text-muted-foreground"}`}>
                                        Outline
                                    </span>
                                    {/*}
                                    <span className={`${step4Complete ? "text-emerald-600 font-medium" : "text-muted-foreground"}`}>
                                        Docs
                                    </span>
                                    */}
                                </div>
                            </div>
                            
                            {/* Progress bar with steps */}
                            <div className="h-1.5 w-full bg-muted rounded-full overflow-hidden mt-1 flex">
                                <div 
                                    className={`h-full bg-emerald-500 transition-all duration-300 ${step1Complete ? "w-1/5" : "w-0"}`}
                                ></div>
                                <div 
                                    className={`h-full bg-emerald-500 transition-all duration-300 ${step2Complete ? "w-1/5" : "w-0"}`}
                                ></div>
                                <div 
                                    className={`h-full bg-emerald-500 transition-all duration-300 ${step3Complete ? "w-1/5" : "w-0"}`}
                                ></div>
                                <div 
                                    className={`h-full bg-emerald-500 transition-all duration-300 ${step4Complete ? "w-1/5" : "w-0"}`}
                                ></div>
                                <div 
                                    className={`h-full bg-emerald-500 transition-all duration-300 ${step4Complete && currentDisplayStep === 5 ? "w-1/5" : "w-0"}`}
                                ></div>
                            </div>
                        </div>
                    </div>
                </AccordionTrigger>

                <AccordionContent className="p-0 space-y-2">
                    {/* Level 3: Individual Steps Accordion */}
                    <Accordion 
                        type="multiple" 
                        className="w-full space-y-1" 
                        value={openAccordionItems}
                        onValueChange={setOpenAccordionItems}
                        data-accordion-level="individual-steps"
                    >
                        {completedStepsToShow.map((step) => (
                            <AccordionItem 
                                key={step.stepNumber} 
                                value={`step-${step.stepNumber}`} 
                                className="group border bg-white rounded-md shadow-sm" // Added 'group' for chevron rotation
                                data-step-number={step.stepNumber}
                            >
                                {/* Custom Header using Accordion Primitives */}
                                <AccordionPrimitive.Header className="flex items-center justify-between px-3 py-1 data-[state=open]:border-b">
                                    <AccordionPrimitive.Trigger
                                        className="flex flex-grow items-center gap-2 text-sm hover:no-underline focus:outline-none"
                                        aria-label={`Toggle ${step.title}`}
                                    >
                                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                                        <span>{step.title}</span>
                                        <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200 ml-auto group-data-[state=open]:rotate-180" />
                                    </AccordionPrimitive.Trigger>

                                    {/* Edit Mode Switch - now a sibling to the Trigger, within the Header */}
                                    <div 
                                        className="flex items-center space-x-2 pl-2" // Added pl-2 for spacing
                                        onClick={(e) => e.stopPropagation()}
                                    >
                                        <Label htmlFor={`edit-mode-header-step${step.stepNumber}`} className="text-xs">
                                            Edit
                                        </Label>
                                        <Switch 
                                            id={`edit-mode-header-step${step.stepNumber}`} 
                                            checked={editingStep === step.stepNumber}
                                            onCheckedChange={(checked) => {
                                                if (checked) {
                                                    handleEditClick(step.stepNumber);
                                                } else {
                                                    handleEditComplete(step.stepNumber);
                                                }
                                            }}
                                            size="sm"
                                        />
                                    </div>
                                </AccordionPrimitive.Header>

                                <AccordionContent className={`${editingStep === step.stepNumber ? 'p-0.5' : 'p-3'} text-sm`}>
                                    {/* Render the specific content for this step */}
                                    {step.renderContent(step.data as any)}
                                </AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>
                </AccordionContent>
            </AccordionItem>
        </Accordion>
    );
};
