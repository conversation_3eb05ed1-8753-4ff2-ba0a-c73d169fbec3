"use client";

import React, { useState } from "react";
import { MarkdownRenderer } from "@/components/ui/markdown-renderer";
import Modal from "@/components/ui/modal";
import { <PERSON>, CardHeader, CardContent, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Star, Pencil, Trash2 } from "lucide-react";
import { useAuth } from "@/components/auth/auth-provider";

// Match the more complete TemplateData structure
interface Template { 
  id: string;
  title: string;
  icon: string;
  description: string;
  doc_template: string | null;
  active: boolean;
  ai_prompt: string;
  system_prompt: string;
  user_id?: string | null;
  // display_order?: number; // If you re-add this, include it
}

interface TemplatesContentProps {
  documentTypes: Template[];
  viewMode: 'grid' | 'list';
  onEdit: (template: Template) => void;
  onDelete: (templateId: string) => void;
}

export default function TemplatesContent({ documentTypes, viewMode, onEdit, onDelete }: TemplatesContentProps) {
  const { user } = useAuth();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  // Always available actions
  const handleStar = (templateId: string) => {
    // Implement star toggle functionality here.
    alert(`Star toggled for template ${templateId}`);
  };



  // Call the onEdit prop passed from TemplatesPage
  const handleEditClick = (template: Template) => {
    onEdit(template);
  };

  // Call the onDelete prop passed from TemplatesPage
  const handleDeleteClick = (templateId: string) => {
    onDelete(templateId);
  };


  return (
    <div>
      {/* Grid of template cards */}
      {viewMode === 'grid' ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {documentTypes.map((template) => (
            <Card
              key={template.id}
              className="overflow-hidden hover:shadow cursor-pointer"
              onClick={() => setSelectedTemplate(template)}
            >
              <CardHeader className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">{template.icon}</span>
                    <CardTitle className="text-lg font-bold line-clamp-1">
                      {template.title}
                    </CardTitle>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-4 w-4"
                        >
                          <circle cx="12" cy="12" r="1" />
                          <circle cx="19" cy="12" r="1" />
                          <circle cx="5" cy="12" r="1" />
                        </svg>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleStar(template.id)}>
                        <Star className="mr-2 h-4 w-4" /> Star
                      </DropdownMenuItem>

                      {template.user_id === user?.id && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleEditClick(template); }}>
                            <Pencil className="mr-2 h-4 w-4" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleDeleteClick(template.id); }} className="text-destructive">
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <p className="text-sm text-muted-foreground line-clamp-2">{template.description}</p>
              </CardContent>
              <CardFooter className="border-t p-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedTemplate(template);
                  }}
                >
                  View
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        /* List of template rows */
        <div className="border rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Template</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {documentTypes.map((template) => (
                <tr key={template.id} className="hover:bg-gray-50">
                  <td className="px-6 py-1 whitespace-nowrap text-sm font-medium text-gray-900"> {/* Reduced py-4 to py-1 */}
                    <div className="flex items-center">
                      <span className="text-2xl mr-2">{template.icon}</span>
                      <span>{template.title}</span>
                    </div>
                  </td>
                  <td className="px-6 py-1 text-sm text-gray-500"> {/* Reduced py-4 to py-1 */}
                    <div className="max-w-md truncate">
                      {template.description}
                    </div>
                  </td>
                  <td className="px-6 py-1 whitespace-nowrap text-right text-sm font-medium"> {/* Reduced py-4 to py-1 */}
                    <div className="flex items-center justify-end space-x-2">
                      {/* Star Button */}
                      <Button variant="ghost" size="icon" onClick={() => handleStar(template.id)}>
                         <Star className="h-4 w-4 text-muted-foreground" /> {/* Placeholder color */}
                      </Button>

                      {/* Edit Button (User's templates only) */}
                      {template.user_id === user?.id && (
                         <Button variant="ghost" size="icon" onClick={() => handleEditClick(template)}>
                           <Pencil className="h-4 w-4 text-muted-foreground" /> {/* Placeholder color */}
                        </Button>
                      )}
                      {/* Delete Button (User's templates only) */}
                      {template.user_id === user?.id && (
                        <Button variant="ghost" size="icon" className="text-destructive" onClick={() => handleDeleteClick(template.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                      {/* View Button */}
                       <Button variant="ghost" size="sm" onClick={() => setSelectedTemplate(template)}>
                         View
                       </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal for displaying template content */}
      {selectedTemplate && (
        <Modal 
          onClose={() => setSelectedTemplate(null)}
          contentClassName="w-[80vw] h-[80vh] max-w-[80vw] max-h-[80vh]"
        >
          {/* Modal with size="default" provides p-6. Content goes directly here. */}
          <h2 className="text-xl font-bold mb-4">{selectedTemplate.title} Template</h2>
            <div className="markdown-content">
              {selectedTemplate.doc_template ? (
                <MarkdownRenderer>{selectedTemplate.doc_template}</MarkdownRenderer>
              ) : (
                <p>No template content available.</p>
              )}
            </div>
        </Modal>
      )}
    </div>
  );
}