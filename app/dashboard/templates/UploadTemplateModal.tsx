"use client";

import React, { useState } from "react";
import Modal from "@/components/ui/modal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase-client";
import { Switch } from "@/components/ui/switch"; // <-- New import for Switch
import { Label } from "@/components/ui/label"; // <-- New import for Label
import { nanoid } from "nanoid"; // <-- New import for generating unique IDs

interface TemplateData { // Define a more complete template structure
  id: string; // This is the TEXT unique ID
  title: string;
  icon: string;
  description: string;
  doc_template: string | null;
  active: boolean;
  ai_prompt: string;
  system_prompt: string;
  user_id?: string | null; // user_id is for ownership, not directly edited in form by user
}
interface UploadTemplateModalProps {
  onSuccess?: () => void; // Renamed for broader use (create/update)
  isOpen: boolean;
  onClose: () => void;
  templateToEdit?: TemplateData | null;
}

export default function UploadTemplateModal({ onSuccess, isOpen, onClose, templateToEdit }: UploadTemplateModalProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState("");
  const [id, setId] = useState(""); // Re-add state for the unique text ID
  const [icon, setIcon] = useState("");
  const [description, setDescription] = useState("");
  const [docTemplate, setDocTemplate] = useState("");
  const [active, setActive] = useState(true);
  const [aiPrompt, setAiPrompt] = useState("");
  const [systemPrompt, setSystemPrompt] = useState("");

  const DEFAULT_AI_PROMPT_TEMPLATE = "Please generate content based on the provided template and project input. Make sure to swap out any vairables in the templates with the project specific details. Template: {{user_uploaded_content}}";
  const DEFAULT_SYSTEM_PROMPT = "You are a helpful assistant designed to generate documents.";


  const isEditMode = !!templateToEdit;

  // Effect to reset fields when modal closes and auto-generate ID from title
  React.useEffect(() => {
    if (isOpen && templateToEdit) {
      setTitle(templateToEdit.title);
      setId(templateToEdit.id);
      setIcon(templateToEdit.icon || "");
      setDescription(templateToEdit.description);
      setDocTemplate(templateToEdit.doc_template || "");
      setActive(templateToEdit.active);
      setAiPrompt(templateToEdit.ai_prompt || "");
      setSystemPrompt(templateToEdit.system_prompt || "");
    } else if (!isOpen) {
      // Reset fields when modal closes
      setTitle("");
      setId(""); // Reset ID
      setIcon("");
      setDescription("");
      setDocTemplate("");
      setActive(true);
      setAiPrompt("");
      setSystemPrompt("");
      setLoading(false); // Ensure loading is reset
    }
  }, [isOpen, templateToEdit]);

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setTitle(newTitle);
    if (!isEditMode) { // Only auto-generate ID if not in edit mode
      const newId = newTitle
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[^a-z0-9-]/g, "")
        .substring(0, 50) || nanoid(8);
      setId(newId);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Retrieve current user via Supabase Auth
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      const user = userData.user;
      if (!user) {
        toast({ title: "Authentication Error", description: "You must be logged in to upload a template.", variant: "destructive" });
        setLoading(false);
        return;
      }

      // Basic client-side validation
      if (!id.trim() || !title.trim() || !description.trim()) {
        toast({ title: "Validation Error", description: "ID, Title, and Description are required.", variant: "destructive" });
        setLoading(false);
        return;
      }

      let finalAiPrompt = aiPrompt;
      if (aiPrompt.trim() === "" && !isEditMode) {
        finalAiPrompt = DEFAULT_AI_PROMPT_TEMPLATE.replace("{{user_uploaded_content}}", docTemplate || "");
      }

      let finalSystemPrompt = systemPrompt;
      if (systemPrompt.trim() === "" && !isEditMode) {
        finalSystemPrompt = DEFAULT_SYSTEM_PROMPT;
      }

      const templatePayload = {
        title: title.trim(),
        icon: icon.trim(),
        description: description.trim(),
        doc_template: docTemplate,
        active,
        ai_prompt: finalAiPrompt,
        system_prompt: finalSystemPrompt,
        user_id: user.id, // Always set/update user_id for ownership
      };

      let error;

      if (isEditMode && templateToEdit) {
        // Update existing template
        // Note: We don't update 'id' (text) or 'user_id' typically during an edit,
        // but user_id is included here to ensure it's set if it was somehow null.
        // The primary key for update is `templateToEdit.id` which maps to the `id` (text) column.
        const { error: updateError } = await supabase
          .from("document_types")
          .update(templatePayload)
          .eq("id", templateToEdit.id) // Use the original ID for matching
          .eq("user_id", user.id); // Ensure user can only update their own templates
        error = updateError;
      } else {
        // Insert new template
        const { error: insertError } = await supabase
          .from("document_types")
          .insert({ 
            ...templatePayload, 
            id: id.trim(), 
          }); // id is set only on insert
        error = insertError;
      }

      if (error) {
        if (error.message.includes('duplicate key value violates unique constraint "document_types_id_key"')) {
          toast({
            title: isEditMode ? "Save Failed" : "Upload Failed",
            description: `The ID "${id.trim()}" already exists. Please choose a unique ID.`,
            variant: "destructive",
          });
        } else {
          throw error; // Re-throw other errors
        }
      } else {
        toast({ title: "Success", description: `Template ${isEditMode ? 'updated' : 'uploaded'} successfully!` });
        onSuccess?.(); // Call the callback
        onClose(); // Close the modal
      }
    } catch (err: any) {
      console.error(`Error ${isEditMode ? 'updating' : 'uploading'} template:`, err);
      // Avoid double toast for known unique constraint error
      if (!err.message.includes('duplicate key value violates unique constraint "document_types_id_key"')) {
        toast({
          title: isEditMode ? "Save Failed" : "Upload Failed",
          description: err.message || `Could not ${isEditMode ? 'save' : 'upload'} the template.`,
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };
  
  if (!isOpen) return null;

  return (
        <Modal onClose={() => { if (!loading) onClose(); }} size="full-width">
          {/* The Modal component with size="full-width" now adds its own padding wrapper.
              This div is the direct child and can structure the form.
              If specific margins/paddings are needed for the form elements themselves, add them here or on the form. */}
          <div>
            <h2 className="text-xl font-bold mb-4">{isEditMode ? "Edit Template" : "Upload New Template"}</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="title" className="block text-sm font-medium mb-1">Title</Label>
                <Input
                  id="title"
                  value={title}
                  onChange={handleTitleChange}
                  required
                  placeholder="e.g. Project Proposal"
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="templateId" className="block text-sm font-medium mb-1">Unique ID</Label>
                <Input
                  id="templateId"
                  value={id}
                  onChange={(e) => setId(e.target.value.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '').substring(0,50))}
                  required
                  placeholder="e.g. project-proposal (auto-generated from title, non-editable after creation)"
                  disabled={loading || isEditMode} // Disable if loading or in edit mode
                  readOnly={isEditMode} // Make read-only in edit mode
                />
                 <p className="text-xs text-gray-500 mt-1">
                    Unique identifier for the template. Max 50 chars, lowercase, hyphens for spaces.
                 </p>
              </div>
              <div>
                <Label htmlFor="icon" className="block text-sm font-medium mb-1">Icon (Optional)</Label>
                <Input
                  id="icon"
                  value={icon}
                  onChange={(e) => setIcon(e.target.value)}
                  placeholder="e.g. 📄 or ✨ (max 5 chars)"
                  maxLength={5} // Assuming icon is a short emoji or similar
                  disabled={loading}
                  // 'required' attribute removed to make it optional
                />
              </div>
              <div>
                <Label htmlFor="description" className="block text-sm font-medium mb-1">Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  required
                  placeholder="A brief description of the template."
                  rows={3}
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="docTemplate" className="block text-sm font-medium mb-1">
                  Template Content
                </Label>
                <Textarea
                  id="docTemplate"
                  value={docTemplate}
                  onChange={(e) => setDocTemplate(e.target.value)}
                  rows={8}
                  placeholder="Enter the content for the document template. Can be Markdown, text, etc."
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="aiPrompt" className="block text-sm font-medium mb-1">AI Prompt (Optional)</Label>
                <Textarea
                  id="aiPrompt"
                  value={aiPrompt}
                  onChange={(e) => setAiPrompt(e.target.value)}
                  rows={3}
                  placeholder="Default : Generate a {{title}} for {{productName}} based on the {{template}} and the provided project inputs."
                  disabled={loading}
                />
              </div>
              <div>
                <Label htmlFor="systemPrompt" className="block text-sm font-medium mb-1">System Prompt (Optional)</Label>
                <Textarea
                  id="systemPrompt"
                  value={systemPrompt}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                  rows={3}
                  placeholder="Default : You are Provibe AI, an expert Product Management assistant."
                  disabled={loading}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="active" checked={active} onCheckedChange={setActive} disabled={loading} />
                <Label htmlFor="active">Active</Label>
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (isEditMode ? 'Saving...' : 'Uploading...') : (isEditMode ? 'Save Changes' : 'Upload Template')}
                </Button>
              </div>
            </form>
          </div>
        </Modal>
  );
}