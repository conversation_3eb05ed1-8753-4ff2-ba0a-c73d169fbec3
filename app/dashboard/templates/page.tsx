"use client";

import { useEffect, useState, useMemo } from "react";
import { useAuth } from "@/components/auth/auth-provider";
import { supabase } from "@/lib/supabase-client";
import TemplatesContent from "./TemplatesContent";
import UploadTemplateModal from "./UploadTemplateModal";
// New layout imports
import { DashboardShell } from "@/components/dashboard/dashboard-shell";
import { PageHeader } from "@/components/dashboard/page-header";
import { Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Grid, List } from "lucide-react";

// Define a more complete template structure, matching UploadTemplateModal
interface TemplateData {
  id: string; // This is the TEXT unique ID
  title: string;
  icon: string;
  description: string;
  doc_template: string | null;
  active: boolean;
  ai_prompt: string;
  system_prompt: string;
  user_id?: string | null;
}
export default function TemplatesPage() {
  const { user } = useAuth();
  const [documentTypes, setDocumentTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [page, setPage] = useState(0);
  const [pageSize] = useState(10); // Define page size
  const [hasMore, setHasMore] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<TemplateData | null>(null);

  // Memoized fetch function to prevent recreation on every render
  const fetchTemplates = useMemo(() => {
    return async (currentPage: number, currentSearchQuery: string, currentActiveTab: string) => {
      if (!user) return;
      if (!supabase) {
        console.error("Supabase client not initialized.");
        setLoading(false);
        setIsFetchingMore(false);
        setHasMore(false);
        return;
      }

      setLoading(currentPage === 0); // Show full loading only on initial load
      setIsFetchingMore(currentPage > 0); // Show fetching more indicator

      let query = supabase
        .from("document_types")
        .select("id, title, icon, description, doc_template, active, ai_prompt, system_prompt, user_id") // Select all needed fields
        .or(`user_id.eq.${user.id},user_id.is.null`);

      // Apply search filter
      if (currentSearchQuery) {
        query = query.ilike('title', `%${currentSearchQuery}%`);
      }

      // Apply tab filter
      if (currentActiveTab === "starred") {
        query = query.eq('starred', true);
      } else if (currentActiveTab === "recent") {
        const recentThreshold = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        query = query.gte('updated_at', recentThreshold.toISOString());
      } else if (currentActiveTab === "trash") {
        // Assuming a 'is_trashed' column exists for trash functionality
        // If not, this tab will remain empty as per original logic
        // query = query.eq('is_trashed', true);
      }

      // Apply pagination and limit to check for next page (max 50 for performance)
      const from = currentPage * pageSize;
      const to = from + pageSize - 1;
      query = query.order("display_order").range(from, to).limit(Math.min(pageSize + 1, 50));

      const { data, error } = await query;

      if (error) {
        console.error("Error loading templates:", error);
        setHasMore(false); // Assume no more data on error
      } else {
        // Check if there are more items than the page size, indicating more pages
        const newTemplates = data.slice(0, pageSize);
        const hasMoreData = data.length > pageSize;
        setHasMore(hasMoreData);

        if (currentPage === 0) {
          setDocumentTypes(newTemplates); // Replace for initial load or filter/search change
        } else {
          setDocumentTypes((prev) => [...prev, ...newTemplates]); // Append for pagination
        }
      }
      setLoading(false);
      setIsFetchingMore(false);
    };
  }, [user, pageSize]);

  // Effect for initial load and user changes
  useEffect(() => {
    if (!user) return;
    fetchTemplates(0, searchQuery, activeTab);
  }, [user, fetchTemplates]);

  // Effect for search and tab changes (reset to page 0)
  useEffect(() => {
    if (!user) return;
    setPage(0);
    setDocumentTypes([]);
    setHasMore(true);
    fetchTemplates(0, searchQuery, activeTab);
  }, [searchQuery, activeTab, user, fetchTemplates]);

  // Effect for pagination (page > 0)
  useEffect(() => {
    if (!user || page === 0) return;
    fetchTemplates(page, searchQuery, activeTab);
  }, [page, user, fetchTemplates, searchQuery, activeTab]);

  const refreshTemplatesList = () => {
    setDocumentTypes([]); // Clear current templates
    setPage(0); // Reset to the first page
    setHasMore(true); // Assume there might be data to load
    // The useEffect will trigger a re-fetch due to page changing.
  };

  const handleOpenCreateModal = () => {
    setEditingTemplate(null);
    setIsModalOpen(true);
  };

  const handleEditTemplate = (template: TemplateData) => {
    setEditingTemplate(template);
    setIsModalOpen(true);
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!user) return;
    if (window.confirm("Are you sure you want to delete this template?")) {
      try {
        const { error } = await supabase
          .from("document_types")
          .delete()
          .eq("id", templateId)
          .eq("user_id", user.id); // Ensure user can only delete their own templates

        if (error) throw error;
        refreshTemplatesList();
      } catch (error) {
        console.error("Error deleting template:", error);
      }
    }
  };

  if (!user) {
    return <div>Please log in to view your templates.</div>;
  }

  if (loading) {
    return (
      <div className="space-y-6 w-full min-h-screen pt-3 px-1 md:px-3 lg:px-5 animate-pulse">
        <div className="p-2 bg-card text-card-foreground shadow rounded-md flex flex-row justify-between items-center">
          <h2 className="text-xl font-bold">Templates</h2>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 w-full bg-muted rounded" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-background text-foreground p-4 space-y-4">
      <PageHeader
        className="h-[56px] p-1"
        title={
          <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Templates
          </span>
        }
      >
        <Button onClick={handleOpenCreateModal}>Add Template</Button>
        <UploadTemplateModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSuccess={refreshTemplatesList}
          templateToEdit={editingTemplate}
        />
      </PageHeader>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="space-y-2">
          <div className="flex flex-col space-y-4 md:flex-row md:flex-wrap md:items-center md:space-y-0 md:gap-4"> {/* Use flex-wrap and gap for better responsive row behavior */}
             {/* Tabs List */} {/* Consider adjusting spacing/layout for better alignment */}
             <TabsList className="flex-shrink-0">
               <TabsTrigger value="all">All Templates</TabsTrigger>
               <TabsTrigger value="recent">Recent</TabsTrigger>
               <TabsTrigger value="starred">Starred</TabsTrigger>
               <TabsTrigger value="trash">Trash</TabsTrigger>
             </TabsList>
 
             {/* Search Bar */}
             <div className="relative flex-1 min-w-[200px] md:min-w-[250px]"> {/* Ensure search bar has a minimum width but can grow */}
               <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
               <Input
                 type="search"
                 placeholder="Search templates..."
                 className="pl-8 w-full" // Make input take full width of its container
                 value={searchQuery}
                 onChange={(e) => {
                   setSearchQuery(e.target.value);
                   setPage(0); // Reset page on search change
                   setDocumentTypes([]); // Clear current templates
                   setHasMore(true); // Assume there are more results
                 }}
               />
             </div>

             {/* View Mode Switch */}
             <div className="flex space-x-2 flex-shrink-0">
               <Button
                 variant={viewMode === 'grid' ? 'secondary' : 'outline'}
                 size="icon"
                 onClick={() => setViewMode('grid')}
               >
                 <Grid className="h-4 w-4" />
               </Button>
               <Button
                 variant={viewMode === 'list' ? 'secondary' : 'outline'}
                 size="icon"
                 onClick={() => setViewMode('list')}
               >
                 <List className="h-4 w-4" />
               </Button>
             </div>
          </div>

          {/* Tabs Content */} {/* Keep TabsContent outside the inline flex div if needed for layout */}
          <TabsContent value="all" className="space-y-4">
            <TemplatesContent
              documentTypes={documentTypes}
              viewMode={viewMode}
              onEdit={handleEditTemplate}
              onDelete={handleDeleteTemplate}
            />
          </TabsContent>
          <TabsContent value="recent">
            {viewMode === 'grid' ? (
              <TemplatesContent documentTypes={documentTypes} viewMode={viewMode} onEdit={handleEditTemplate} onDelete={handleDeleteTemplate} />
            ) : (
              <div className="space-y-4">
                <TemplatesContent documentTypes={documentTypes} viewMode={viewMode} onEdit={handleEditTemplate} onDelete={handleDeleteTemplate} />
              </div>
            )}
          </TabsContent>
          <TabsContent value="starred">
            {viewMode === 'grid' ? (
              <TemplatesContent documentTypes={documentTypes} viewMode={viewMode} onEdit={handleEditTemplate} onDelete={handleDeleteTemplate} />
            ) : (
              <div className="space-y-4">
                <TemplatesContent documentTypes={documentTypes} viewMode={viewMode} onEdit={handleEditTemplate} onDelete={handleDeleteTemplate} />
              </div>
            )}
          </TabsContent>
          <TabsContent value="trash">
            {/* For trash, you might want to fetch specifically trashed items */}
            {/* <TemplatesContent 
              documentTypes={documentTypes.filter(doc => doc.is_trashed)} // Example filter
              viewMode={viewMode} 
              onRestore={handleRestoreTemplate} // Example restore handler
              onPermanentDelete={handlePermanentDeleteTemplate} // Example permanent delete
            /> */}
            <div className="flex h-[300px] items-center justify-center">
              <p className="text-center text-muted-foreground">Trash is empty</p>
            </div>
          </TabsContent>
        </Tabs>
      {hasMore && (
        <div className="flex justify-center mt-4">
          <Button onClick={() => setPage(prev => prev + 1)} disabled={isFetchingMore}>
            {isFetchingMore ? "Loading More..." : "Load More"}
          </Button>
        </div>
      )}
    </div>
  );
}