import { OpenAIChatModelId } from "@ai-sdk/openai";

export const aiModel: OpenAIChatModelId = "gpt-4o-mini";

export enum DocumentType {
  PRD = "prd",
  ARCHITECTURE = "architecture",
  USER_FLOW = "user_flow",
  TECH_STACK = "tech_stack",
}

export const DOCUMENT_TYPES = [
  DocumentType.PRD,
  DocumentType.ARCHITECTURE,
  DocumentType.USER_FLOW,
  DocumentType.TECH_STACK,
] as const;

export type DocumentTypeValues = typeof DOCUMENT_TYPES[number];

export type NonPrdDocumentTypeValues = Exclude<DocumentTypeValues, DocumentType.PRD>;

