import { Octokit } from '@octokit/rest';

// GitHub URL pattern for validation
export const githubUrlPattern = /https?:\/\/github\.com\/[\w-]+\/[\w-]+/;

// Helper function to fetch file content safely
async function tryFetchFile(octokit: Octokit, owner: string, repo: string, path: string): Promise<string | null> {
  try {
    const { data } = await octokit.repos.getContent({ owner, repo, path });
    if ('content' in data) {
      return Buffer.from(data.content, 'base64').toString();
    }
    return null;
  } catch {
    return null;
  }
}

// Function to extract owner and repo from GitHub URL
export function extractRepoInfo(githubUrl: string) {
  const url = new URL(githubUrl);
  const [owner, repo] = url.pathname.split('/').slice(1, 3);
  return { owner, repo };
}

// Function to fetch repository metadata and key files
export async function fetchRepoData(githubUrl: string) {
  try {
    const { owner, repo } = extractRepoInfo(githubUrl);
    const octokit = new Octokit();

    // Fetch repository metadata
    const metadata = await octokit.repos.get({ owner, repo });

    const { data: rootContents } = await octokit.repos.getContent({ owner, repo, path: '' });

    const fileContents: Record<string, string | null> = {};
    const directories: string[] = [];

    if (Array.isArray(rootContents)) {
      for (const item of rootContents) {
        if (item.type === 'file') {
          fileContents[item.name] = await tryFetchFile(octokit, owner, repo, item.path);
        } else if (item.type === 'dir') {
          directories.push(item.name);
        }
      }
    }

    // Parse package.json if available
    let dependencies = {};
    let devDependencies = {};
    let scripts = {};

    if (fileContents['package.json']) {
      try {
        const packageJson = JSON.parse(fileContents['package.json']);
        dependencies = packageJson.dependencies || {};
        devDependencies = packageJson.devDependencies || {};
        scripts = packageJson.scripts || {};
      } catch (error) {
        console.error('Error parsing package.json:', error);
      }
    }

    return {
      metadata: metadata.data,
      fileContents,
      directories,
      dependencies,
      devDependencies,
      scripts
    };
  } catch (error) {
    console.error('Error fetching repository data:', error);
    throw new Error('Failed to fetch repository data. Please ensure the URL is correct and the repository is public.');
  }
}