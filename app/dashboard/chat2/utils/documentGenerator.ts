import { DocumentType, DocumentTypeValues, NonPrdDocumentTypeValues } from "../config";
import { generateText } from "ai";
import { createOpenRouter } from '@openrouter/ai-sdk-provider';

// Create OpenRouter client
const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY || '',
  headers: {
    'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://provibe.dev',
    'X-Title': 'Provibe'
  }
});

// Define the model to use with OpenRouter
const aiModel = openrouter("openai/gpt-4o-mini");

// Helper function to call OpenRouter API using AI SDK
const callOpenRouter = async (messages: any[]) => {
  const apiKey = process.env.OPENROUTER_API_KEY;

  if (!apiKey) {
    console.error("OPENROUTER_API_KEY is not set. Document generation will fail.");
    throw new Error("AI service is not configured. Please contact support.");
  }

  try {
    const result = await generateText({
      model: aiModel,
      messages: messages,
      temperature: 0.7,
      maxTokens: 4000,
    });

    return result.text;
  } catch (error) {
    console.error('OpenRouter API request failed:', error);
    throw new Error(`AI service request failed. Please try again later.`);
  }
};

// Function to generate PRD from repository data
export async function generatePRD(repoData: any) {
  const { metadata, fileContents, directories, dependencies, devDependencies, scripts } = repoData;
  
  // Create a structured prompt for the AI
  const prompt = `
Generate a comprehensive Product Requirements Document (PRD) based on the following GitHub repository information:

REPOSITORY INFORMATION:
- Name: ${metadata.name}
- Description: ${metadata.description || 'No description provided'}
- Owner: ${metadata.owner.login}
- Stars: ${metadata.stargazers_count}
- Forks: ${metadata.forks_count}
- Last Updated: ${metadata.updated_at}
- Main Language: ${metadata.language || 'Not specified'}

README CONTENT:
${fileContents['README.md'] || 'No README found'}

PROJECT STRUCTURE:
Top-level directories: ${directories.join(', ')}

DEPENDENCIES:
${JSON.stringify(dependencies, null, 2)}

DEV DEPENDENCIES:
${JSON.stringify(devDependencies, null, 2)}

SCRIPTS:
${JSON.stringify(scripts, null, 2)}

CONFIGURATION FILES:
${fileContents['tsconfig.json'] ? 'TypeScript configuration is present.' : ''}
${fileContents['next.config.js'] || fileContents['next.config.mjs'] ? 'Next.js configuration is present.' : ''}
${fileContents['Dockerfile'] ? 'Docker configuration is present.' : ''}
${fileContents['docker-compose.yml'] ? 'Docker Compose configuration is present.' : ''}

Based on this information, create a detailed PRD with the following sections:
1. Executive Summary
2. Vision & Goals
3. Target Audience
4. Problem Statement
5. Core Features (inferred from the repository)
6. Technical Architecture
   - Frontend (technologies, frameworks)
   - Backend (technologies, frameworks)
   - Database (if identifiable)
   - Integrations (APIs, services)
7. User Flows (if possible to infer)
8. Non-Functional Requirements
9. Key Assumptions & Constraints
10. Development Roadmap (suggested)

Format the document in Markdown with clear headings and bullet points where appropriate.
`;

  try {
    return await callOpenRouter([
      {
        role: "system",
        content: "You are a product manager and technical documentation expert. Your task is to analyze GitHub repositories and create professional Product Requirements Documents."
      },
      {
        role: "user",
        content: prompt
      }
    ]);
  } catch (error) {
    console.error('Error generating PRD:', error);
    throw new Error('Failed to generate PRD. Please try again later.');
  }
}

// Function to generate other document types
export async function generateDocument(repoData: any, documentType: NonPrdDocumentTypeValues) {
  const { metadata, fileContents, directories, dependencies, devDependencies, scripts } = repoData;
  
  // Create a base prompt with repository details
  const baseRepoInfoPrompt = `
REPOSITORY INFORMATION:
- Name: ${metadata.name}
- Description: ${metadata.description || 'No description provided'}
- Owner: ${metadata.owner.login}
- Stars: ${metadata.stargazers_count}
- Forks: ${metadata.forks_count}
- Last Updated: ${metadata.updated_at}
- Main Language: ${metadata.language || 'Not specified'}

README CONTENT:
${fileContents['README.md'] || 'No README found'}

PROJECT STRUCTURE:
Top-level directories: ${directories.join(', ')}

DEPENDENCIES:
${JSON.stringify(dependencies, null, 2)}

DEV DEPENDENCIES:
${JSON.stringify(devDependencies, null, 2)}

SCRIPTS:
${JSON.stringify(scripts, null, 2)}

CONFIGURATION FILES:
${fileContents['tsconfig.json'] ? 'TypeScript configuration is present.' : ''}
${fileContents['next.config.js'] || fileContents['next.config.mjs'] ? 'Next.js configuration is present.' : ''}
${fileContents['Dockerfile'] ? 'Docker configuration is present.' : ''}
${fileContents['docker-compose.yml'] ? 'Docker Compose configuration is present.' : ''}
`;

  // Define document-specific prompts
  const documentPrompts: Record<NonPrdDocumentTypeValues, string> = {
    [DocumentType.ARCHITECTURE]: `
Generate a detailed Architecture Document based on the following GitHub repository information:
${baseRepoInfoPrompt}

Create a comprehensive architecture document with these sections:
1. System Overview
2. Component Architecture
3. Data Flow
4. Technology Stack
5. API Design (if applicable)
6. Database Schema (if applicable)
7. Security Considerations
8. Scalability Strategy
9. Deployment Architecture
10. Development Environment Setup
`,
    [DocumentType.USER_FLOW]: `
Generate a User Flow Document based on the following GitHub repository information:
${baseRepoInfoPrompt}

Create a detailed user flow document with these sections:
1. User Personas
2. Key User Journeys
3. Entry Points
4. Primary Flows
5. Edge Cases and Error Handling
6. Success Criteria
7. User Experience Considerations
`,
    [DocumentType.TECH_STACK]: `
Generate a Technical Stack Document based on the following GitHub repository information:
${baseRepoInfoPrompt}

Create a comprehensive technical stack document with these sections:
1. Frontend Technologies
2. Backend Technologies
3. Database Technologies
4. DevOps & Infrastructure
5. Third-Party Services
6. Testing Framework
7. Performance Considerations
8. Security Implementations
9. Scalability Approach
10. Development Tools & Environment
`
  };
  
  // Use the appropriate prompt. If for some reason PRD is passed here, it will be handled by generatePRD directly.
  // This function is for 'architecture', 'user_flow', 'tech_stack'.
  const prompt = documentPrompts[documentType];

  // This check is technically not needed anymore if documentType is correctly typed as NonPrdDocumentTypeValues,
  // as it ensures prompt will always be found. However, keeping it for robustness or if types are bypassed.
  if (!prompt) {
    console.error(`Invalid document type for generateDocument: ${documentType}. This should not happen with NonPrdDocumentTypeValues.`);
    throw new Error(`Cannot generate document for type: ${documentType}.`);
  }
  
  try {
    return await callOpenRouter([
      {
        role: "system",
        content: "You are a technical documentation expert. Your task is to analyze GitHub repositories and create professional technical documentation."
      },
      {
        role: "user",
        content: prompt
      }
    ]);
  } catch (error) {
    console.error(`Error generating ${documentType} document:`, error);
    throw new Error(`Failed to generate ${documentType} document. Please try again later.`);
  }
}