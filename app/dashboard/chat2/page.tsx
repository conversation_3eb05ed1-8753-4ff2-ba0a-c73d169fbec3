"use client";

import { useChat } from "ai/react";
import Markdown from "markdown-to-jsx";
import { SparklesIcon } from "./icons/SparklesIcon";
import * as React from "react";
import { Message } from "ai";
import { FormEvent, useCallback, useEffect, useState } from "react";
import { v4 as uuidv4 } from 'uuid';
import { CreateIcon } from "./icons/CreateIcon";
import { StopIcon } from "./icons/StopIcon";
import { githubUrlPattern } from "./utils/githubParser";
import { GithubIcon } from "./icons/GithubIcon";
import { DocumentType, DocumentTypeValues } from "./config";

export const dynamic = "force-dynamic";
export const maxDuration = 30;

export default function Page() {
  return <Chat />;
}

function Chat() {
  // Check `app/api/chat/route.ts` for the back-end
  const { messages, input, handleInputChange, isLoading, stop, setMessages } =  
  useChat({
      api: '/dashboard/chat2/api/chat',
      keepLastMessageOnError: true,
    });

  const [processingGithub, setProcessingGithub] = useState(false);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [pendingGithubUrl, setPendingGithubUrl] = useState<string | null>(null);
  const [lastSubmissionTime, setLastSubmissionTime] = useState(0);

  // Simple scroll reference without auto-scroll to avoid infinite loops
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Custom function to filter GitHub URLs from messages before sending to AI
  const getFilteredMessages = () => {
    return messages.filter(message => {
      // Filter out messages that contain GitHub URLs to prevent AI from responding to them
      if (message.role === 'user' && githubUrlPattern.test(message.content)) {
        return false;
      }
      // Also filter out assistant responses about GitHub repositories
      if (message.role === 'assistant' && message.content.includes('GitHub repository')) {
        return false;
      }
      return true;
    });
  };

  // Custom chat submit that filters GitHub-related messages
  const customChatSubmit = async (userMessage: string) => {
     // Add user message locally without triggering an API call
    const userMsg = { role: 'user', content: userMessage, id: uuidv4() } as Message;
    setMessages(prev => [...prev, userMsg]);

    // Get filtered conversation history (without GitHub URLs)
    const filteredMessages = getFilteredMessages();

    // Add the current user message to the filtered history
    const messagesToSend = [
      ...filteredMessages.map(m => ({ role: m.role, content: m.content })),
      { role: 'user' as const, content: userMessage }
    ];

    try {
      const response = await fetch('/dashboard/chat2/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: messagesToSend,
        }),
      });

      if (!response.ok || !response.body) {
        throw new Error('Failed to get response');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = "";
      let streamingContent = "";

      // Create a temporary assistant message for streaming
      const assistantMessageId = uuidv4();
      setMessages(prev => [...prev, { role: 'assistant', content: '', id: assistantMessageId }]);
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.trim() && line.startsWith('0:')) {
            // Extract text content from AI SDK format
            const textMatch = line.match(/^0:"(.*)"/);
            if (textMatch) {
              const text = textMatch[1];
              streamingContent += text;
              fullResponse += text;

              // Update the last message with streaming content
              setMessages(prev => {
                const updated = [...prev];
                const lastIndex = updated.length - 1;
                if (updated[lastIndex]?.role === 'assistant') {
                  updated[lastIndex] = { ...updated[lastIndex], content: streamingContent };
                }
                return updated;
              });
            }
          }
        }
      }
    } catch (error) {
      console.error('Chat error:', error);
            setMessages(prev => [...prev, { role: 'assistant', content: 'Sorry, I encountered an error. Please try again.', id: uuidv4() }]);

    }
  };


  // Function to determine document type from user message
  const getDocumentTypeFromMessage = (message: string): DocumentTypeValues | null => {
    const lowerMessage = message.toLowerCase().trim();

    // More strict matching - require more specific keywords
    if (lowerMessage.includes('prd') ||
        lowerMessage.includes('product requirement') ||
        lowerMessage.includes('requirements document')) {
      return DocumentType.PRD;
    } else if (lowerMessage.includes('architecture') ||
               lowerMessage.includes('system design')) {
      return DocumentType.ARCHITECTURE;
    } else if (lowerMessage.includes('user flow') ||
               lowerMessage.includes('user journey')) {
      return DocumentType.USER_FLOW;
    } else if (lowerMessage.includes('tech stack') ||
               lowerMessage.includes('technology stack') ||
               lowerMessage.includes('technologies')) {
      return DocumentType.TECH_STACK;
    }

    // Return null if no specific type is detected (instead of defaulting to PRD)
    return null;
  };

  // Function to check if a message looks like a document type request
  const isDocumentTypeRequest = (message: string): boolean => {
    const lowerMessage = message.toLowerCase().trim();

    // Only consider very specific, short responses as document type requests
    const exactMatches = ['prd', 'architecture', 'user flow', 'tech stack'];

    // Check for exact matches (case insensitive)
    if (exactMatches.includes(lowerMessage)) {
      return true;
    }

    // Check for slightly longer but still specific phrases
    const specificPhrases = [
      'product requirements document',
      'product requirement document',
      'architecture document',
      'system design document',
      'user flow document',
      'user journey document',
      'tech stack document',
      'technology stack document'
    ];

    // Only match if the message is primarily about requesting a document type
    // and is relatively short (less than 50 characters)
    if (lowerMessage.length < 50) {
      return specificPhrases.some(phrase => lowerMessage.includes(phrase));
    }

    return false;
  };
  
  // Function to generate document from GitHub URL
  const generateDocumentFromGithub = async (githubUrl: string, documentType: DocumentTypeValues) => {
    setProcessingGithub(true);
    
      // Add processing message without triggering API call
    setMessages(prev => [
      ...prev,
      {
        role: 'assistant',
        content: `Generating ${documentType.toUpperCase()} document from GitHub repository, please wait... This may take a minute.`,
        id: uuidv4(),
      } as Message,
    ]);
    
    try {
      // Call GitHub API
      const response = await fetch('/dashboard/chat2/api/github', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          githubUrl,
          documentType: documentType, // Send the enum value
          projectId: selectedProject,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process GitHub repository');
      }
      
      const data = await response.json();
      
      // Format document type for display
      let documentTypeDisplay: string;
      switch (documentType) {
        case DocumentType.PRD:
          documentTypeDisplay = "PRD";
          break;
        case DocumentType.ARCHITECTURE:
          documentTypeDisplay = "Architecture";
          break;
        case DocumentType.USER_FLOW:
          documentTypeDisplay = "User Flow";
          break;
        case DocumentType.TECH_STACK:
          documentTypeDisplay = "Tech Stack";
          break;
        default:
          // This case should ideally not be reached if documentType is always a DocumentTypeValues
          // However, to satisfy TypeScript and provide a fallback:
          const exhaustiveCheck: never = documentType;
          documentTypeDisplay = String(exhaustiveCheck).toUpperCase(); // Fallback, though `never` implies it won't happen
      }
      
      // Update the last message with the generated document
      const successMessage = selectedProject
        ? `# ${documentTypeDisplay} Generated from GitHub Repository\n\nI've analyzed the repository and created a ${documentTypeDisplay} document. It has been saved to your project.\n\n${data.document.content}`
        : `# ${documentTypeDisplay} Generated from GitHub Repository\n\n${data.document.content}`;
      
      // Replace the "generating" message with the result
      const updatedMessages = [...messages];
      updatedMessages.pop(); // Remove the "generating" message
      
      const finalMessages = [...updatedMessages, { role: 'assistant', content: successMessage, id: uuidv4() } as Message];
      setMessages(finalMessages);
      
    } catch (error: any) {
      // Replace the "generating" message with the error
      const currentMessages = [...messages]; // get fresh messages state
      currentMessages.pop(); // Remove the "generating" message
      
      const errorResponseMessage = {
        role: 'assistant',
        content: `Error: ${error.message || 'Failed to process GitHub repository'}. Please ensure the URL is correct and the repository is public.`,
        id: uuidv4() // Ensure unique ID
      } as Message;
      setMessages([...currentMessages, errorResponseMessage]);
    } finally {
      setProcessingGithub(false);
      setPendingGithubUrl(null); // Clear GitHub context after generation attempt
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!input.trim()) return;

    // Prevent multiple simultaneous submissions
    if (isLoading || processingGithub) return;

    // Debounce submissions to prevent rapid-fire requests
    const now = Date.now();
    if (now - lastSubmissionTime < 1000) { // 1 second debounce
      return;
    }
    setLastSubmissionTime(now);

    // Priority 1: Is the input a GitHub URL?
    if (githubUrlPattern.test(input)) {
      console.log('[Chat Flow] GitHub URL detected:', input);
      setPendingGithubUrl(input);
  // Add GitHub URL as user message and assistant prompt locally
      setMessages(prev => [
        ...prev,
        { role: 'user', content: input, id: uuidv4() } as Message,
        {
          role: 'assistant',
          content: `I see you've shared a GitHub repository. What type of document would you like me to generate from it? Options include:\n\n- **${DocumentType.PRD.toUpperCase()}** (Product Requirements Document)\n- **${DocumentType.ARCHITECTURE.toUpperCase()}** Document\n- **${DocumentType.USER_FLOW.toUpperCase().replace('_', ' ')}** Document\n- **${DocumentType.TECH_STACK.toUpperCase().replace('_', ' ')}** Document\n\nJust type the document type you want, or ask me anything else to continue our regular conversation.`,
          id: uuidv4(),
        } as Message,
      ]);

      handleInputChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
      return;
    } else if (pendingGithubUrl) {
      // Priority 2: A GitHub URL is pending - check if this is a document type request
      const potentialDocType = getDocumentTypeFromMessage(input);
      const isDocRequest = isDocumentTypeRequest(input);

      console.log('[Chat Flow] GitHub mode - input:', input);
      console.log('[Chat Flow] Potential doc type:', potentialDocType);
      console.log('[Chat Flow] Is doc request:', isDocRequest);

      if (potentialDocType && isDocRequest) {
        // This looks like a document type request - process it
        console.log('[Chat Flow] Processing document generation');
        setMessages(prev => [...prev, { role: 'user', content: input, id: uuidv4() }]);        await generateDocumentFromGithub(pendingGithubUrl, potentialDocType);
        handleInputChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
        // pendingGithubUrl will be cleared in generateDocumentFromGithub's finally block
        return;
      } else {
        // This doesn't look like a document type request - clear GitHub mode and process as regular chat
        console.log('[Chat Flow] Exiting GitHub mode, processing as regular chat');
        setPendingGithubUrl(null);
        setMessages(prev => [
          ...prev,
          {
            role: 'assistant',
            content: 'I\'ll continue with our regular conversation. If you want to generate a document from the GitHub repository later, just paste the URL again.',
            id: uuidv4(),
          } as Message,
        ]);        // Process this message as regular chat using custom submit
        await customChatSubmit(input);
        handleInputChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
        // Manually scroll to bottom after sending message
        setTimeout(() => {
          messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
        }, 100);
        return;
      }
    }

    // Priority 3: Generic message (no GitHub context)
    // For regular chat messages, use custom submit that filters GitHub URLs
    console.log('[Chat Flow] Processing regular chat message:', input);
    await customChatSubmit(input);
    handleInputChange({ target: { value: '' } } as React.ChangeEvent<HTMLInputElement>);
    // Manually scroll to bottom after sending message
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  return (
    <div className="relative w-full mx-auto h-screen flex flex-col bg-gray-50">
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-[740px] mx-auto px-8 py-6 flex flex-col gap-4">
          {messages.map((message) => (
            <MessageLine key={message.id} message={message} />
          ))}
          {messages.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">
                <SparklesIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
              </div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">Welcome to Provibe AI</h3>
              <p className="text-sm text-gray-600">
                Ask me questions or paste a GitHub repository URL to generate documentation.
              </p>
            </div>
          )}
          {/* Add padding at bottom so last message isn't hidden behind input */}
          <div className="h-20" />
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Fixed input form at bottom */}
      <div className="border-t bg-white sticky bottom-0 shadow-lg">
        <form
          onSubmit={handleSubmit}
          className="max-w-[740px] mx-auto w-full"
        >
          <div className="mx-8 my-4 relative">
            <input
              placeholder={isLoading || processingGithub
                ? "Generating…"
                : pendingGithubUrl
                  ? "Type document type (PRD, Architecture, etc.) or ask anything else..."
                  : "Ask a question or paste a GitHub repository URL..."}
              className="border block w-full p-2 pl-3 rounded-lg outline-none transition-all focus:outline-indigo-500 disabled:bg-gray-50 disabled:outline-none"
              name="prompt"
              value={input}
              onChange={handleInputChange}
              disabled={isLoading || processingGithub}
              autoFocus={true}
            />
            <button
              className="absolute right-0 px-2 top-0 bottom-0 transition-colors rounded-r-lg border border-transparent hover:border-gray-200 hover:disabled:border-transparent hover:bg-gray-100 hover:disabled:bg-transparent"
              onClick={isLoading ? stop : undefined}
              disabled={(!isLoading && !processingGithub && !input) || processingGithub}
            >
              {isLoading || processingGithub ? (
                <StopIcon className="h-4 text-red-500 pointer-events-none" />
              ) : githubUrlPattern.test(input) ? (
                <GithubIcon className="h-4 text-gray-700 pointer-events-none" />
              ) : (
                <SparklesIcon
                  style={isLoading || processingGithub || !input ? { opacity: 0.6 } : {}}
                  className="h-4 text-indigo-500 pointer-events-none"
                />
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

function MessageLine({ message }: { message: Message }) {

  const [title, setTitle] = useState("");
  const [content, setContent] = useState(message.content);
  const [loading, setLoading] = useState(false);

  // If the message starts with an H1 heading (#), extract it as the title
  useEffect(() => {
    const match = message.content.match(/^#\s(.+)/);
    if (match) {
      setTitle(match[1]);
      setContent(message.content.replace(/^#\s.+/, "").trim());
    } else {
      setTitle("");
      setContent(message.content);
    }
  }, [message.content]);

  // Create new document with content/title and redirect
  const handleSubmit = useCallback(
    async (e: FormEvent) => {
      e.preventDefault();
      setLoading(true);
      try {
        // For now, just show an alert - you can replace this with your own document creation logic
        alert(`Document "${title || "Untitled document"}" would be created with content: ${content.substring(0, 100)}...`);
        setLoading(false);
      } catch (error) {
        console.error("Error creating document:", error);
        setLoading(false);
      }
    },
    [content, title]
  );

  return (
    <div key={message.id}>
      {message.role === "user" ? (
        // Your messages
        <div className="flex justify-end">
          <div className="bg-blue-500 text-white rounded-2xl py-2 px-4 max-w-xs lg:max-w-md shadow-sm">
            {content}
          </div>
        </div>
      ) : (
        // AI messages
        <div className="flex flex-col gap-2">
          <div className="border rounded-2xl shadow-sm bg-white">
            {title ? (
              <div className="font-semibold border-b px-4 py-3 pr-2 text-base flex justify-start items-center gap-1.5 bg-gray-50 rounded-t-2xl">
                <span className="text-gray-900">{title}</span>
                <form onSubmit={handleSubmit}>
                  <button
                    disabled={loading}
                    className="font-normal text-gray-500 hover:text-gray-700 hover:bg-gray-100 disabled:hover:text-gray-500 disabled:hover:bg-transparent transition-colors rounded-lg py-1 px-1.5 flex gap-1 items-center disabled:opacity-70"
                  >
                    <CreateIcon className="w-3 h-3 opacity-70" />
                    {loading ? "Creating…" : "Create"}
                  </button>
                </form>
              </div>
            ) : null}

            {/*Render markdown message as HTML */}
            <div className="px-4 py-3">
              <Markdown
                options={{
                  forceBlock: true,
                  overrides: {
                    h1: {
                      props: {
                        className: 'text-2xl font-bold mb-4 text-gray-900 border-b pb-2'
                      }
                    },
                    h2: {
                      props: {
                        className: 'text-xl font-semibold mb-3 mt-6 text-gray-800'
                      }
                    },
                    h3: {
                      props: {
                        className: 'text-lg font-medium mb-2 mt-4 text-gray-800'
                      }
                    },
                    p: {
                      props: {
                        className: 'mb-3 text-gray-700 leading-relaxed'
                      }
                    },
                    ul: {
                      props: {
                        className: 'list-disc list-inside mb-3 space-y-1 text-gray-700'
                      }
                    },
                    ol: {
                      props: {
                        className: 'list-decimal list-inside mb-3 space-y-1 text-gray-700'
                      }
                    },
                    li: {
                      props: {
                        className: 'mb-1'
                      }
                    },
                    code: {
                      props: {
                        className: 'bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-800'
                      }
                    },
                    pre: {
                      props: {
                        className: 'bg-gray-100 p-3 rounded-lg overflow-x-auto mb-3'
                      }
                    },
                    blockquote: {
                      props: {
                        className: 'border-l-4 border-gray-300 pl-4 italic text-gray-600 mb-3'
                      }
                    },
                    strong: {
                      props: {
                        className: 'font-semibold text-gray-900'
                      }
                    },
                    em: {
                      props: {
                        className: 'italic text-gray-700'
                      }
                    },
                    table: {
                      props: {
                        className: 'min-w-full border-collapse border border-gray-300 mb-3'
                      }
                    },
                    th: {
                      props: {
                        className: 'border border-gray-300 px-3 py-2 bg-gray-50 font-semibold text-left'
                      }
                    },
                    td: {
                      props: {
                        className: 'border border-gray-300 px-3 py-2'
                      }
                    }
                  }
                }}
              >
                {content}
              </Markdown>
            </div>
          </div>

          {title && (
            <form onSubmit={handleSubmit}>
              <button
                disabled={loading}
                className="bg-blue-500 hover:bg-blue-600 text-white transition-colors rounded-lg py-2 px-4 flex gap-2 items-center disabled:opacity-70 hover:disabled:bg-blue-500 shadow-sm"
              >
                <CreateIcon className="w-4 h-4" />
                {loading ? "Creating…" : "Create document"}
              </button>
            </form>
          )}
        </div>
      )}
    </div>
  );
}
