import { NextRequest, NextResponse } from 'next/server';
import { fetchRepoData } from '../../utils/githubParser';
import { generatePRD, generateDocument } from '../../utils/documentGenerator';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { githubUrlPattern } from '../../utils/githubParser';
import { DocumentType, DocumentTypeValues } from '../../config';

export async function POST(req: NextRequest) {
  try {
    const { githubUrl, documentType = DocumentType.PRD, projectId } = await req.json() as { githubUrl: string; documentType?: DocumentTypeValues; projectId?: string };
    
    // Validate GitHub URL
    if (!githubUrlPattern.test(githubUrl)) {
      return NextResponse.json(
        { error: 'Invalid GitHub repository URL' },
        { status: 400 }
      );
    }
    
    // Fetch repository data
    const repoData = await fetchRepoData(githubUrl);
    
    // Generate document based on type
    let documentContent;
    let documentTitle;
    
    switch (documentType) {
      case DocumentType.PRD:
        documentContent = await generatePRD(repoData);
        documentTitle = `PRD - ${repoData.metadata.name}`;
        break;
      case DocumentType.ARCHITECTURE:
        documentContent = await generateDocument(repoData, DocumentType.ARCHITECTURE);
        documentTitle = `Architecture - ${repoData.metadata.name}`;
        break;
      case DocumentType.USER_FLOW:
        documentContent = await generateDocument(repoData, DocumentType.USER_FLOW);
        documentTitle = `User Flow - ${repoData.metadata.name}`;
        break;
      case DocumentType.TECH_STACK:
        documentContent = await generateDocument(repoData, DocumentType.TECH_STACK);
        documentTitle = `Tech Stack - ${repoData.metadata.name}`;
        break;
      default:
        // Should not happen if documentType is validated or typed correctly from request
        documentContent = await generatePRD(repoData);
        documentTitle = `PRD - ${repoData.metadata.name}`;
    }
    
    // If projectId is provided, save the document to the database
    if (projectId) {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const { data: userData } = await supabase.auth.getUser();
      
      if (userData?.user) {
        const { data, error } = await supabase
          .from('project_documents')
          .insert({
            project_id: projectId,
            title: documentTitle,
            content: documentContent,
            type: documentType,
            status: 'completed',
            user_id: userData.user.id,
            source_type: 'github_integration',
            metadata: {
              github_url: githubUrl,
              repo_name: repoData.metadata.name,
              repo_owner: repoData.metadata.owner.login
            }
          })
          .select('id, title')
          .single();
          
        if (error) {
          console.error('Error saving document to database:', error);
        } else {
          // Return document with database ID
          return NextResponse.json({
            success: true,
            document: {
              id: data.id,
              title: data.title,
              content: documentContent,
              type: documentType
            }
          });
        }
      }
    }
    
    // Return generated document
    return NextResponse.json({
      success: true,
      document: {
        title: documentTitle,
        content: documentContent,
        type: documentType
      }
    });
    
  } catch (error: any) {
    console.error('Error processing GitHub repository:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to process GitHub repository' },
      { status: 500 }
    );
  }
}