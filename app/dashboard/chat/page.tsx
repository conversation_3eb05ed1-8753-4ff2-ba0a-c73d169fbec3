"use client"

import { Suspense, useState, useEffect, useMemo } from "react"
import { createClientComponentClient, SupabaseClient } from '@supabase/auth-helpers-nextjs';
import { useChat, ALL_PROJECTS_ITEM_VALUE } from "../../../components/chat/ChatProvider"
import { Artifact } from "@/components/artifact"
import { useArtifact } from "@/hooks/use-artifact"
import type { Attachment } from "ai"
// We will lay out MessageList and MessageInput directly instead of using a generic Chat component
// import { Chat } from "@/components/chat/chat" 
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { PageHeader } from "@/components/dashboard/page-header"
import { MessageList } from "@/components/chat/message-list"
import { MessageInput } from "@/components/chat/message-input"
import { PromptSuggestions } from "@/components/chat/inline-prompt-suggestions"
import { useAutoScroll } from "@/hooks/use-auto-scroll"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/copy-button"
import { ThumbsUp, ThumbsDown } from "lucide-react"

export default function ChatPage() {
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    chatLoading,
    stop,
    append,
    setMessages,
    selectedModel,
    setSelectedModel,
    selectedProject,
    setSelectedProject,
    selectedDocType,
    setSelectedDocType,
    projects,
    documentTypes,
    isLoading,
    selectedAgent,
    setSelectedAgent,
    agents,
    availableModels, // Added from useChat for model selector
  } = useChat()

  const [projectDocuments, setProjectDocuments] = useState<any[]>([]);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | undefined>();
  const [files, setFiles] = useState<File[]>([]); // For MessageInput attachments
  const { artifact } = useArtifact();
  const [attachments, setAttachments] = useState<Attachment[]>([]);

  const messageOptions = (message: any) => ({
    actions: (
      <>
        <div className="border-r pr-1">
          <CopyButton
            content={message.content}
            copyMessage="Copied response to clipboard!"
          />
        </div>
        <Button
          size="icon"
          variant="ghost"
          className="h-6 w-6"
          onClick={() => console.log('vote up', message.id)}
        >
          <ThumbsUp className="h-4 w-4" />
        </Button>
        <Button
          size="icon"
          variant="ghost"
          className="h-6 w-6"
          onClick={() => console.log('vote down', message.id)}
        >
          <ThumbsDown className="h-4 w-4" />
        </Button>
      </>
    ),
  })

  useEffect(() => {
    const supabase = createClientComponentClient();
    
    // If no project is selected, or "All Projects" is selected, don't fetch specific documents.
    if (!selectedProject || selectedProject === ALL_PROJECTS_ITEM_VALUE) {
      setProjectDocuments([]);
      return;
    }

    supabase
      .from("project_documents")
      .select("id, title")
      .eq("project_id", selectedProject)
      .then(({ data, error }) => {
        if (error) {
          console.error("Supabase error:", error);
          setProjectDocuments([]);
        } else {
          setProjectDocuments(data ?? []);
        }
      });
  }, [selectedProject]);

  const ragContext = useMemo(() => {
    if (selectedProject && selectedDocumentId) {
      return { projectId: selectedProject, documentId: selectedDocumentId };
    }
    return null;
  }, [selectedProject, selectedDocumentId]);

  const isEmpty = messages.length === 0;
  const messageListRef = useAutoScroll([messages]); // For auto-scrolling the message list

  return (
    <div className="flex h-full w-full">
      <div 
      className="flex flex-col flex-1 bg-background text-foreground p-4 gap-4"
      >

      <PageHeader
        className="min-h-[56px] p-1" // Allow height to grow if content wraps
        title={
          <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Chat
          </span>
        }
      >
        <div className="flex flex-wrap items-center gap-1"> {/* Added flex-wrap for selectors */}
          <Select value={selectedAgent || ""} onValueChange={setSelectedAgent}>
            <SelectTrigger className="h-8 min-w-[120px] flex-1 xs:flex-none xs:w-auto sm:w-[150px] text-xs truncate"> {/* Responsive width */}
              <SelectValue placeholder="Select agent" />
            </SelectTrigger>
            <SelectContent>
              {(agents || []).map((agent) => (
                <SelectItem key={agent.id} value={agent.id} className="text-xs">
                  <div className="flex items-center">
                    <span className="mr-1 sm:mr-2">{agent.icon}</span> {/* Adjusted margin for smaller screens */}
                    <span className="truncate">{agent.name}</span> {/* Ensure agent name truncates if too long */}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
 
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="h-8 min-w-[120px] flex-1 xs:flex-none xs:w-auto sm:w-[150px] text-xs truncate"> {/* Responsive width */}
              <SelectValue placeholder="Select model" />
            </SelectTrigger>
            <SelectContent>
              {(availableModels || []).map((model) => (
                <SelectItem key={model.id} value={model.id} className="text-xs">
                  <span className="truncate">{model.name}</span> {/* Ensure model name truncates */}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
 
          <Select value={selectedProject || ""} onValueChange={setSelectedProject}>
            <SelectTrigger className="h-8 min-w-[120px] flex-1 xs:flex-none xs:w-auto sm:w-[150px] text-xs truncate"> {/* Responsive width */}
              <SelectValue placeholder="Select project" />
            </SelectTrigger>
            <SelectContent>
              {(projects || []).map((project) => (
                <SelectItem key={project.id} value={project.id} className="text-xs">
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {selectedProject && selectedProject !== ALL_PROJECTS_ITEM_VALUE && (projectDocuments || []).length > 0 && (
            <Select value={selectedDocumentId || ""} onValueChange={setSelectedDocumentId}>
              <SelectTrigger className="h-8 min-w-[120px] flex-1 xs:flex-none xs:w-auto sm:w-[150px] text-xs truncate"> {/* Responsive width */}
                <SelectValue placeholder="Select document" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="" className="text-xs">No specific document</SelectItem>
                {projectDocuments.map((doc) => (
                  <SelectItem key={doc.id} value={doc.id} className="text-xs">
                    <span className="truncate">{doc.title}</span> {/* Ensure document title truncates */}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

        </div>

      </PageHeader>
      
      {/* The Chat component will now be the main content area below the PageHeader */}
      {/* This container takes the remaining space due to flex-1, and is a flex column itself for message list and input. */}
      {/* overflow-hidden is important for when content exceeds available space. */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Message display area */}
        <div ref={messageListRef.containerRef} className="flex-1 overflow-y-auto p-4 space-y-4">
          {isEmpty ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              
              <PromptSuggestions
                label="Get started with these prompts:"
                append={append} // Pass the append function from useChat
                suggestions={[
                  "Create a new project",
                  "Help me create a PRD for my idea",
                  "Brainstorm feature ideas for my product",
                  "Give me feedback on my product idea",
                  "Draft a user story for login functionality",             
                ]}
                className="w-full max-w-lg" // Optional: Adjust width as needed
              />
            </div>
          ) : (
            <MessageList messages={messages} />
          )}
        </div>

        {/* Message input area */}
        <div className="p-4 bg-background">
          <MessageInput
            value={input}
            onChange={handleInputChange}
            onSend={handleSubmit}
            isGenerating={chatLoading}
            allowAttachments={true} // Set to true if you want to allow attachments
            files={files}
            setFiles={setFiles}
            // The MessageInput will use the selectedAgent, selectedModel, etc. from the ChatProvider context
            // when handleSubmit is called. No need to pass chatSettings if selectors are in PageHeader.
            // If MessageInput has its own internal selectors (it might), you'd pass chatSettings here.
            // For now, assuming selectors in PageHeader are the source of truth.
          />
        </div>
      </div>
      {artifact.isVisible && (
        <div className="hidden md:block h-full p-1 w-1/3">
          <Artifact
            chatId=""
            input={input}
            setInput={() => {}}
            handleSubmit={handleSubmit}
            status={chatLoading ? 'streaming' : 'idle'}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            setMessages={setMessages}
            append={append}
            reload={() => {}}
            votes={undefined}
            isReadonly={false}
            selectedVisibilityType="private"
          />
        </div>
      )}
    </div>
  </div>
  )
}
