// /app/dashboard/chat/layout.tsx
'use client'

import React from 'react';
import { ChatProvider } from '../../../components/chat/ChatProvider';

export default function ChatLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ChatProvider>
      {/* This div establishes the full viewport height and flex column layout */}
      <div className="flex flex-col h-screen w-full">{children}</div>
    </ChatProvider>
  );
}
