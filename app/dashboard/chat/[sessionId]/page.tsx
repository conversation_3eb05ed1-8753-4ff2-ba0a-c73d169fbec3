"use client"

import { useState, useEffect, useMemo } from "react"
import { useParams } from "next/navigation"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useChat, ALL_PROJECTS_ITEM_VALUE } from "../../../../components/chat/ChatProvider"
import { ChatHeader } from "@/components/chat/chat-header"
import { MessageList } from "@/components/chat/message-list"
import { MessageInput } from "@/components/chat/message-input"
import { PromptSuggestions } from "@/components/chat/prompt-suggestions"
import { useAutoScroll } from "@/hooks/use-auto-scroll"
import { createNewProject } from "@/app/actions/project-actions"
import { useRouter } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { CopyButton } from "@/components/ui/copy-button"
import { ThumbsUp, ThumbsDown } from "lucide-react"
import { Artifact } from "@/components/artifact"
import { useArtifact } from "@/hooks/use-artifact"
import type { Attachment } from "ai"

export default function ChatPage() {
  const { sessionId } = useParams()

  // Chat context/state from hook
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    chatLoading,
    stop,
    append,
    setMessages,
    selectedModel,
    setSelectedModel,
    selectedProject,
    setSelectedProject,
    selectedDocType,
    setSelectedDocType,
    projects,
    documentTypes,
    isLoading,
    selectedAgent,
    setSelectedAgent,
    agents,
    selectedDocumentId,
    setSelectedDocumentId,
  } = useChat()

  const [projectDocuments, setProjectDocuments] = useState<Array<{ id: string; title: string }>>([])
  const [files, setFiles] = useState<Array<File>>([])
  const { artifact } = useArtifact()
  const [attachments, setAttachments] = useState<Attachment[]>([])

  const messageOptions = (message: any) => ({
    actions: (
      <>
        <div className="border-r pr-1">
          <CopyButton
            content={message.content}
            copyMessage="Copied response to clipboard!"
          />
        </div>
        <Button
          size="icon"
          variant="ghost"
          className="h-6 w-6"
          onClick={() => console.log('vote up', message.id)}
        >
          <ThumbsUp className="h-4 w-4" />
        </Button>
        <Button
          size="icon"
          variant="ghost"
          className="h-6 w-6"
          onClick={() => console.log('vote down', message.id)}
        >
          <ThumbsDown className="h-4 w-4" />
        </Button>
      </>
    ),
  })

  // Sidebar collapsed state
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  useEffect(() => {
    const updateSidebarState = () => {
      const collapsed = localStorage.getItem("sidebarCollapsed")
      setIsSidebarCollapsed(collapsed === "true")
    }
    updateSidebarState()
    window.addEventListener("storage", updateSidebarState)
    return () => window.removeEventListener("storage", updateSidebarState)
  }, [])


  const sidebarWidth = isSidebarCollapsed ? 56 : 240

  useEffect(() => {
    const supabase = createClientComponentClient()
    if (!selectedProject || selectedProject === ALL_PROJECTS_ITEM_VALUE) {
      setProjectDocuments([])
      setSelectedDocumentId("")
      return
    }

    let isCancelled = false;

    const fetchDocuments = async () => {
      try {
        const { data, error } = await supabase
          .from("project_documents")
          .select("id, title")
          .eq("project_id", selectedProject)
          .limit(50); // Add limit for performance

        if (!isCancelled) {
          if (error) {
            console.error("Supabase error fetching project documents.", error)
            setProjectDocuments([])
          } else {
            setProjectDocuments(data ?? [])
          }
          setSelectedDocumentId("")
        }
      } catch (err) {
        if (!isCancelled) {
          console.error("Error fetching documents:", err);
          setProjectDocuments([]);
          setSelectedDocumentId("");
        }
      }
    };

    fetchDocuments();

    return () => {
      isCancelled = true;
    };
  }, [selectedProject, setSelectedDocumentId])

  const ragContext = useMemo(() => {
    if (selectedProject && selectedProject !== ALL_PROJECTS_ITEM_VALUE && selectedDocumentId) {
      return { projectId: selectedProject, documentId: selectedDocumentId }
    }
    return null
  }, [selectedProject, selectedDocumentId])
  
  // Use the auto-scroll hook to get a ref that auto-scrolls when messages update
  const messageListRef = useAutoScroll([messages])

  const router = useRouter()

  const handleProjectAction = async (action: { type: string; payload: any }) => {
    if (action.type === "confirm-project" && action.payload?.project) {
      try {
        const projectData = action.payload.project;
        
        // Add detailed logging
        console.log("Project action triggered with data:", {
          type: action.type,
          projectName: projectData.name,
          hasIdea: !!projectData.idea,
          hasTg: !!projectData.tg
        });
        
        // Get the current user ID
        const supabase = createClientComponentClient();
        const { data: session } = await supabase.auth.getSession();
        const userId = session?.session?.user?.id;
        
        if (!userId) {
          toast({
            title: "Authentication required",
            description: "Please sign in to create a project",
            variant: "destructive",
          });
          throw new Error("User not authenticated");
        }
        
        console.log("Creating project for user:", userId);
        
        // Create the project using the server action with the full project data
        const result = await createNewProject(
          userId, 
          projectData.name || "Untitled Project", 
          {
            idea: projectData.idea,
            tg: projectData.tg,
            problems: projectData.problems,
            features: projectData.features,
            tech_stack: projectData.tech_stack,
            usp: projectData.usp,
            selected_tools: projectData.selected_tools || [],
            product_details: projectData.product_details,
            creation_method: "agent_wizard"
          }
        );
        
        console.log("Project creation result:", result);
        
        if (!result.success) {
          throw new Error(result.error || "Failed to create project");
        }
        
        // Add a message to the chat indicating success
        append({
          role: "assistant",
          content: `✅ Project "${projectData.name || "Untitled Project"}" created successfully!`,
        });
        
        // Optionally redirect to the new project
        toast({
          title: "Project created",
          description: "Redirecting to your new project...",
        });
        
        // Wait a moment before redirecting
        setTimeout(() => {
          router.push(`/dashboard/project/${result.project.id}`);
        }, 1500);
        
        return result;
      } catch (error) {
        console.error("Error creating project:", error);
        
        // Log the full error details
        if (error instanceof Error) {
          console.error("Error details:", {
            message: error.message,
            stack: error.stack,
            name: error.name
          });
        }
        
        // Add a message to the chat indicating failure
        append({
          role: "assistant",
          content: "❌ Failed to create project. Please try again.",
        });
        
        toast({
          title: "Project creation failed",
          description: error instanceof Error ? error.message : "Unknown error",
          variant: "destructive",
        });
        
        throw error;
      }
    }
  }

  return (
    // Main container: flex row to accommodate chat and side panel side-by-side
    // Added w-full for clarity, though h-full likely implies it
    <div className="flex flex-row h-full w-full bg-card rounded p-1 ">

      {/* Chat Section - occupies left side with calculated width */}
      <div
        className="flex flex-col h-full"
        style={{
          width: artifact.isVisible
            ? `calc(100vw - ${sidebarWidth}px - 33.33%)`
            : `calc(100vw - ${sidebarWidth}px)`
        }}
      >
        <div className="w-full flex flex-col h-full bg-card">
          {/* Chat header with fixed height of 60px and 4px top padding */}
          <div className="w-full">
            <div
              className="bg-card rounded shadow-sm flex items-center"
              style={{
                height: "56px",
                paddingTop: "4px",
                paddingBottom: "4px",
                paddingLeft: "4px",
                paddingRight: "4px"
              }}
            >
              <ChatHeader
                agents={agents}
                selectedAgent={selectedAgent}
                setSelectedAgent={setSelectedAgent}
                selectedProject={selectedProject}
                selectedDocumentId={selectedDocumentId}
                setSelectedDocumentId={setSelectedDocumentId}
                projectDocuments={projectDocuments}
                isSidebarCollapsed={isSidebarCollapsed}
                sidebarWidth={sidebarWidth}
              />
            </div>
          </div>

          {/* Chat area */}
          <div className="flex flex-col h-full items-center w-full">
            {/* Message list with calculated height and auto-scroll ref */}
            <div
              ref={messageListRef.containerRef}
              className="w-[70%] overflow-y-auto"
              style={{ height: "calc(100vh - 60px - 120px - 8px)" }}
            >
              <MessageList
                messages={messages}
                onAction={handleProjectAction}
                messageOptions={messageOptions}
              />
            </div>

            {/* Message input with fixed container height and bottom padding = 8px */}
            <div
              className="w-[70%] "
              style={{
                height: "60px",
                padding: "0px 16px 16px 8px"
              }}
            >
              <MessageInput
                value={input}
                onChange={handleInputChange}
                onSend={handleSubmit}
                isGenerating={chatLoading}
                allowAttachments={true}
                files={files}
                setFiles={setFiles}
                chatSettings={{
                  models: [
                    { id: "google/gemini-2.5-flash-preview", name: "Gemini 2.5 Flash"},
                    { id: "openai/gpt-4o", name: "GPT-4o" },
                    { id: "openai/gpt-4-turbo", name: "GPT-4 Turbo" },
                    { id: "openai/gpt-3.5-turbo", name: "GPT-3.5 Turbo" },
                    { id: "anthropic/claude-3-5-sonnet", name: "Claude 3.5 Sonnet" },
                    { id: "anthropic/claude-3-opus", name: "Claude 3 Opus" },
                    { id: "anthropic/claude-3-haiku", name: "Claude 3 Haiku" },
                    { id: "google/gemini-1.5-flash", name: "Gemini 1.5 Flash" },
                    { id: "google/gemini-1.5-pro", name: "Gemini 1.5 Pro" },
                    { id: "google/gemini-2.0-flash-exp:free", name: "Gemini 2.0 Flash" },

                  ],
                  projects,
                  documentTypes,
                  selectedModel,
                  selectedProject,
                  selectedDocType,
                  onModelChange: setSelectedModel,
                  onProjectChange: setSelectedProject,
                  onDocTypeChange: setSelectedDocType,
                  isLoading,
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {artifact.isVisible && (
        <div className="h-full p-1" style={{ width: "33.33%" }}>
          <Artifact
            chatId={String(sessionId)}
            input={input as string}
            setInput={() => {}}
            handleSubmit={handleSubmit}
            status={chatLoading ? 'streaming' : 'idle'}
            stop={stop}
            attachments={attachments}
            setAttachments={setAttachments}
            messages={messages}
            setMessages={setMessages}
            append={append}
            reload={() => {}}
            votes={undefined}
            isReadonly={false}
            selectedVisibilityType="private"
          />
        </div>
      )}
    </div>
  )
}
