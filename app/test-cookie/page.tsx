"use client"

import { useEffect, useState } from "react"
import { getSupabaseCookieConfig, getDeploymentInfo } from "@/lib/supabase-client"
import { setSupabaseCookie, hasSupabaseCookie, getCookieDebugInfo } from "@/lib/cookie-utils"

export default function TestCookiePage() {
  const [config, setConfig] = useState<any>(null)
  const [deploymentInfo, setDeploymentInfo] = useState<any>(null)
  const [cookieDebug, setCookieDebug] = useState<any>(null)

  useEffect(() => {
    const loadInfo = () => {
      try {
        const cookieConfig = getSupabaseCookieConfig()
        const deployment = getDeploymentInfo()
        const debug = getCookieDebugInfo()
        
        setConfig(cookieConfig)
        setDeploymentInfo(deployment)
        setCookieDebug(debug)
      } catch (error) {
        console.error("Error loading cookie info:", error)
      }
    }

    loadInfo()
  }, [])

  const testSetCookie = () => {
    const mockSession = {
      access_token: "test_token_123",
      refresh_token: "refresh_token_123",
      expires_at: Date.now() + 3600000,
      user: {
        id: "test_user_id",
        email: "<EMAIL>"
      }
    }

    const result = setSupabaseCookie(mockSession)
    console.log("Test cookie set result:", result)
    
    // Refresh debug info
    const debug = getCookieDebugInfo()
    setCookieDebug(debug)
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Cookie Configuration Test</h1>
      
      <div className="grid gap-6">
        <div className="bg-blue-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Cookie Configuration</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(config, null, 2)}
          </pre>
        </div>

        <div className="bg-green-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Deployment Info</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(deploymentInfo, null, 2)}
          </pre>
        </div>

        <div className="bg-yellow-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Cookie Debug Info</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(cookieDebug, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Test Cookie Setting</h2>
          <button 
            onClick={testSetCookie}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Test Set Cookie
          </button>
          <p className="text-sm mt-2">
            This will set a test cookie and refresh the debug info above.
          </p>
        </div>

        <div className="bg-red-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Current Environment</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify({
              hostname: typeof window !== 'undefined' ? window.location.hostname : 'N/A',
              origin: typeof window !== 'undefined' ? window.location.origin : 'N/A',
              userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'N/A'
            }, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  )
}
