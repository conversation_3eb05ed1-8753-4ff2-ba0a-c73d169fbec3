"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@/components/auth/auth-provider"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, Github, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase-client" // Import supabase client
import { setSupabaseCookie, hasSupabaseCookie } from "@/lib/cookie-utils"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [showDebug, setShowDebug] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const { login, user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const searchParams = useSearchParams()
  const error = searchParams.get("error")

  // If session exists, redirect to dashboard and set cookie if needed
  useEffect(() => {
    const checkAndRedirect = async () => {
      // Don't check session if we're currently loading from a login attempt
      if (isLoading) return;
      
      const { data } = await supabase.auth.getSession();
      const session = data?.session;

      if (session) {
        console.log("Existing session found, redirecting to dashboard");

        if (!hasSupabaseCookie()) {
          console.log("Setting Supabase cookie manually");
          setSupabaseCookie(session);
        }

        router.replace("/dashboard");
      } else {
        console.log("No active session found");
      }
    };

    checkAndRedirect();
  }, [router, isLoading]);

  useEffect(() => {
    // Clear the logged out flag when user visits login page
    localStorage.removeItem("v0_user_logged_out")
    
    // TESTING: Add a quick logout button for development
    if (process.env.NODE_ENV === 'development') {
      window.testLogout = async () => {
        console.log("🧹 Test logout: Clearing all sessions...")
        await supabase.auth.signOut()
        sessionStorage.clear()
        localStorage.clear()
        console.log("✅ All sessions cleared - ready for fresh login test")
        window.location.reload()
      }
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrorMessage("")

    try {
      console.log("Attempting login with:", { email }) // Don't log password
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error("Login error details:", error.message, error.status)
        
        // Handle specific error cases
        if (error.message?.includes("Invalid login credentials")) {
          setErrorMessage("Incorrect email or password. Please try again.")
        } else if (error.status === 400) {
          setErrorMessage("Invalid login attempt. Please check your credentials.")
        } else {
          setErrorMessage(error.message || "Login failed. Please try again.")
        }
        
        throw error
      }
      
      // Get the session data
      const { data: sessionData } = await supabase.auth.getSession()
      console.log("Session established:", sessionData.session ? "Yes" : "No")
      
      if (sessionData.session) {
        // Manually set the cookie if session exists but cookie doesn't
        if (!hasSupabaseCookie()) {
          console.log("Setting cookie manually...")
          setSupabaseCookie(sessionData.session);
          console.log("Cookie manually set:", document.cookie.includes('sb-'))
        }
        
        console.log("Login successful, redirecting to dashboard...")
        // Add a small delay to ensure session is fully established
        setTimeout(() => {
          router.push("/dashboard")
        }, 100)
      } else {
        throw new Error("No session created after successful login")
      }
    } catch (error: any) {
      console.error("Login error:", error)
      
      toast({
        title: "Login failed",
        description: errorMessage || "Please check your credentials and try again",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSSOLogin = async (provider: "google" | "github") => {
    try {
      setIsLoading(true)
      setErrorMessage(null)
      // Removed undefined loginWithSSO call and replaced with appropriate logic below
      if (provider === "google") {
        await handleGoogleSignIn()
      } else if (provider === "github") {
        await handleGitHubSignIn()
      }
      toast({
        title: "SSO Login initiated",
        description: `Redirecting to ${provider} for authentication...`,
      })
      // Note: The actual redirect happens in the OAuth signInWithOAuth function
    } catch (error) {
      console.error(`${provider} login error:`, error)
      setErrorMessage(`Could not login with ${provider}. Please try again.`)
      toast({
        title: "SSO login failed",
        description: `Could not login with ${provider}. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // For testing purposes, let's add a function to pre-fill the test user credentials
  // const fillTestCredentials = () => {
  //   setEmail("<EMAIL>")
  //   setPassword("password123")
  // }

  // Add a debug function to test Supabase connection
  const testConnection = async () => {
    try {
      setDebugInfo({ status: "testing" })

      // Test if environment variables are set
      const envInfo = {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? "✅ Set" : "❌ Missing",
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
          ? "✅ Set"
          : "❌ Missing (first 5 chars: " +
            (process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
              ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 5) + "...)"
              : "N/A)"),
      }

      // Test Supabase connection
      const { data, error } = await supabase.auth.getSession()

      setDebugInfo({
        status: "complete",
        environment: envInfo,
        connection: error ? "❌ Failed" : "✅ Success",
        error: error ? error.message : null,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      setDebugInfo({
        status: "error",
        message: error.message,
        timestamp: new Date().toISOString(),
      })
    }
  }

  const handleGoogleSignIn = async () => {
    try {
      // Force localhost in development
      const isDev = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
      const redirectUrl = isDev 
        ? 'http://localhost:3000/auth/callback'
        : `${window.location.origin}/auth/callback`;
        
      console.log("Google OAuth redirect URL:", redirectUrl);
      console.log("Current origin:", window.location.origin);
      console.log("Is development:", isDev);
      
      // This initiates the OAuth flow with Supabase
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "google", // Specifies Google as the provider
        options: {
          // Tells Supabase where to redirect the user back to YOUR app
          // after successful authentication with Google. This is crucial.
          redirectTo: redirectUrl,
          // Optional: Extra parameters sent to Google
          queryParams: {
            access_type: "offline", // Request refresh token
            prompt: "consent",      // Force consent screen
          },
        },
      })
  
      // Basic error handling if the initiation fails
      if (error) {
        console.error("Google sign-in error:", error)
        toast({
          title: "Sign in failed",
          description: error.message,
          variant: "destructive",
        })
      }
      // Note: A successful initiation doesn't mean login is complete yet.
      // The browser will redirect to Google, then back to your /auth/callback page.
    } catch (error) {
      // Catch unexpected errors during the process
      console.error("Unexpected error during Google sign-in:", error)
      toast({
        title: "Sign in failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    }
  }

  const handleGitHubSignIn = async () => {
    try {
      setIsLoading(true)
      setErrorMessage(null)

      // Force localhost in development
      const isDev = process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
      const redirectUrl = isDev 
        ? 'http://localhost:3000/auth/callback'
        : `${window.location.origin}/auth/callback`;
      console.log("GitHub OAuth redirect URL:", redirectUrl);
      console.log("Current origin:", window.location.origin);
      console.log("Is development:", isDev);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "github",
        options: {
          redirectTo: redirectUrl,
        },
      })

      if (error) {
        console.error("GitHub sign-in error:", error)
        toast({
          title: "Sign in failed",
          description: error.message,
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Unexpected error during GitHub sign-in:", error)
      toast({
        title: "Sign in failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full lg:grid lg:min-h-screen lg:grid-cols-2">
      <div className="hidden bg-muted lg:flex lg:flex-col lg:items-center lg:justify-center p-8 text-center">
        <Link href="/" className="flex flex-col items-center group mb-6">
          <img src="/logo.png" alt="ProVibe Logo" className="h-16 w-16 mb-3 transition-transform duration-300 group-hover:scale-110" />
          <span className="text-4xl font-bold text-primary group-hover:text-primary/90 transition-colors">Provibe</span>
        </Link>
        <p className="text-xl text-muted-foreground mt-2">
          Streamline your workflow and bring your ideas to life.
        </p>
        {/* You can add an illustration or a larger brand image here if desired */}
      </div>
      <div className="flex items-center justify-center py-12 px-4">
        <div className="mx-auto grid w-full max-w-[400px] gap-6">
          <div className="grid gap-2 text-center">
            <Link href="/" className="flex items-center justify-center mb-16 lg:hidden">
              <img src="/logo.png" alt="ProVibe Logo" className="h-6 w-6 mr-2" />
              <span className="text-2xl font-bold text-primary">Provibe</span>
            </Link>
            <h1 className="text-3xl font-bold ">Sign in</h1>

          </div>

          {(error || errorMessage) && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {errorMessage ||
                  (error === "auth-callback-failed"
                    ? "Authentication failed. Please try again."
                    : error === "no-session"
                      ? "No session found. Please sign in again."
                      : "An error occurred. Please try again.")}
              </AlertDescription>
            </Alert>
          )}

          {showDebug && process.env.NODE_ENV === 'development' && (
            <div className="bg-slate-800 p-3 rounded text-xs text-slate-300 space-y-2">
              <div className="flex justify-between">
                <span className="font-semibold">Debug Mode</span>
                <button onClick={testConnection} className="text-emerald-400 hover:text-emerald-300">
                  Test Connection
                </button>
              </div>

              {debugInfo && (
                <pre className="whitespace-pre-wrap overflow-auto max-h-40">{JSON.stringify(debugInfo, null, 2)}</pre>
              )}
            </div>
          )}

          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" className="w-full" onClick={handleGoogleSignIn} disabled={isLoading}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 mr-2">
                <path
                  fill="currentColor"
                  d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"
                />
              </svg>
              Google
            </Button>
            <Button variant="outline" className="w-full" onClick={handleGitHubSignIn} disabled={isLoading}>
              <Github className="h-5 w-5 mr-2" />
              GitHub
            </Button>
          </div>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
            </div>
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="password">Password</Label>
                <Link href="/auth/reset-password" className="text-sm text-primary hover:text-primary/80">
                  Forgot password?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Signing in...
                </>
              ) : (
                "Sign in"
              )}
            </Button>
          </form>
          <div className="mt-4 text-center text-sm">
            Don&apos;t have an account?{" "}
            <Link href="/auth/register" className="underline text-primary hover:text-primary/80">
              Sign up
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
