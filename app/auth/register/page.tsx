"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useAuth } from "@/components/auth/auth-provider"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, Github, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function RegisterPage() {
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const { register, loginWithSSO, user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  // If user is already logged in, redirect to dashboard
  useEffect(() => {
    if (user) {
      router.push("/dashboard")
    }
  }, [user, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrorMessage(null)
    setSuccessMessage(null)

    try {
      // Validate inputs
      if (!name || !email || !password) {
        setErrorMessage("Please fill in all fields")
        setIsLoading(false)
        return
      }

      if (password.length < 6) {
        setErrorMessage("Password must be at least 6 characters long")
        setIsLoading(false)
        return
      }

      console.log("Registering with:", { name, email })

      // Register the user
      await register(email, password, name)

      // Show success message
      setSuccessMessage("Registration successful! Redirecting to dashboard...")

      toast({
        title: "Registration successful",
        description: "Welcome to ProVibe!",
      })

      // Redirect after a short delay to allow the user to see the success message
      setTimeout(() => {
        router.push("/dashboard")
      }, 1500)
    } catch (error: any) {
      console.error("Registration error:", error)

      // Handle specific Supabase errors
      if (error.message?.includes("already registered")) {
        setErrorMessage("This email is already registered. Please sign in instead.")
      } else {
        setErrorMessage(error.message || "There was an error creating your account. Please try again.")
      }

      toast({
        title: "Registration failed",
        description: "There was an error creating your account. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSSOLogin = async (provider: "google" | "github") => {
    try {
      setIsLoading(true)
      setErrorMessage(null)

      await loginWithSSO(provider)

      toast({
        title: "SSO Registration initiated",
        description: `Redirecting to ${provider} for authentication...`,
      })
    } catch (error) {
      console.error(`${provider} login error:`, error)
      setErrorMessage(`Could not register with ${provider}. Please try again.`)

      toast({
        title: "SSO registration failed",
        description: `Could not register with ${provider}. Please try again.`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full lg:grid lg:min-h-screen lg:grid-cols-2">
      <div className="hidden bg-muted lg:flex lg:flex-col lg:items-center lg:justify-center p-8 text-center">
        <Link href="/" className="flex flex-col items-center group mb-6">
          <img src="/logo.png" alt="ProVibe Logo" className="h-16 w-16 mb-3 transition-transform duration-300 group-hover:scale-110" />
          <span className="text-4xl font-bold text-primary group-hover:text-primary/90 transition-colors">Provibe</span>
        </Link>
        <p className="text-xl text-muted-foreground mt-2">
          Join our community and start building amazing things.
        </p>
      </div>
      <div className="flex items-center justify-center py-12 px-4">
        <div className="mx-auto grid w-full max-w-[400px] gap-6">
          <div className="grid gap-2 text-center">
            <Link href="/" className="flex items-center justify-center mb-12 lg:hidden">
              <img src="/logo.png" alt="ProVibe Logo" className="h-6 w-6 mr-2" />
              <span className="text-2xl font-bold text-primary">Provibe</span>
            </Link>
            <h1 className="text-3xl font-bold">Create an account</h1>
          </div>

          {errorMessage && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          {successMessage && (
            <Alert className="bg-emerald-50 text-emerald-800 border-emerald-200">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-2 gap-3">
            <Button variant="outline" className="w-full" onClick={() => handleSSOLogin("google")} disabled={isLoading}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="h-5 w-5 mr-2">
                <path
                  fill="currentColor"
                  d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"
                />
              </svg>
              Google
            </Button>
            <Button variant="outline" className="w-full" onClick={() => handleSSOLogin("github")} disabled={isLoading}>
              <Github className="h-5 w-5 mr-2" />
              GitHub
            </Button>
          </div>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or continue with</span>
            </div>
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                type="text"
                placeholder="John Doe"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                disabled={isLoading}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                disabled={isLoading}
                minLength={6}
              />
              <p className="text-xs text-muted-foreground">Password must be at least 6 characters long</p>
            </div>
            <Button type="submit" className="w-full bg-emerald-500 hover:bg-emerald-600" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Creating account...
                </>
              ) : (
                "Create account"
              )}
            </Button>
          </form>
          <div className="mt-4 text-center text-sm">
            Already have an account?{" "}
            <Link href="/auth/login" className="underline text-primary hover:text-primary/80">
              Sign in
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
