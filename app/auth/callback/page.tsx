"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { supabase } from "@/lib/supabase-client"
import { Loader2 } from "lucide-react"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { setSupabaseCookie, hasSupabaseCookie } from "@/lib/cookie-utils"

const getDashboardUrl = () => {
  const isProd = process.env.NODE_ENV === 'production'
  return isProd ? 'https://provibe.dev/dashboard?from=oauth' : '/dashboard?from=oauth'
}

export default function AuthCallback() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const handleAuthAndRedirect = async () => {
      // First check for error params
      const queryParams = new URLSearchParams(window.location.search)
      const errorParam = queryParams.get("error")
      const errorDescription = queryParams.get("error_description")

      if (errorParam) {
        console.error("OAuth Callback Error:", errorParam, errorDescription)
        setError(errorDescription || errorParam || "An unknown error occurred during authentication.")
        setIsLoading(false)
        router.push(`/auth/login?error=${encodeURIComponent(errorParam)}`)
        return
      }

      try {
        // Clear any existing auth state to prevent conflicts
        console.log("Callback: Clearing any existing sessions...")
        
        // Mark that we're in callback mode FIRST
        sessionStorage.setItem('auth_callback_active', 'true')
        
        // Check for existing session first (faster than refresh)
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        
        if (session) {
          console.log("Callback: Existing session found, redirecting immediately")
          sessionStorage.removeItem('auth_callback_active')
          window.location.href = getDashboardUrl()
          return
        }
        
        if (sessionError) {
          console.error("Session check error:", sessionError)
          // Continue with auth state listener as fallback
        }

        console.log("Callback: No session yet, setting up listener...")
        
        // Set up auth state change listener with aggressive redirect
        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
          console.log("🔥 Auth callback: Auth state changed:", event)
          
          if (event === 'SIGNED_IN' && session) {
            console.log("🚀 Auth callback: SIGNED IN - Redirecting IMMEDIATELY")
            
            // Unsubscribe FIRST to prevent multiple triggers
            subscription.unsubscribe()
            
            // Clear callback flag
            sessionStorage.removeItem('auth_callback_active')
            
            // Force immediate redirect - bypass React Router completely
            window.location.replace(getDashboardUrl())
            return
          }
          
          if (event === 'TOKEN_REFRESHED' && session) {
            console.log("🔄 Auth callback: TOKEN REFRESHED - Redirecting")
            subscription.unsubscribe()
            sessionStorage.removeItem('auth_callback_active')
            window.location.replace(getDashboardUrl())
            return
          }
        })

        // Reduced timeout for faster error handling
        const timeout = setTimeout(() => {
          subscription.unsubscribe()
          setError("Authentication timed out. Please try signing in again.")
          setIsLoading(false)
          router.push('/auth/login?error=timeout')
        }, 3000) // Reduced to 3s for faster feedback

        return () => {
          clearTimeout(timeout)
          subscription.unsubscribe()
          // Clean up callback flag
          sessionStorage.removeItem('auth_callback_active')
        }
      } catch (err) {
        console.error("Error during auth callback:", err)
        setError("Authentication failed. Please try again.")
        setIsLoading(false)
        router.push('/auth/login?error=auth-failed')
      }
    }

    handleAuthAndRedirect()
  }, [router])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
      {isLoading && !error && (
        <>
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <h1 className="text-xl font-semibold mb-2">Completing sign in...</h1>
          <p className="text-muted-foreground">Please wait while we securely process your authentication.</p>
        </>
      )}
      {error && (
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Authentication Failed</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      {/* Optional: Add a message when redirecting after error */}
      {!isLoading && error && (
         <p className="text-muted-foreground mt-4 text-sm">Redirecting back to login...</p>
      )}
    </div>
  )
}
