import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateAIDocument } from '@/lib/ai-service';
import { revalidatePath } from 'next/cache';

// Initialize Supabase Admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function POST(request: Request) {
  try {
    const { documentId, projectId } = await request.json();
    
    if (!documentId || !projectId) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }
    
    // Get document info
    const { data: document, error: docError } = await supabaseAdmin
      .from('project_documents')
      .select('*')
      .eq('id', documentId)
      .single();
      
    if (docError || !document) {
      return NextResponse.json(
        { success: false, error: 'Document not found' },
        { status: 404 }
      );
    }
    
    // Get project data
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();
      
    if (projectError || !project) {
      return NextResponse.json(
        { success: false, error: 'Project not found' },
        { status: 404 }
      );
    }
    
    // Generate new content
    const content = await generateAIDocument(document.type, project);
    
    // Update document
    const { data, error } = await supabaseAdmin
      .from('project_documents')
      .update({ 
        content,
        status: 'completed',
        updated_at: new Date().toISOString()
      })
      .eq('id', documentId)
      .select()
      .single();
      
    if (error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      );
    }
    
    // Revalidate the document page
    revalidatePath(`/dashboard/project/${projectId}/documents/${documentId}`);
    
    return NextResponse.json({ success: true, document: data });
  } catch (error) {
    console.error('Error regenerating document:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
