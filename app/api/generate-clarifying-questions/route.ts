// /app/api/generate-clarifying-questions/route.ts
// ----------------------------------------------------------------------------
// POST  /api/generate-clarifying-questions
// Body: { idea: string; enhancedIdea?: string }
// Returns: { clarifyingQuestions: {question: string; suggestedAnswer: string}[] }
// ----------------------------------------------------------------------------

import { NextResponse, NextRequest } from "next/server";
import { getAIProvider, fetchPromptTemplate } from '@/lib/ai-providers';
import { supabase } from '@/lib/supabase-client';

// Helper function to log credit usage
async function logCreditUsage({
  userId,
  projectId,
  documentId, // New parameter
  action,     // Renamed from serviceName, ensure it's one of the enum values
  creditsUsed // Renamed from creditsConsumed
}: {
  userId: string | undefined;
  projectId: string | null | undefined;
  documentId?: string | null | undefined; // Optional document_id
  action: 'idea_refinement' | 'ai_answer' | 'plan_generation' | 'document_generation' | 'document_regeneration';
  creditsUsed: number;
}) {
  if (!userId) {
    console.warn(`[Credit Log - ${action}] Skipping credit usage logging: No user ID provided.`);
    return;
  }

  try {
    const logData: {
      user_id: string;
      project_id?: string | null;
      document_id?: string | null;
      action: string;
      credits_used: number;
    } = {
      user_id: userId,
      action: action,
      credits_used: creditsUsed,
    };

    if (projectId !== undefined) logData.project_id = projectId;
    if (documentId !== undefined) logData.document_id = documentId;

    console.log(`[Credit Log - ${action}] Attempting to log credit usage - User: ${userId}, Project: ${projectId || 'N/A'}, Document: ${documentId || 'N/A'}, Action: ${action}, Credits: ${creditsUsed}`);
    const { error } = await supabase.from("credit_usage_log").insert(logData);

    if (error) {
      console.error(`[Credit Log - ${action}] Supabase error logging credit usage for user ${userId}:`, error.message, error.details);
    } else {
      console.log(`[Credit Log - ${action}] Credit usage logged for user ${userId}: ${creditsUsed} credits for ${action}.`);
    }
  } catch (err: any) {
    console.error(`[Credit Log - ${action}] Exception during credit usage logging for user ${userId}:`, err.message);
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("[generate-clarifying-questions] ► endpoint hit");
    
    // Parse request
    let body: { idea?: string; enhancedIdea?: string; projectId?: string } = {};
    try {
      body = await request.json();
      console.log("[generate-clarifying-questions] ► Request body:", {
        ideaLength: body.idea?.length || 0,
        enhancedIdeaLength: body.enhancedIdea?.length || 0,
        projectId: body.projectId || 'not provided'
      });
    } catch (err) {
      console.error("[generate-clarifying-questions] ✖ bad JSON", err);
      return NextResponse.json({ error: "Invalid JSON body" }, { status: 400 });
    }

    const { idea, enhancedIdea, projectId } = body;

    // Input validation
    if (!idea) {
      console.error("[generate-clarifying-questions] ✖ Missing idea in request");
      return NextResponse.json({ error: "Idea is required" }, { status: 400 });
    }
    
    // Fetch prompt template from DB
    const taskSlug = "clarifying_qs";
    const userId = request.headers.get("x-user-id") || undefined;
    const CREDITS_PER_GENERATION = 1; // Define credit consumption
    const startTime = Date.now();

    try {
      console.log(`[generate-clarifying-questions] ► Fetching prompt for task '${taskSlug}' from DB...`);
      const promptConfig = await fetchPromptTemplate(taskSlug, userId);
      
      // Build system prompt
      const systemPrompt = promptConfig.template
        .replace("{{RAW_IDEA}}", idea)
        .replace("{{ENHANCED_IDEA}}", enhancedIdea || idea);
      
      console.log(`[generate-clarifying-questions] ► Using Model: ${promptConfig.model}, Temp: ${promptConfig.temperature}, MaxTokens: ${promptConfig.maxTokens}`);
      
      // Get the appropriate AI provider
      const aiProvider = getAIProvider(promptConfig.model);
      
      // Generate content
      const aiResponseContent = await aiProvider.generateContent(
        "Generate clarifying questions with suggested answers in JSON format.",
        {
          model: promptConfig.model,
          temperature: promptConfig.temperature,
          maxTokens: promptConfig.maxTokens,
          systemPrompt: systemPrompt,
          jsonOutput: true
        }
      );
      
      // Parse and return response
      let parsed;
      try {
        // Clean up the response if it's wrapped in markdown code blocks
        let cleanedResponse = aiResponseContent;
        
        // Check if response is wrapped in markdown code blocks
        if (cleanedResponse.includes("```json")) {
          cleanedResponse = cleanedResponse.replace(/```json\s*/, "");
          cleanedResponse = cleanedResponse.replace(/\s*```\s*$/, "");
        }
        
        parsed = JSON.parse(cleanedResponse);
      } catch (err) {
        console.error("[generate-clarifying-questions] ✖ JSON parse fail", err, "RAW:", aiResponseContent);
        return NextResponse.json({ error: "Failed to parse AI response" }, { status: 500 });
      }

      // Before returning the response, ensure it has the expected structure
      if (!parsed.productDetails) {
        console.warn("[generate-clarifying-questions] ⚠ Missing productDetails in AI response, adding empty object");
        parsed.productDetails = {};
      }

      // Log the full parsed response
      console.log("[generate-clarifying-questions] ✓ Full response:", JSON.stringify(parsed, null, 2));
      console.log(`[generate-clarifying-questions] ✓ Generated ${parsed.clarifyingQuestions?.length || 0} questions`);

      // Ensure the response includes clarifyingQuestions array
      if (!parsed.clarifyingQuestions) {
        parsed.clarifyingQuestions = [];
      }

      // Create a complete response object with all expected fields
      const completeResponse = {
        clarifyingQuestions: parsed.clarifyingQuestions,
        productDetails: parsed.productDetails || {},
        isValid: true
      };

      // Supabase insert for logging prompt executions
      const latency = Date.now() - startTime;
      const tokensIn = systemPrompt.length; // Calculate tokens in
      const tokensOut = aiResponseContent.length; // Calculate tokens out
      
      // Only attempt to log if we have a valid UUID for user_id
      if (userId) {
        try {
          console.log(`[generate-clarifying-questions] ► Logging prompt execution with projectId: ${projectId || 'null'}`);
          
          const { error: insertError } = await supabase.from("prompt_executions").insert({
            user_id: userId,
            project_id: projectId || null, // Use projectId from request body
            api_name: taskSlug,
            model: promptConfig.model,
            tokens_in: tokensIn,
            tokens_out: tokensOut,
            latency,
            executed_at: new Date().toISOString(),
            success: true,
            error_message: null,
            prompt_template_id: null,
            document_id: null
          });
          
          if (insertError) {
            console.error("Supabase insert error (prompt_executions):", insertError);
          } else {
            console.log(`[generate-clarifying-questions] ► Prompt execution logged successfully with projectId: ${projectId || 'null'}`);
          }
        } catch (logError) {
          console.error("[generate-clarifying-questions] ✖ Error logging prompt execution:", logError);
        }
      } else {
        console.log("[generate-clarifying-questions] ► Skipping prompt execution logging: No valid user ID");
      }

      // Log credit usage
      await logCreditUsage({
        userId,
        projectId,
        action: 'ai_answer', // Or 'idea_refinement' if more appropriate for clarifying questions
        creditsUsed: CREDITS_PER_GENERATION,
      });

      // Return the complete response
      return NextResponse.json(completeResponse);

    } catch (err) {
      console.error("[generate-clarifying-questions] ✖ Failed to generate questions:", err);
      const message = err instanceof Error ? err.message : "Unknown server error";
      const userErrorMessage = message.includes("API Key missing") || 
                               message.includes("template") || 
                               message.includes("database") || 
                               message.includes("Unsupported model")
        ? message
        : "An unexpected error occurred.";
      return NextResponse.json({ error: userErrorMessage }, { status: 500 });
    }
  } catch (err) {
    console.error("[generate-clarifying-questions] ✖ unexpected error in POST handler", err);
    const message = err instanceof Error ? err.message : "Unknown server error";
    const userErrorMessage = message.includes("API Key missing") || 
                             message.includes("template") || 
                             message.includes("database") || 
                             message.includes("Unsupported model")
      ? message
      : "An unexpected error occurred.";
    return NextResponse.json({ error: userErrorMessage }, { status: 500 });
  }
}
