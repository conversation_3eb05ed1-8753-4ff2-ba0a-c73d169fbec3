import { NextResponse } from 'next/server';
import { getAIProvider } from '@/lib/ai-providers';
import { StreamingTextResponse } from 'ai';
import { prompt as outlinePromptV1 } from '@/lib/prompts/outline/v1';
import { supabase } from '@/lib/supabase-client';

// Helper function to log credit usage
async function logCreditUsage({
  userId,
  projectId,
  documentId, // New parameter
  action,     // Renamed from serviceName, ensure it's one of the enum values
  creditsUsed // Renamed from creditsConsumed
}: {
  userId: string | undefined;
  projectId: string | null | undefined;
  documentId?: string | null | undefined; // Optional document_id
  action: 'idea_refinement' | 'ai_answer' | 'plan_generation' | 'document_generation' | 'document_regeneration';
  creditsUsed: number;
}) {
  if (!userId) {
    console.warn(`[Credit Log - ${action}] Skipping credit usage logging: No user ID provided.`);
    return;
  }

  try {
    const logData: {
      user_id: string;
      project_id?: string | null;
      document_id?: string | null;
      action: string;
      credits_used: number;
    } = {
      user_id: userId,
      action: action,
      credits_used: creditsUsed,
    };

    if (projectId !== undefined) logData.project_id = projectId;
    if (documentId !== undefined) logData.document_id = documentId;

    console.log(`[Credit Log - ${action}] Attempting to log credit usage - User: ${userId}, Project: ${projectId || 'N/A'}, Document: ${documentId || 'N/A'}, Action: ${action}, Credits: ${creditsUsed}`);
    const { error } = await supabase.from("credit_usage_log").insert(logData);

    if (error) {
      console.error(`[Credit Log - ${action}] Supabase error logging credit usage for user ${userId}:`, error.message, error.details);
    } else {
      console.log(`[Credit Log - ${action}] Credit usage logged for user ${userId}: ${creditsUsed} credits for ${action}.`);
    }
  } catch (err: any) {
    console.error(`[Credit Log - ${action}] Exception during credit usage logging for user ${userId}:`, err.message);
  }
}

// Helper function to log prompt execution
async function logPromptExecution({
  userId,
  projectId,
  taskSlug,
  model,
  tokensIn,
  tokensOut,
  latency,
  success = true,
  errorMessage = null
}: {
  userId: string | undefined,
  projectId: string | null,
  taskSlug: string,
  model: string,
  tokensIn: number,
  tokensOut: number,
  latency: number,
  success?: boolean,
  errorMessage?: string | null
}) {
  try {
    console.log(`Logging prompt execution for user: ${userId}, project: ${projectId}, latency: ${latency}ms`);
    
    const { error: insertError } = await supabase.from("prompt_executions").insert({
      user_id: userId,
      project_id: projectId,
      api_name: taskSlug,
      model: model,
      tokens_in: tokensIn,
      tokens_out: tokensOut,
      latency: latency,
      executed_at: new Date().toISOString(),
      success: success,
      error_message: errorMessage
    });
    
    if (insertError) {
      console.error("Supabase insert error (prompt_executions):", insertError);
    } else {
      console.log("Prompt execution logged successfully.");
    }
  } catch (loggingError) {
    console.error("Error during prompt execution logging:", loggingError);
  }
}

export async function POST(request: Request) {
  try {
    // Start timing for latency calculation
    const startTime = Date.now();
    console.log(`Request started at: ${startTime}`);
    
    // Parse the request body
    const body = await request.json();
    const taskSlug = body.taskSlug || 'plan_gen'; // Used for prompt_executions logging
    const projectId = body.projectId; // Extract projectId from the request body
    const { idea, refinedIdea, tools, prompt, model, temperature, maxTokens, currentPlan, productDetails } = body;

    const CREDITS_PER_PLAN_GENERATION = 1; // Define credit consumption
    const userId = request.headers.get("x-user-id") || undefined; // Get userId once
    console.log("Received request with data:", { 
      projectId: projectId || 'not provided', // Log projectId
      idea: typeof idea === 'string' ? idea.substring(0, 50) + '...' : idea,
      refinedIdea: refinedIdea ? 'provided' : 'not provided',
      tools: tools ? `${tools.length} tools provided` : 'not provided',
      customPrompt: prompt ? 'provided' : 'not provided',
      model: model || 'default',
      currentPlan: currentPlan ? 'provided' : 'not provided',
      productDetails: productDetails ? 'provided' : 'not provided'
    });

    // Log product details to debug
    if (productDetails) {
      console.log("Product details:", JSON.stringify(productDetails, null, 2));
    }

    // Use a default empty string if idea is not provided
    const safeIdea = idea || '';
    const safeRefinedIdea = refinedIdea || '';

    // If a custom prompt is provided, use it directly
    if (prompt) {
      console.log("Using provided custom prompt");
      console.log("Prompt template excerpt:", prompt.substring(0, 200) + "...");
      
      // Get the appropriate AI provider
      const providerModel = model || 'gemini-1.5-flash'; // Default to a known streaming model
      const aiProvider = getAIProvider(providerModel);

      // Add instruction to avoid preamble
      const enhancedPrompt = `${prompt}\n\nIMPORTANT: Start your response directly with the project plan content. Do not include any introductory text like "Okay, let's create..." or explanations about missing information. Write as if this is a formal document.`;
      
      console.log("Enhanced prompt excerpt:", enhancedPrompt.substring(0, 200) + "...");

      if (!aiProvider.generateContentStream) {
        return NextResponse.json({ error: `Streaming not supported for model ${providerModel}` }, { status: 400 });
      }

      // --- Log Prompt Execution (Custom Prompt Path) ---
      try {
        console.log(`Attempting to log custom prompt execution for user: ${userId}, project: ${body.projectId}`);
        const latencyToLog = Date.now() - startTime; // Calculate latency up to this point
        const { error: insertError } = await supabase.from("prompt_executions").insert({
          user_id: userId,
          project_id: body.projectId || null,
          api_name: taskSlug, // Schema uses api_name
          model: providerModel,
          tokens_in: enhancedPrompt.length, // Schema uses tokens_in (estimate)
          tokens_out: 0, // Required: Placeholder before stream completes
          latency: latencyToLog, // Required: Latency until call initiation
          success: true, // Required: Assuming success if we start the call
          error_message: null, // Optional
          // executed_at: Default handled by DB
        });
        if (insertError) {
          console.error("Supabase insert error (prompt_executions - custom):", insertError);
        } else {
          console.log("Custom prompt execution logged successfully.");
        }
      } catch (loggingError) {
        console.error("Error during custom prompt execution logging:", loggingError);
      }
      // --- End Logging ---

      console.log("generateContentStream call parameters:", {
        branch: "customPrompt",
        model: providerModel,
        temperature: temperature || 0.7,
        maxTokens: maxTokens || 1500,
        systemPrompt: enhancedPrompt
      });

      try {
        // Generate content stream with the provided prompt
        const stream = await aiProvider.generateContentStream(
          "Generate a detailed project plan based on the provided information. Start directly with the plan content.",
          { 
            model: providerModel, 
            temperature: temperature || 0.7, 
            maxTokens: maxTokens || 1500, 
            systemPrompt: enhancedPrompt,
            skipLogging: true // Always skip logging in the AI provider
          }
        );

        console.log("Successfully received response from AI provider");

        // Log the execution for streaming response
        await logPromptExecution({
          userId,
          projectId,
          taskSlug,
          model: providerModel,
          tokensIn: enhancedPrompt.length,
          tokensOut: 0, // We don't know this for streaming responses
          latency: Date.now() - startTime
        });

        // Log credit usage for streaming response
        await logCreditUsage({
          userId,
          projectId,
          action: 'plan_generation',
          creditsUsed: CREDITS_PER_PLAN_GENERATION,
        });

        // Return the generated plan
        return new StreamingTextResponse(stream);
      } catch (streamError) {
        console.error('Streaming error:', streamError);
        
        // Fallback to non-streaming if streaming fails
        console.log("Falling back to non-streaming due to stream error");
        const plan = await aiProvider.generateContent(
          "Generate a detailed project plan based on the provided information. Start directly with the plan content.",
          {
             model: providerModel,
             temperature: temperature || 0.7,
             maxTokens: maxTokens || 1500,
             systemPrompt: enhancedPrompt,
             skipLogging: true // Always skip logging in the AI provider
          }
        );
        
        // Log the execution after it's complete
        await logPromptExecution({
          userId,
          projectId,
          taskSlug,
          model: providerModel,
          tokensIn: enhancedPrompt.length,
          tokensOut: plan.length,
          latency: Date.now() - startTime
        });

        // Log credit usage for non-streaming fallback
        await logCreditUsage({
          userId,
          projectId,
          action: 'plan_generation',
          creditsUsed: CREDITS_PER_PLAN_GENERATION,
        });
        
        return NextResponse.json({ plan });
      }
    }

    // Format the tools for the prompt
    const toolsText = tools && tools.length > 0 
      ? `Selected tools: ${tools.join(', ')}` 
      : 'No specific tools selected';

    try {
      // Use local prompt template instead of fetching from database
      const promptTemplate = {
        template: outlinePromptV1.aiPrompt,
        temperature: outlinePromptV1.temperature || 0.7,
        maxTokens: outlinePromptV1.maxTokens || 1500,
        model: outlinePromptV1.model || model || 'gemini-2.0-flash'
      };

      // Format product details for better inclusion in the prompt
      let productDetailsFormatted = '';
      if (productDetails) {
        productDetailsFormatted = `
Product Details:
${productDetails.targetAudience ? `- Target Audience: ${productDetails.targetAudience}` : ''}
${productDetails.keyFeatures ? `- Key Features: ${Array.isArray(productDetails.keyFeatures) ? productDetails.keyFeatures.join(', ') : productDetails.keyFeatures}` : ''}
${productDetails.frontendTech ? `- Frontend Technologies: ${Array.isArray(productDetails.frontendTech) ? productDetails.frontendTech.join(', ') : productDetails.frontendTech}` : ''}
${productDetails.backendTech ? `- Backend Technologies: ${Array.isArray(productDetails.backendTech) ? productDetails.backendTech.join(', ') : productDetails.backendTech}` : ''}
${productDetails.usp ? `- Unique Selling Points: ${Array.isArray(productDetails.usp) ? productDetails.usp.join(', ') : productDetails.usp}` : ''}
`;
      }

      // Replace placeholders in the prompt template
      const systemPrompt = promptTemplate.template
        .replace('{{PROJECT_IDEA}}', safeIdea)
        .replace('{{REFINED_IDEA}}', safeRefinedIdea)
        .replace('{{TECHNICAL_STACK}}', toolsText)
        .replace('{{CURRENT_PLAN}}', currentPlan || '')
        .replace('{{EXISTING_PLAN}}', currentPlan || '')
        .replace('{{project_plan}}', currentPlan || '')
        .replace('{{PROJECT_DETAILS}}', productDetailsFormatted || JSON.stringify(productDetails || {}, null, 2));

      // Add instruction to avoid preamble
      const enhancedSystemPrompt = `${systemPrompt}\n\nIMPORTANT: Start your response directly with the project plan content. Do not include any introductory text like "Okay, let's create..." or explanations about missing information. Write as if this is a formal document.`;

      const providerModel = promptTemplate.model || model || 'gemini-1.5-flash'; // Default to a known streaming model
      const aiProvider = getAIProvider(providerModel);

      if (!aiProvider.generateContentStream) {
        // Fallback to non-streaming if the selected model/provider doesn't support it
        console.warn(`Streaming not supported for model ${providerModel}, falling back to non-streaming.`);
        const plan = await aiProvider.generateContent(
          "Generate a detailed project plan based on the provided information. Start directly with the plan content.",
          {
             model: providerModel,
             temperature: promptTemplate.temperature || temperature || 0.7,
             maxTokens: promptTemplate.maxTokens || maxTokens || 1500,
             systemPrompt: enhancedSystemPrompt,
             // Pass context for logging
             userId: userId,
             taskSlug: taskSlug
          }
        );

        // Log credit usage for non-streaming fallback (if streaming not supported)
        await logCreditUsage({
          userId,
          projectId,
          action: 'plan_generation',
          creditsUsed: CREDITS_PER_PLAN_GENERATION,
        });

        return NextResponse.json({ plan });
      }

      // --- Log Prompt Execution ---
      try {
        console.log(`Attempting to log prompt execution for user: ${userId}, project: ${projectId}`);
        const latencyToLog = Date.now() - startTime;
        console.log(`Calculated latency: ${latencyToLog}ms (from ${startTime} to ${Date.now()})`);
        const tokensIn = enhancedSystemPrompt.length; // Placeholder estimate
        const tokensOut = 0; // Placeholder - Can't know this before streaming completes

        const { error: insertError } = await supabase.from("prompt_executions").insert({
          user_id: userId,
          project_id: projectId || null,
          api_name: taskSlug,
          model: providerModel,
          tokens_in: tokensIn,
          tokens_out: tokensOut,
          latency: latencyToLog,
          executed_at: new Date().toISOString(), // Explicitly set execution time
          success: true, // Assuming success if we start streaming
          error_message: null,
          // prompt_template_id: null, // Add if you have template ID
          // document_id: null // Add if relevant
        });
        
        if (insertError) {
          console.error("Supabase insert error (prompt_executions):", insertError);
        } else {
          console.log("Prompt execution logged successfully.");
        }
      } catch (loggingError) {
        console.error("Error during prompt execution logging:", loggingError);
      }
      // --- End Logging ---

      console.log("generateContentStream call parameters:", {
        branch: "template",
        model: providerModel,
        temperature: promptTemplate.temperature || temperature || 0.7,
        maxTokens: promptTemplate.maxTokens || maxTokens || 1500,
        systemPrompt: enhancedSystemPrompt
      });

      try {
        // Generate content stream
        const stream = await aiProvider.generateContentStream(
          "Generate a detailed project plan based on the provided information. Start directly with the plan content.",
          { 
            model: providerModel, 
            temperature: promptTemplate.temperature || temperature || 0.7, 
            maxTokens: promptTemplate.maxTokens || maxTokens || 1500, 
            systemPrompt: enhancedSystemPrompt,
            skipLogging: true // Always skip logging in the AI provider
          }
        );

        // Log the execution for streaming response
        await logPromptExecution({
          userId,
          projectId,
          taskSlug,
          model: providerModel,
          tokensIn: enhancedSystemPrompt.length,
          tokensOut: 0, // We don't know this for streaming responses
          latency: Date.now() - startTime
        });

        // Log credit usage for streaming response
        await logCreditUsage({
          userId,
          projectId,
          action: 'plan_generation',
          creditsUsed: CREDITS_PER_PLAN_GENERATION,
        });

        return new StreamingTextResponse(stream);
      } catch (streamError) {
        console.error('Streaming error:', streamError);
        
        // Fallback to non-streaming if streaming fails
        console.log("Falling back to non-streaming due to stream error");
        const plan = await aiProvider.generateContent(
          "Generate a detailed project plan based on the provided information. Start directly with the plan content.",
          {
            model: providerModel,
            temperature: promptTemplate.temperature || temperature || 0.7,
            maxTokens: promptTemplate.maxTokens || maxTokens || 1500,
            systemPrompt: enhancedSystemPrompt,
            skipLogging: true // Always skip logging in the AI provider
          }
        );
        
        // Log the execution after it's complete
        await logPromptExecution({
          userId,
          projectId,
          taskSlug,
          model: providerModel,
          tokensIn: enhancedSystemPrompt.length,
          tokensOut: plan.length,
          latency: Date.now() - startTime
        });

        // Log credit usage for non-streaming fallback
        await logCreditUsage({
          userId,
          projectId,
          action: 'plan_generation',
          creditsUsed: CREDITS_PER_PLAN_GENERATION,
        });
        
        return NextResponse.json({ plan });
      }

    } catch (templateError) {
      console.error('Template error:', templateError);
      
      // Fallback to a simple prompt if template fetch fails
      console.log("Using fallback prompt");
      const fallbackPrompt = `
        Create a detailed project plan for the following idea:
        
        ${safeIdea}
        
        ${safeRefinedIdea ? `Refined idea: ${safeRefinedIdea}` : ''}
        
        ${toolsText}
        
        ${productDetails ? `Product Details: ${JSON.stringify(productDetails, null, 2)}` : ''}
        
        Include sections for:
        1. Project Overview
        2. Technical Architecture
        3. Implementation Steps
        4. Timeline
        5. Potential Challenges
        
        IMPORTANT: Start your response directly with the project plan content. Do not include any introductory text like "Okay, let's create..." or explanations about missing information. Write as if this is a formal document.
      `;
      
      const providerModel = model || 'gemini-1.5-flash'; // Default to a known streaming model
      const aiProvider = getAIProvider(providerModel);

      // Always use non-streaming for fallback to be safe
      console.log("Using non-streaming for fallback prompt");
      const plan = await aiProvider.generateContent(
        "Generate a detailed project plan based on the provided information. Start directly with the plan content.",
        {
          model: providerModel,
          temperature: temperature || 0.7,
          maxTokens: maxTokens || 1500,
          systemPrompt: fallbackPrompt,
          skipLogging: true // Always skip logging in the AI provider
        }
      );
      
      // Log the execution after it's complete
      await logPromptExecution({
        userId,
        projectId,
        taskSlug,
        model: providerModel,
        tokensIn: fallbackPrompt.length,
        tokensOut: plan.length,
        latency: Date.now() - startTime
      });
      
      // Log credit usage for template error fallback
      await logCreditUsage({
        userId,
        projectId,
        action: 'plan_generation',
        creditsUsed: CREDITS_PER_PLAN_GENERATION,
      });

      return NextResponse.json({ plan });
    }
  } catch (error) {
    console.error('Error generating project plan:', error);
    
    // Provide more detailed error information
    let errorMessage = 'Failed to generate project plan';
    if (error instanceof Error) {
      errorMessage = `${errorMessage}: ${error.message}`;
    }
    
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
