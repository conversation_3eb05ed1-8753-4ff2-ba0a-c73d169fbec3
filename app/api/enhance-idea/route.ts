// /app/api/enhance-idea/route.ts
// v1.2 – [Your Current Date] - Fetch latest prompt by task from DB
// ----------------------------------------------------------------------------
// POST  /api/enhance-idea
// Body: { idea: string; techStackHint?: string }
// Returns: { enhancedIdea: string; clarifyingQuestions: {question: string; suggestedAnswer: string}[] }
// ----------------------------------------------------------------------------

import { NextResponse, NextRequest } from "next/server";
import { getAIProvider } from '@/lib/ai-providers';
import { refineIdeaPromptV1 } from "@/lib/prompts/idea/refine_idea/v1";
import { supabase } from "@/lib/supabase-client";

// Helper function to log credit usage
async function logCreditUsage({
  userId,
  projectId,
  documentId, // New parameter
  action,     // Renamed from serviceName, ensure it's one of the enum values
  creditsUsed // Renamed from creditsConsumed
}: {
  userId: string | undefined;
  projectId: string | null | undefined;
  documentId?: string | null | undefined; // Optional document_id
  action: 'idea_refinement' | 'ai_answer' | 'plan_generation' | 'document_generation' | 'document_regeneration';
  creditsUsed: number;
}) {
  if (!userId) {
    console.warn(`[Credit Log - ${action}] Skipping credit usage logging: No user ID provided.`);
    return;
  }

  try {
    const logData: {
      user_id: string;
      project_id?: string | null;
      document_id?: string | null;
      action: string;
      credits_used: number;
    } = {
      user_id: userId,
      action: action,
      credits_used: creditsUsed,
    };

    if (projectId !== undefined) logData.project_id = projectId;
    if (documentId !== undefined) logData.document_id = documentId;

    console.log(`[Credit Log - ${action}] Attempting to log credit usage - User: ${userId}, Project: ${projectId || 'N/A'}, Document: ${documentId || 'N/A'}, Action: ${action}, Credits: ${creditsUsed}`);
    const { error } = await supabase.from("credit_usage_log").insert(logData);

    if (error) {
      console.error(`[Credit Log - ${action}] Supabase error logging credit usage for user ${userId}:`, error.message, error.details);
    } else {
      console.log(`[Credit Log - ${action}] Credit usage logged for user ${userId}: ${creditsUsed} credits for ${action}.`);
    }
  } catch (err: any) {
    console.error(`[Credit Log - ${action}] Exception during credit usage logging for user ${userId}:`, err.message);
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log("[enhance-idea] ► endpoint hit");
    
    // Parse request
    let body: { idea?: string } = {};
    try {
      body = await request.json();
      console.log("[enhance-idea] ► Request body:", {
        ideaLength: body.idea?.length || 0
      });
    } catch (err) {
      console.error("[enhance-idea] ✖ bad JSON", err);
      return NextResponse.json({ error: "Invalid JSON body" }, { status: 400 });
    }

    const { idea } = body;

    // Input validation
    if (!idea) {
      console.error("[enhance-idea] ✖ Missing idea in request");
      return NextResponse.json({ error: "Idea is required" }, { status: 400 });
    }
    
    // Fetch prompt template from DB
    const taskSlug = "refine_idea";
    const userId = request.headers.get("x-user-id") || undefined;
    const CREDITS_PER_ENHANCEMENT = 1; // Define credit consumption
    
    try {
      console.log(`[enhance-idea] ► Fetching prompt for task '${taskSlug}' from DB...`);
      const promptTemplate = refineIdeaPromptV1;
      const model = "gpt-4o";
      const temperature = 0.85;
      const maxTokens = 10000;
      
      // Build system prompt
      const systemPrompt = promptTemplate.replace("{{RAW_IDEA}}", idea);
      
      // Get the appropriate AI provider
      const aiProvider = getAIProvider(model);
      
      // Generate content
      const aiResponseContent = await aiProvider.generateContent(
        "Generate an enhanced idea and clarifying questions in JSON format.",
        {
          model: model,
          temperature: temperature,
          maxTokens: maxTokens,
          systemPrompt: systemPrompt,
          jsonOutput: true,
          userId,
          taskSlug
        }
      );
      
      // Parse and return response
      let parsed;
      try {
        parsed = JSON.parse(aiResponseContent);
        
        // Ensure we have the expected fields
        if (!parsed.enhancedIdea) {
          console.warn("[enhance-idea] ⚠ Missing enhancedIdea in AI response");
          parsed.enhancedIdea = idea; // Fallback to original idea
        }
        
        // Make sure productName is included in the response
        if (!parsed.productName) {
          console.warn("[enhance-idea] ⚠ Missing productName in AI response");
          parsed.productName = "Untitled Project"; // Provide a default
        }
        
        console.log(`[enhance-idea] ✓ Generated enhanced idea and product name: ${parsed.productName}`);
      } catch (err) {
        console.error("[enhance-idea] ✖ JSON parse fail", err, "RAW:", aiResponseContent);
        return NextResponse.json({ error: "Failed to parse AI response" }, { status: 500 });
      }
      
      const latency = Date.now() - startTime;
      const tokensIn = systemPrompt.length;
      const tokensOut = aiResponseContent?.length || 0;

      try {
        const { error: insertError } = await supabase.from("prompt_executions").insert({
          user_id: userId,
          project_id: null,
          api_name: "enhance-idea",
          model: model,
          tokens_in: tokensIn,
          tokens_out: tokensOut,
          latency,
          executed_at: new Date().toISOString(),
          success: true,
          error_message: null,
          prompt_template_id: "refine_idea",
          document_id: null
        });
        if (insertError) {
          console.error("[enhance-idea] ✖ Logging failed:", insertError);
        } else {
          console.log("[enhance-idea] ► Prompt execution logged");
        }
      } catch (logError) {
        console.error("[enhance-idea] ✖ Logging exception:", logError);
      }

      // Log credit usage
      await logCreditUsage({
        userId,
        projectId: null, // enhance-idea doesn't typically have a projectId at this stage
        action: 'idea_refinement',
        creditsUsed: CREDITS_PER_ENHANCEMENT,
      });
      
      return NextResponse.json(parsed);
      
    } catch (err) {
      console.error("[enhance-idea] ✖ Error processing request:", err);
      const message = err instanceof Error ? err.message : "Unknown server error";
      return NextResponse.json({ error: message }, { status: 500 });
    }
  } catch (err) {
    console.error("[enhance-idea] ✖ unexpected error in POST handler", err);
    const message = err instanceof Error ? err.message : "Unknown server error";
    return NextResponse.json({ error: message }, { status: 500 });
  }
}
