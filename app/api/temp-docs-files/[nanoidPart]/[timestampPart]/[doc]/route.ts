import { createClient } from '@supabase/supabase-js';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { StorageError } from '@supabase/storage-js'; // Recommended for explicit error typing
import path from 'path'; // For path normalization and basename extraction
// Ensure Buffer is available if needed for stream handling, though not strictly necessary for .stream()
// import { Buffer } from 'buffer';

export async function GET(
  req: NextRequest,
  context: { params: { nanoidPart: string; timestampPart: string; doc: string } } // Updated params type
) {
  // Initialize Supabase client inside the handler
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    console.error('SUPABASE_URL environment variable is not set in temp-docs-files API.');
    return NextResponse.json({ error: 'Server configuration error: SUPABASE_URL missing' }, { status: 500 });
  }
  if (!supabaseServiceRoleKey) {
    console.error('SUPABASE_SERVICE_ROLE_KEY environment variable is not set in temp-docs-files API.');
    return NextResponse.json({ error: 'Server configuration error: SUPABASE_SERVICE_ROLE_KEY missing' }, { status: 500 });
  }

  const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

  const { nanoidPart, timestampPart, doc } = context.params; // New params
  const MAX_TOKEN_AGE_MS = 5 * 60 * 1000; // 5 minutes

  // 1. Validate the nanoid part (optional: add format checks if needed)
  if (!nanoidPart) {
     console.warn(`Missing nanoid part in path.`);
     return NextResponse.json({ error: 'Invalid path' }, { status: 400 });
  }

  // 2. Validate and parse the timestamp part
  const requestTimestamp = parseInt(timestampPart, 10);
  if (isNaN(requestTimestamp) || Date.now() - requestTimestamp > MAX_TOKEN_AGE_MS) {
    console.warn(
      `Token expired or invalid timestamp. Timestamp: ${timestampPart}. Parsed timestamp: ${requestTimestamp}, Current time: ${Date.now()}`
    );
    return NextResponse.json({ error: 'Token expired or invalid' }, { status: 403 });
  }

  // 3. Construct the file path in Supabase Storage and Sanitize the document name
  // This must exactly match the path structure used in the uploading route (`temp/NANOID/DOC_TYPE.md`)
  const sanitizedDocName = path.basename(doc);
  if (sanitizedDocName !== doc || !sanitizedDocName.endsWith('.md')) {
      console.warn(`Invalid document name received: ${doc}. Sanitized: ${sanitizedDocName}`);
      return NextResponse.json({ error: 'Invalid document name' }, { status: 400 });
  }
  const storageFilePath = `temp/${nanoidPart}/${sanitizedDocName}`;
  console.log(`[temp-docs-files] Attempting to fetch from storage: ${storageFilePath}`);

  // Attempt to download the file from Supabase storage
  const { data: downloadStream, error: dlErr } = await supabase
    .storage
    .from('temp-docs')
    .download(storageFilePath);

  if (dlErr) { // Check for error first
     const errorMessage = dlErr instanceof StorageError ? dlErr.message : (dlErr as Error)?.message || 'Unknown error';
     const errorStatus = dlErr instanceof StorageError ? dlErr.statusCode : undefined;
     console.error(
       `Error downloading temp file ${storageFilePath}. Status: ${errorStatus || 'N/A'}, Message: ${errorMessage}`,
       dlErr // Log the full error object
     );
     // Return 404 for 'not found' errors specifically
     if (errorMessage.includes('not found')) {
         return NextResponse.json({ error: 'File not found' }, { status: 404 });
     }
     // Return 500 for other download errors
     return NextResponse.json({ error: 'Failed to retrieve file' }, { status: 500 });
  }

  if (!downloadStream) { // Check if data is null/undefined even if no explicit error
       console.error(`[temp-docs-files] Downloaded data is null/undefined for ${storageFilePath}`);
       return NextResponse.json({ error: 'File not found or empty' }, { status: 404 });
  }

  // 4. Convert stream to UTF-8 string and return raw text
  const arrayBuffer = await downloadStream.arrayBuffer();
  const fileText = Buffer.from(arrayBuffer).toString('utf-8');

  return new NextResponse(fileText, {
    headers: {
      'Content-Type': 'text/markdown',
      'Access-Control-Allow-Origin': 'https://v0.dev',
      'Content-Disposition': `inline; filename="${sanitizedDocName}"`,
    },
  });
}