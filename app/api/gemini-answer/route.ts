import { NextResponse } from 'next/server';
import { getAIProvider, fetchPromptTemplate } from '@/lib/ai-providers';

export async function POST(req: Request) {
  try {
    console.log("[gemini-answer] ► endpoint hit");
    
    const { idea, projectDetails, question, suggestedAnswer } = await req.json();
    
    console.log("[gemini-answer] ► Received request:", { 
      ideaLength: idea?.length || 0,
      questionLength: question?.length || 0,
      suggestedAnswerLength: suggestedAnswer?.length || 0,
      hasProjectDetails: !!projectDetails
    });
    
    if (!idea) {
      console.log("[gemini-answer] ✖ Missing idea in request");
      return Response.json({ error: "Missing required field: idea" }, { status: 400 });
    }
    
    try {
      // Fetch prompt template from DB
      const taskSlug = "answer_regen";
      const userId = req.headers.get("x-user-id") || undefined;
      
      console.log(`[gemini-answer] ► Fetching prompt for task '${taskSlug}' from DB...`);
      const promptConfig = await fetchPromptTemplate(taskSlug, userId);
      
      if (!promptConfig) {
        console.error("[gemini-answer] ✖ Failed to fetch prompt template");
        return Response.json({ error: "Failed to fetch prompt template" }, { status: 500 });
      }
      
      if (!promptConfig.template) {
        console.error("[gemini-answer] ✖ Prompt template is empty");
        return Response.json({ error: "Prompt template is empty" }, { status: 500 });
      }
      
      console.log(`[gemini-answer] ► Prompt config:`, {
        model: promptConfig.model,
        temperature: promptConfig.temperature,
        maxTokens: promptConfig.maxTokens,
        templateLength: promptConfig.template.length
      });
      
      // Build system prompt with replacements - fix the placeholder casing
      let systemPrompt = promptConfig.template
        .replace(/{{REFINED_IDEA}}/gi, idea)
        .replace(/{{QUESTION}}/gi, question || "")
        .replace(/{{SUGGESTED_ANSWER}}/gi, suggestedAnswer || "");
      
      // Add context from other fields if available
      if (projectDetails) {
        systemPrompt += " Consider these other details about the project: ";
        for (const [key, value] of Object.entries(projectDetails)) {
          if (key !== "clarifyingQuestions" && typeof value === 'string' && value) {
            systemPrompt += `${key}: ${value}. `;
          }
        }
      }
      
      console.log("[gemini-answer] ► System prompt:", systemPrompt);
      
      // Get the appropriate AI provider
      console.log(`[gemini-answer] ► Getting AI provider for model: ${promptConfig.model}`);
      const aiProvider = getAIProvider(promptConfig.model);
      
      if (!aiProvider) {
        console.error(`[gemini-answer] ✖ No AI provider available for model: ${promptConfig.model}`);
        return Response.json({ error: `Unsupported model: ${promptConfig.model}` }, { status: 500 });
      }
      
      // Generate content
      console.log(`[gemini-answer] ► Generating content with model: ${promptConfig.model}`);
      const answer = await aiProvider.generateContent(
        "Generate an answer based on the provided context.",
        {
          model: promptConfig.model,
          temperature: promptConfig.temperature,
          maxTokens: promptConfig.maxTokens,
          systemPrompt: systemPrompt
        }
      );
      
      if (!answer || answer.trim() === "" || 
          answer.includes("Please provide") || 
          answer.includes("I need more context")) {
        console.error("[gemini-answer] ✖ AI returned a generic or empty response");
        return Response.json({ 
          error: "AI couldn't generate a specific answer. Please try again." 
        }, { status: 500 });
      }
      
      console.log("[gemini-answer] ✓ Generated answer length:", answer?.length || 0);
      return Response.json({ answer });
    } catch (apiError) {
      console.error("[gemini-answer] ✖ AI API error:", apiError);
      const errorMessage = apiError instanceof Error ? apiError.message : "Failed to generate content from AI API";
      console.error("[gemini-answer] ✖ Error message:", errorMessage);
      return Response.json({ error: errorMessage }, { status: 500 });
    }
  } catch (error) {
    console.error("[gemini-answer] ✖ Error in gemini-answer API:", error);
    const errorMessage = error instanceof Error ? error.message : "Failed to generate answer";
    console.error("[gemini-answer] ✖ Error message:", errorMessage);
    return Response.json({ error: errorMessage }, { status: 500 });
  }
}


