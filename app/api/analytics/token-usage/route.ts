import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Initialize Supabase client with service role for admin access
const supabaseAdmin = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: Request) {
  try {
    // Get user ID from header
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 401 });
    }
    
    // Parse URL to get query parameters
    const url = new URL(request.url);
    const projectId = url.searchParams.get('projectId');
    const feature = url.searchParams.get('feature');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    const period = url.searchParams.get('period') || 'day'; // day, week, month
    
    // Build the query
    let query = supabaseAdmin
      .from('token_usage')
      .select('*')
      .eq('user_id', userId);
    
    // Add filters if provided
    if (projectId) {
      query = query.eq('project_id', projectId);
    }
    
    if (feature) {
      query = query.eq('feature', feature);
    }
    
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    
    if (endDate) {
      query = query.lte('created_at', endDate);
    }
    
    // Execute the query
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching token usage:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    // Calculate summary statistics
    const summary = {
      totalInputTokens: data.reduce((sum, item) => sum + item.input_tokens, 0),
      totalOutputTokens: data.reduce((sum, item) => sum + item.output_tokens, 0),
      totalContextTokens: data.reduce((sum, item) => sum + item.context_tokens, 0),
      totalTokens: data.reduce((sum, item) => sum + item.input_tokens + item.output_tokens, 0),
      averageLatency: data.length > 0 ? data.reduce((sum, item) => sum + item.latency_ms, 0) / data.length : 0,
      requestCount: data.length,
      byModel: {} as Record<string, { count: number, inputTokens: number, outputTokens: number }>,
      byFeature: {} as Record<string, { count: number, inputTokens: number, outputTokens: number }>,
    };
    
    // Group by model
    data.forEach(item => {
      if (!summary.byModel[item.model]) {
        summary.byModel[item.model] = { count: 0, inputTokens: 0, outputTokens: 0 };
      }
      summary.byModel[item.model].count++;
      summary.byModel[item.model].inputTokens += item.input_tokens;
      summary.byModel[item.model].outputTokens += item.output_tokens;
    });
    
    // Group by feature
    data.forEach(item => {
      if (!summary.byFeature[item.feature]) {
        summary.byFeature[item.feature] = { count: 0, inputTokens: 0, outputTokens: 0 };
      }
      summary.byFeature[item.feature].count++;
      summary.byFeature[item.feature].inputTokens += item.input_tokens;
      summary.byFeature[item.feature].outputTokens += item.output_tokens;
    });
    
    // Return the data and summary
    return NextResponse.json({
      data,
      summary,
    });
  } catch (error: any) {
    console.error('Unexpected error in token-usage endpoint:', error);
    return NextResponse.json({ error: error.message || 'An unexpected error occurred' }, { status: 500 });
  }
}