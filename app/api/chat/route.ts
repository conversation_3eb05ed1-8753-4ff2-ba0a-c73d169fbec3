// /app/api/chat/route.ts
import { createClient, SupabaseClient } from '@supabase/supabase-js'; // Import SupabaseClient
import { Database } from '@/lib/database.types';
import { streamText, CoreMessage, ToolCall, ToolResult } from 'ai';
import { NextRequest } from 'next/server';
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { agents } from '@/lib/chat/agents'; // Assuming agents are correctly defined elsewhere
import { z } from 'zod';
import { tools as provibeToolsArray, ProvibeTool } from '@/lib/chat/tools/index'; // Updated import

const supabaseAdmin = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Simple token counting function that doesn't require external dependencies
function countTokens(text: string): number {
  if (!text) return 0;
  

  return Math.ceil(text.length / 4);
  

}

// --- Configuration ---
const HISTORY_LIMIT = 10;
export const maxDuration = 30;
export const runtime = 'edge';

// --- System Prompt Construction Logic (moved higher for clarity) ---
// Base parts of the system prompt that are always present.
const baseSystemPromptInitial = `You are Provibe AI, an expert Product Management assistant.

Your role is to help users with their product management tasks, including ideation, planning, and documentation.
Use the provided context for the user's project and documents if available. If no specific project context is provided, answer generally.

When discussing documents:
- If the user has a focused document type selected, prioritize that document in your responses
- You have access to all project documents in the context, so you can reference any of them
- If the user asks about creating a new document, suggest appropriate templates based on their project.
- IMPORTANT (CREATE DOCUMENT): Before using the 'create_document' tool, you MUST first discuss the proposed title and content with the user. Generate a draft of the content, present it to the user for review and confirmation. Only after the user explicitly approves the content should you call the 'create_document' tool with the confirmed title and content.
- IMPORTANT (EDIT/UPDATE DOCUMENT): When editing an existing document using the 'update_document_content' tool:
  - You MUST first retrieve or confirm the 'documentId' and 'projectId'.
  - Fetch or recall the current content of the document.
  - Discuss the desired changes with the user (e.g., changes to title, content, or document type).
  - Generate a draft of the *entire new content* if the content is being changed. Present all proposed changes (new title, new full content, new type) to the user for review and explicit confirmation.
  - Only after the user explicitly approves ALL changes should you call the 'update_document_content' tool.
  - You MUST provide the 'documentId' and 'projectId'. You MUST provide at least one of 'title', 'content', or 'documentType' with the new confirmed value(s).`;

const baseSystemPromptEnding = `\nAlways be helpful, concise, and focused on product management best practices.`;

// --- Transform Provibe Tools for streamText ---
// The execute function signature expected by streamText is (args: any) => Promise<any>
// We will wrap our original execute function which expects (supabaseAdmin, args)
const provibeToolsForStreamText: Record<string, {
  description?: string;
  parameters: z.ZodSchema<any>; // This will be populated by the loop
  execute?: (args: any) => Promise<any>;
}> = provibeToolsArray.reduce((acc, toolDefinition: ProvibeTool) => { // Added ProvibeTool type
  if (toolDefinition.type === 'function') {
    const func = toolDefinition.function;
    let zodSchema: z.ZodSchema<any> = z.object({}); // Default to empty object if no params

    // Manually define Zod schemas based on your JSON schemas in tools.ts
    // This requires careful mapping.
    if (func.name === "echo") {
      zodSchema = z.object({
        text: z.string().describe(func.parameters.properties.text?.description || "Text to echo"),
      });
    } else if (func.name === "create_project") {
      zodSchema = z.object({
        name: z.string().describe(func.parameters.properties.name?.description || "Project name"),
        idea: z.string().describe(func.parameters.properties.idea?.description || "Raw initial concept"),
        refined_idea: z.string().optional().describe(func.parameters.properties.refined_idea?.description || "Enhanced concept"),
        tg: z.string().describe(func.parameters.properties.tg?.description || "Target audience"),
        problems: z.string().describe(func.parameters.properties.problems?.description || "Problems being solved"),
        features: z.array(z.string()).describe(func.parameters.properties.features?.description || "Core functionality features"),
        tech_stack: z.object({}).passthrough().optional().describe(func.parameters.properties.tech_stack?.description || "Technologies"),
        usp: z.array(z.string()).optional().describe(func.parameters.properties.usp?.description || "Unique value propositions"),
        selected_tools: z.array(z.string()).optional().describe(func.parameters.properties.selected_tools?.description || "Development tools"),
        product_details: z.object({}).passthrough().optional().describe(func.parameters.properties.product_details?.description || "Product information"),
        clarifying_questions: z.array(z.object({
          question: z.string(),
          suggestedAnswer: z.string(),
          dimension: z.string(),
          userAnswer: z.string()
        })).optional().describe(func.parameters.properties.clarifying_questions?.description || "Deep-dive insights"),
        creation_method: z.string().optional().describe(func.parameters.properties.creation_method?.description || "Creation method"),
        createMode: z.string().optional().describe(func.parameters.properties.createMode?.description || "Creation mode"),
      });
    } else if (func.name === "edit_project") {
      zodSchema = z.object({
        projectId: z.string().describe(func.parameters.properties.projectId?.description || "Project ID"),
        name: z.string().optional().describe(func.parameters.properties.name?.description || "Project name"),
        description: z.string().optional().describe(func.parameters.properties.description?.description || "Project description"),
        status: z.enum(["draft","active","completed"]).optional().describe(func.parameters.properties.status?.description || "Project status"),
      });
    } else if (func.name === "create_document") {
      zodSchema = z.object({
        projectId: z.string().uuid({ message: "Invalid project ID format. Expected a valid UUID." }).describe(func.parameters.properties.projectId?.description || "Project ID"),
        title: z.string().describe(func.parameters.properties.title?.description || "Document title"),
        content: z.string().describe(func.parameters.properties.content?.description || "Document content"),
        documentType: z.string().optional().describe(func.parameters.properties.documentType?.description || "Document type"),
      });
    } else if (func.name === "update_document_content") {
      zodSchema = z.object({
        documentId: z.string().uuid({ message: "Invalid document ID format. Expected a valid UUID." }).describe(func.parameters.properties.documentId?.description || "Document ID"),
        projectId: z.string().uuid({ message: "Invalid project ID format. Expected a valid UUID." }).describe(func.parameters.properties.projectId?.description || "Project ID"),
        title: z.string().optional().describe(func.parameters.properties.title?.description || "Document title"),
        content: z.string().optional().describe(func.parameters.properties.content?.description || "Document content"),
        documentType: z.string().optional().describe(func.parameters.properties.documentType?.description || "Document type"),
      });

    }
    // Add other tools here with their Zod schemas

    acc[func.name] = {
      description: func.description,
      parameters: zodSchema,
      // The execute function here will be called by streamText.
      // It needs to be an async function that takes `args` and returns a Promise.
      // We will define this fully within the POST handler where `userId` is available.
      // For now, this structure is a placeholder for what streamText expects.
      execute: async (args: any) => {
        throw new Error(`Execute function for ${func.name} should be dynamically assigned in POST handler with userId context.`);
      },
    };
  }
  return acc;
}, {} as Record<string, {
  description?: string;
  parameters: z.ZodSchema<any>;
  execute?: (args: any) => Promise<any>; // This is the signature streamText expects
}>);



// --- Global Helper Functions ---

// Moved extractCreateProjectPayload here for better organization if it wasn't already global
function extractCreateProjectPayload(content: string) {
  const match = content.match(/::create_project::\s*({[\s\S]+})/);
  if (!match) return null;
  try {
    return JSON.parse(match[1]);
  } catch {
    return null;
  }
}

// Corrected global handleCompletion function
async function handleCompletion(
  textToSave: string, // This is the actual content of the message to be saved
  // Parameters from POST scope, passed by onFinish's call
  userId: string,
  projectId: string | null | undefined,
  sessionId: string,
  modelUsed: string, // Renamed to avoid conflict if 'model' is a global
  agentIdUsed: string | null | undefined, // Renamed
  // Token counts for the LLM interaction itself
  llmInputTokens: number,
  llmOutputTokens: number,
  contextTokensFromPost: number,
  // Latency
  startTimeFromPost: number
) {
  try {
    const projectPayload = extractCreateProjectPayload(textToSave); // Use textToSave from params
    let messageType = 'normal'; // Default message type
    let isFinal = false;

    if (projectPayload && agentIdUsed === 'project_creator_agent') {
      messageType = 'agent_output';
      isFinal = true;
      console.log(`[API /api/chat] Detected project creation payload:`, projectPayload);
    }
    // Example: if messageSource was passed and is 'tool_result', you could set a different messageType
    // if (messageSource === 'tool_result') {
    //   messageType = 'tool_confirmation';
    // }

    const latencyMs = Date.now() - startTimeFromPost;

    supabaseAdmin
      .from('token_usage')
      .insert({
        user_id: userId, project_id: projectId || null, session_id: sessionId, model: modelUsed,
        input_tokens: llmInputTokens, output_tokens: llmOutputTokens, // Log actual LLM tokens
        context_tokens: contextTokensFromPost, latency_ms: latencyMs,
        created_at: new Date().toISOString(), feature: 'chat'
      }).then(({ error }) => { if (error) console.error(`[API /api/chat] Error logging token usage:`, error); });

    supabaseAdmin
      .from('chat_messages')
      .insert({
        session_id: sessionId, user_id: userId, role: 'assistant', content: textToSave,
        agent_id: agentIdUsed || null, project_id: projectId || null, type: messageType, is_final: isFinal
      }).then(({ error }) => { if (error) console.error(`[API /api/chat] Supabase assistant message insert error:`, error); });
  } catch (e: any) {
    console.error(`[API /api/chat] Error in global handleCompletion:`, e);
  }
}

async function getProjectContext(projectId: string, userId: string, focusedDocumentType?: string): Promise<string> {
    try {
        // Fetch project data
        const { data: projectData, error: projectError } = await supabaseAdmin
            .from('projects')
            .select('name, idea, refined_idea, product_details, project_plan, status, clarifying_questions')
            .eq('id', projectId)
            .eq('user_id', userId)
            .single();

        // Handle project fetch errors
        if (projectError) {
            if (projectError.code === 'PGRST116') { // Not found
                console.log(`Project context fetch: Project ${projectId} not found for user ${userId}.`);
                return "Project context could not be found for the specified project.";
            } else { // Other DB errors
                console.error("Supabase project fetch error:", projectError);
                return "Error retrieving project details due to a database issue.";
            }
        }

        // Format project context with token budget
        let context = "";
        let tokenBudget = 2000;
        const appendWithBudget = (label: string, value: string) => {
          const tokens = countTokens(value);
          if (tokenBudget - tokens <= 0) return;
          context += `\n### ${label}:\n${value}`;
          tokenBudget -= tokens;
        };
        if (projectData) {
            context = `\n\n## Current Project Context (${projectData.name || 'N/A'}):\n`;
            appendWithBudget("Status", projectData.status || "Not specified");
            appendWithBudget("Original Idea", projectData.idea || "Not specified");
            appendWithBudget("Refined Idea", projectData.refined_idea || "Not specified");
            appendWithBudget("Product Details", projectData.product_details ? JSON.stringify(projectData.product_details, null, 2) : "Not specified");
            appendWithBudget("Clarifying Questions Asked Previously", projectData.clarifying_questions ? JSON.stringify(projectData.clarifying_questions, null, 2) : "None");
            appendWithBudget("Project Plan", projectData.project_plan || "Not specified");
        }

        // Fetch ALL documents for this project
        const { data: allDocuments, error: allDocsError } = await supabaseAdmin
            .from('project_documents')
            .select('id, title, content, type, status')
            .eq('project_id', projectId)
            .order('updated_at', { ascending: false });

        if (allDocsError) {
            console.error("Error fetching project documents:", allDocsError);
            context += "\n### Project Documents:\nError retrieving documents due to a database issue.\n";
        } else if (allDocuments && allDocuments.length > 0) {
            // Group documents by type (keeping only the latest of each type)
            const docTypeMap = new Map();
            allDocuments.forEach(doc => { // Prioritize focused document type if multiple docs of the same type exist
                if (!docTypeMap.has(doc.type) || doc.type === focusedDocumentType) {
                    docTypeMap.set(doc.type, doc);
                } 
            });
            
            // Add document section to context
            context += "\n## Project Documents:\n";
            
            // Process each document
            for (const [type, doc] of docTypeMap.entries()) {
                // Determine if this is the focused document
                const isFocused = focusedDocumentType === type;
                
                // For the focused document, include full content
                if (isFocused) {
                    context += `\n### ${doc.title || doc.type} (FOCUSED DOCUMENT):\n`;
                    context += `${doc.content || 'No content available'}\n`;
                    console.log(`Including full content for focused document type: ${type}`);
                } 
                // For other documents, include a summary/preview (first 500 chars)
                else {
                    context += `\n### ${doc.title || doc.type}:\n`;
                    if (doc.content) {
                        const preview = doc.content.substring(0, 500);
                        context += `${preview}${doc.content.length > 500 ? '...' : ''}\n`;
                    } else {
                        context += 'No content available\n';
                    }
                }
            }
            
            // If a specific document type is focused, add a note
            if (focusedDocumentType && docTypeMap.has(focusedDocumentType)) {
                context += `\n## User's Current Focus:\nThe user is currently focused on the "${focusedDocumentType}" document type. Prioritize this document in your responses when relevant.\n`;
            } else if (focusedDocumentType) {
                 context += `\n## User's Current Focus:\nThe user specified focus on "${focusedDocumentType}", but no document of this type was found for the project.\n`;
            }
        } else {
            context += "\n### Project Documents:\nNo documents have been created for this project yet.\n";
        }

        console.log(`Injected context for project: ${projectId}${focusedDocumentType ? ` with focused document type: ${focusedDocumentType}` : ''}`);
        return context + "\n##\n";
    } catch (e: any) {
        console.error("Unexpected error fetching project details:", e);
        return `Error retrieving project details: ${e.message || 'Unknown error'}`;
    }
}


// --- API Route Handler ---
export async function POST(req: NextRequest) {
  try {
    console.log(`[API /api/chat] POST request received at ${new Date().toISOString()}`);
    console.log(`[API /api/chat] Request URL: ${req.url}`);
    console.log(`[API /api/chat] Request headers:`, Object.fromEntries(req.headers.entries()));
    console.log(`[API /api/chat] User-Agent:`, req.headers.get('user-agent'));
    console.log(`[API /api/chat] Referer:`, req.headers.get('referer'));

    // --- Authentication ---
    const userId = req.headers.get("x-user-id");
    if (!userId) {
      console.error("Error in /api/chat: Missing x-user-id header");
      return new Response(JSON.stringify({ error: 'Unauthorized: Missing user identifier' }), {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
      });
    }

    // --- Parse Request Body ---
    const { messages: incomingMessages, sessionId, model: requestedModel, projectId, documentType, agentId, documentId } = await req.json();

    // Explicitly check for sessionId after parsing
    if (!sessionId || typeof sessionId !== 'string' || sessionId.trim() === '') {
      console.error("[API /api/chat] Critical: Missing or invalid sessionId in request body. Cannot process chat.");
      console.error("[API /api/chat] Request headers:", req.headers);
      console.error("[API /api/chat] Request body:", { messages: incomingMessages, sessionId, model: requestedModel, projectId, documentType, agentId, documentId });
      console.error("[API /api/chat] User-Agent:", req.headers.get('user-agent'));
      console.error("[API /api/chat] Referer:", req.headers.get('referer'));
      return new Response(JSON.stringify({ error: 'Bad Request: Missing or invalid session identifier' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
      });
    }

    // Determine the model to use, defaulting if not provided or invalid
    let modelToUse = requestedModel;
    if (!modelToUse || typeof modelToUse !== 'string' || modelToUse.trim() === '') {
      modelToUse = "google/gemini-2.5-flash-preview";
      console.log(`[API /api/chat] Model not provided or invalid in request, defaulting to: ${modelToUse}`);
    } else {
      console.log(`[API /api/chat] Model provided in request: ${modelToUse}`);
    }
    console.log(`[API /api/chat] Received request for session: ${sessionId}, User: ${userId}, Project: ${projectId || 'none'}, Focused Document: ${documentType || 'none'}, Agent: ${agentId || 'default'}, DocumentId: ${documentId || 'none'}, Model: ${modelToUse}`);

    // Extract the user's query from the last message
    const userQuery = incomingMessages[incomingMessages.length - 1]?.content || '';
    const currentUserMessage = { role: 'user', content: userQuery };

    // Count tokens in the user's message
    const userMessageTokens = countTokens(userQuery);
    console.log(`[API /api/chat] User message token count: ${userMessageTokens}`);

    if (!userQuery) {
      console.error("[API /api/chat] No user query found in incoming messages");
      return new Response(JSON.stringify({ error: 'No user message found in request' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // --- Project Context Handling (Fetch on every request if projectId is provided) ---
    let projectContext = "No specific project context was requested or provided for this turn.";
    let contextTokens = 0;
    let ragContext = "";
    
    if (projectId && typeof projectId === 'string') {
      console.log(`[API /api/chat] Fetching context for project: "${projectId}", focused document type: "${documentType || 'none'}" for session ${sessionId}`);
      projectContext = await getProjectContext(projectId, userId, documentType);
      contextTokens = countTokens(projectContext);
      console.log(`[API /api/chat] Project context token count: ${contextTokens}`);

      // --- RAG Context for Specific Document via Vector Similarity Search ---
      if (projectId && documentId) {
        console.log(`[API /api/chat] Attempting to load RAG context via vector similarity search for documentId: ${documentId}`);
        // Confirm documentId is received and sent in the body
        console.log(`[API /api/chat] (DEBUG) documentId received from client:`, documentId);
        const embeddingResponse = await fetch("https://api.openai.com/v1/embeddings", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${process.env.OPENAI_API_KEY}`,
          },
          body: JSON.stringify({
            input: userQuery,
            model: "text-embedding-ada-002"
          })
        });

        const embeddingJson = await embeddingResponse.json();
        const embedding = embeddingJson?.data?.[0]?.embedding;

        if (!embedding) {
          console.error(`[API /api/chat] Failed to generate embedding for query: ${userQuery}`);
        } else {
          const { data: relevantChunks, error: rpcError } = await supabaseAdmin.rpc('match_document_chunks', {
            query_embedding: embedding,
            match_threshold: 0.6,
            match_count: 5,
            project_id: projectId,
            document_id: documentId
          });

          if (rpcError) {
            console.error("[API /api/chat] match_document_chunks RPC error:", rpcError);
          }

          if (relevantChunks && relevantChunks.length > 0) {
            ragContext = relevantChunks.map((c: any) => c.content).join('\n\n');
            console.log(`[API /api/chat] Loaded ${relevantChunks.length} semantically relevant chunks`);
          } else {
            // fallback to full document
            console.log(`[API /api/chat] No relevant chunks found, falling back to full document content`);
            const { data: fullDoc, error: docError } = await supabaseAdmin
              .from('project_documents')
              .select('content')
              .eq('id', documentId)
              .single();

            if (docError) {
              console.error(`[API /api/chat] Error loading fallback document content:`, docError);
            } else if (fullDoc?.content) {
              console.log(`[API /api/chat] RAG fallback content length: ${fullDoc.content.length}`);
              ragContext = fullDoc.content;
              console.log(`[API /api/chat] Loaded full document content as fallback`);
            }
          }
        }
      }

      // --- Log final ragContext length and tokens ---
      console.log(`[API /api/chat] Final ragContext length (chars): ${ragContext.length}`);
      console.log(`[API /api/chat] Final ragContext tokens: ${countTokens(ragContext)}`);

     
      await supabaseAdmin
        .from('chat_sessions')
        .update({
          project_id: projectId,
          // focused_document_type removed
          // context storage fields removed as well
        })
        .eq('id', sessionId)
        .then(({ error: updateError }) => {
          if (updateError) {
            console.error(`[API /api/chat] Failed to update session ${sessionId} with last used context filters:`, updateError);
          } else {
            console.log(`[API /api/chat] Updated session ${sessionId} with last used context filters (Project: ${projectId}).`);
          }
        });

    } else {
      console.log(`[API /api/chat] No projectId provided by client for this turn in session ${sessionId}.`);
       // Optionally clear the last used context filters in the session if no project is selected now
       supabaseAdmin
        .from('chat_sessions')
        .update({
          project_id: null,
          // focused_document_type removed
        })
        .eq('id', sessionId)
        .then(({ error: updateError }) => {
            if (updateError) {
                console.error(`[API /api/chat] Failed to clear last used context filters for session ${sessionId}:`, updateError);
            }
        });
    }

    // --- Fetch Chat History ---
    // Fetching DB history is still useful for checks like `isMessageAlreadySaved`
    const { data: historyData, error: historyError } = await supabaseAdmin
      .from('chat_messages')
      .select('role, content')
      .eq('session_id', sessionId)
      .eq('user_id', userId)
      .order('created_at', { ascending: true })
      .limit(HISTORY_LIMIT * 2); 

    if (historyError) {
      console.error("Supabase history fetch error:", historyError);
      // Log and continue, don't block response
    }
    const chatHistory: CoreMessage[] = historyData?.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content || ''
    })) || [];
    

    const isMessageAlreadySaved = chatHistory.some(msg => 
      msg.role === 'user' && 
      msg.content === userQuery
    );

    // --- Save Incoming User Message if not already saved ---
    if (!isMessageAlreadySaved) {
      console.log(`[API /api/chat] Saving user message that wasn't found in history: "${userQuery.substring(0, 50)}..."`);
      supabaseAdmin
        .from('chat_messages')
        .insert({
          session_id: sessionId,
          user_id: userId,
          role: 'user',
          content: userQuery,
        })
        .then(({ data, error: insertMsgError }) => {
          if (insertMsgError) {
            console.error(`[API /api/chat] Supabase user message insert error (async) for session ${sessionId}:`, insertMsgError);
          } else {
            console.log(`[API /api/chat] User message saved successfully (async) for session ${sessionId}.`);
          }
        });
    } else {
      console.log(`[API /api/chat] User message already exists in history, skipping save.`);
    }

    // --- Prepare messages for AI ---
    let systemPromptForThisTurn = baseSystemPromptInitial;

    if (projectId && typeof projectId === 'string' && projectContext.includes("## Current Project Context")) {
        // Project context is loaded and seems valid (contains the expected header)
        systemPromptForThisTurn += `

Context for Project ID ${projectId}:
"""
${projectContext}
"""

IMPORTANT TOOLING INSTRUCTIONS FOR THE LOADED PROJECT (ID: ${projectId}):
When a tool requires a 'projectId' for operations related to the project detailed above (Project ID ${projectId}), you MUST use this specific ID: ${projectId}.
Do NOT ask the user for this project ID.
If the user requests actions on a *different* project, you must clarify this with them first before attempting any tool call for a different project.
`;
    } else {
        // No specific project context or projectId is invalid/missing
        systemPromptForThisTurn += `

Context (if provided):
"""
${projectContext} 
"""

Tool Usage Guidelines:
No specific project context with a confirmed Project ID is loaded for this interaction.
If a tool requires a 'projectId', and it's not implicitly clear from the user's request for a general action (like creating a brand new unassociated project), you may need to ask the user for the specific Project ID they intend to use.
`;
    }
    systemPromptForThisTurn += baseSystemPromptEnding;

    let systemPromptWithContext = systemPromptForThisTurn; // This is now our fully constructed system prompt for the turn

    if (ragContext) {
      systemPromptWithContext += `\n\n## Referenced Document Content:\n${ragContext}`;
    }

    // If an agent is specified, use its system prompt instead
    if (agentId && agentId !== 'default' && agents[agentId]) {
      // Replace the project context placeholder in the agent's system prompt
      let agentSystemPrompt = agents[agentId].systemPrompt;
      if (projectId && typeof projectId === 'string' && projectContext.includes("## Current Project Context")) {
        // If agent prompt has a {PROJECT_CONTEXT} placeholder
        agentSystemPrompt = agentSystemPrompt.replace('{PROJECT_CONTEXT}', `\n\nProject Context (ID: ${projectId}):\n${projectContext}\n\nIMPORTANT TOOLING NOTE FOR AGENT: For tools needing a projectId for this context, use ${projectId}.`);
        // If agent prompt needs a more generic way to know the ID, it should be designed to look for it or have it passed.
      } else {
        agentSystemPrompt = agentSystemPrompt.replace('{PROJECT_CONTEXT}', '\n\nNo specific project context loaded for this agent turn.\n');
      }
      
      systemPromptWithContext = agentSystemPrompt; // Agent prompt takes precedence

      // Append RAG context to agent's system prompt if it exists
      // This was outside the agent block before, potentially appending to the default prompt even if an agent was active.
      if (ragContext) { 
        systemPromptWithContext += `\n\n## Referenced Document Content:\n${ragContext}`;
      }
      console.log(`[API /api/chat] Using agent system prompt for: ${agentId}`);
      console.log(`[API /api/chat] ${projectId ? 'Added' : 'Did not add'} project context to agent prompt`);
    }
    
    const systemPromptTokens = countTokens(systemPromptWithContext);
    //console.log(`[API /api/chat] System prompt token count: ${systemPromptTokens}`);
    
    // Use incomingMessages (from client) as the primary source for AI context
    const fullChatHistoryFromClient: CoreMessage[] = incomingMessages.map((msg: any) => ({
        role: msg.role as 'user' | 'assistant' | 'system' | 'tool',
        content: msg.content || '',
        // Add other CoreMessage fields if used, e.g., tool_calls
    }));

    let historyForAI = fullChatHistoryFromClient; // Default to full history from client

    if (agentId === 'project_creator_agent') {
    
      const wizardIntroSystemMessage = "I am your project creation wizard. What would you like to call your project?";
      let startIndex = -1;

      for (let i = 0; i < fullChatHistoryFromClient.length; i++) {
        if (
          fullChatHistoryFromClient[i].role === 'system' &&
          fullChatHistoryFromClient[i].content === wizardIntroSystemMessage
        ) {
          startIndex = i; // Update to the latest found occurrence
        }
      }

      if (startIndex !== -1) {
        // If the intro message is found, take history from that point onwards.
        historyForAI = fullChatHistoryFromClient.slice(startIndex);
        console.log(`[API /api/chat] Project Creator Agent: Sliced history from its introductory system message (index ${startIndex}). New history length: ${historyForAI.length}`);
      } else {
        console.log(`[API /api/chat] Project Creator Agent: Introductory system message not found in client-sent history. Using full client-sent history for the agent. Length: ${historyForAI.length}`);
      }
    }

    const messagesForAI: CoreMessage[] = [
      { role: 'system', content: systemPromptWithContext },
      ...historyForAI // Use the potentially filtered history
    ];

    // Adjust token counting for history based on what's sent to AI
    // Exclude the current user message (last one) from historyTokens count, as it's userMessageTokens
    const historyMessagesForTokenCount = historyForAI.length > 0 
        ? historyForAI.slice(0, -1) 
        : [];
    const historyTokens = historyMessagesForTokenCount.reduce((total, msg) => total + countTokens(String(msg.content || '')), 0);
    //console.log(`[API /api/chat] Chat history (from client, for AI) token count: ${historyTokens}`);

    // Calculate total input tokens
    const totalInputTokens = systemPromptTokens + historyTokens + userMessageTokens;
   

    // Get the start time for latency tracking
    const startTime = Date.now();

    // Initialize OpenRouter model once
    const openrouterModelInstance = openrouter(modelToUse); // Use the correctly defaulted model

    // Dynamically create the tools object for streamText, injecting userId into the execute context
    const toolsForThisRequest: Record<string, { description?: string; parameters: z.ZodSchema<any>; execute: (args: any) => Promise<any>; }> = {};
    for (const toolDef of provibeToolsArray as ProvibeTool[]) { // Cast to ProvibeTool[]
      if (toolDef.type === 'function') {
        const func = toolDef.function;
        toolsForThisRequest[func.name] = {
          description: provibeToolsForStreamText[func.name].description,
          parameters: provibeToolsForStreamText[func.name].parameters, // Use the Zod schema already defined
          execute: async (args: any) => func.execute({ supabaseAdmin, userId }, args), // Pass context object
        };
      }
    }

    // Stream the chat
    const result = streamText({
      model: openrouterModelInstance, // Pass the AI SDK model instance directly
      messages: messagesForAI,
      tools: toolsForThisRequest, // Use the tools prepared for this specific request with userId context
      temperature: 0.7,
      maxTokens: 4000, // Use maxTokens for streamText options
      // Use onFinish for more comprehensive handling, especially with tools
      onFinish: async (finishResult) => {
        const { text, toolCalls, toolResults, usage } = finishResult;
        console.log('[onFinish] LLM interaction finished.', { 
          text: text ? text.substring(0,100)+'...' : null, 
          toolCalls, 
          toolResults, 
          usage 
        });

        // These are the actual tokens consumed/produced by the LLM for this turn
        const actualLlmInputTokens = usage?.promptTokens ?? totalInputTokens; // totalInputTokens is a fallback
        const actualLlmOutputTokens = usage?.completionTokens ?? countTokens(text || ""); // countTokens(text) is a fallback if usage isn't available

        let messageToSave = text;

        if (!messageToSave && toolResults && toolResults.length > 0) {
          // If LLM provided no direct text, try to find a success message from relevant tool results
          for (const toolResult of toolResults) {
            // New: Properly handle create_project for project_creator_agent with structured tool result
            if (toolResult.toolName === 'create_project' && agentId === 'project_creator_agent') {
              try {
                const resultData = JSON.parse(toolResult.result as string);
                if (resultData.success && resultData.project) {
                  messageToSave = `✅ Project "${resultData.project.name}" has been created.\n📁 View it here: /dashboard/project/${resultData.project.id}`;
                  // Insert an extra message with action_type for client-side handling
                  await supabaseAdmin.from('chat_messages').insert({
                    session_id: sessionId,
                    user_id: userId,
                    role: 'assistant',
                    content: messageToSave,
                    agent_id: agentId || null,
                    project_id: resultData.project.id,
                    type: 'system',
                    action_type: 'project_created',
                    action_payload: { projectId: resultData.project.id }
                  });
                  console.log(`[onFinish] Saved structured project creation confirmation for agent: ${messageToSave}`);
                  return; // Don't fall through to regular message saving logic
                }
              } catch (e) {
                console.warn(`[onFinish] Could not parse create_project tool result for project_creator_agent:`, e, "Raw result:", toolResult.result);
              }
            } else if (['create_document', 'update_document_content', 'edit_project'].includes(toolResult.toolName)) {
              // Generic handling for other tools that provide a 'message' field
              try {
                const resultData = JSON.parse(toolResult.result as string);
                if (resultData.success && resultData.message) {
                  messageToSave = resultData.message; // Use the message from the tool's successful execution
                  console.log(`[onFinish] LLM provided no text, using tool result message for ${toolResult.toolName}: "${messageToSave.substring(0, 100)}..."`);
                  break; // Found a suitable message, no need to check other tool results
                }
              } catch (e) {
                console.warn(`[onFinish] Could not parse tool result for ${toolResult.toolName}:`, e, "Raw result:", toolResult.result);
              }
            }
          }
        }

        if (messageToSave) {
          // Call handleCompletion to save the message and log token usage for the LLM interaction
          queueMicrotask(() => handleCompletion(
            messageToSave,   // The content of the message to save
            userId,          // from POST scope
            projectId,       // from POST scope
            sessionId,       // from POST scope
            modelToUse,      // Pass the correctly defaulted model
            agentId,         // from POST scope
            actualLlmInputTokens,  // Actual input tokens for the LLM call
            actualLlmOutputTokens, // Actual output tokens from the LLM call
            contextTokens,   // from POST scope
            startTime        // from POST scope
            // Optionally pass messageSource if handleCompletion needs to adapt its logic:
            // messageSource
          ));
        } else {
          // No textual message from LLM and no suitable message derived from tool results.
          // Still, log token usage if there was an interaction.
          console.log('[onFinish] No text from LLM and no suitable message from tool results to save to chat_messages.');
          if (actualLlmInputTokens > 0 || actualLlmOutputTokens > 0) { // Check if there was any token activity
             const latencyMs = Date.now() - startTime;
             supabaseAdmin
              .from('token_usage')
              .insert({
                user_id: userId, project_id: projectId || null, session_id: sessionId, model: modelToUse,
                input_tokens: actualLlmInputTokens,
                output_tokens: actualLlmOutputTokens,
                context_tokens: contextTokens, latency_ms: latencyMs,
                created_at: new Date().toISOString(), feature: 'chat_no_message_saved' // Indicate a turn where LLM ran but no message was saved
              }).then(({ error }) => { if (error) console.error(`[API /api/chat] Error logging token usage (no_message_saved):`, error); });
           }
        }
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    // --- General Error Handling ---
    console.error("Error in /api/chat:", error);
    return new Response(JSON.stringify({ error: 'An unexpected error occurred processing your request.' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// This is the global OpenRouter client. Ensure it's defined once at the module level.
// If it was defined inside the old broken handleCompletion, that part is now removed.
const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY || '',
  headers: { 'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'https://www.provibe.dev', 'X-Title': 'Provibe' }
});

