import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { headers } from 'next/headers';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Add this function to help with debugging
async function checkMessageHistory(sessionId: string) {
  try {
    // Get all messages for this session
    const { data: allMessages, error } = await supabaseAdmin
      .from('chat_messages')
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true });
      
    if (error) {
      console.error('Error fetching session messages:', error);
      return;
    }
    
    console.log(`Found ${allMessages.length} messages for session ${sessionId}`);
    
    // Look for agent_output messages
    const agentOutputMessages = allMessages.filter(msg => 
      msg.agent_id === 'project_creator_agent' && msg.type === 'agent_output'
    );
    
    console.log(`Found ${agentOutputMessages.length} agent_output messages`);
    
    // Check each agent_output message for the project creation payload
    for (const msg of agentOutputMessages) {
      console.log(`Checking message ${msg.id}:`, msg.content.substring(0, 100) + '...');
      const payload = extractCreateProjectPayload(msg.content);
      if (payload) {
        console.log('Found valid payload in message:', msg.id);
        return;
      }
    }
    
    console.log('No valid project creation payload found in any message');
  } catch (error) {
    console.error('Error in checkMessageHistory:', error);
  }
}

export async function POST(req: Request) {
  try {
    // Get user ID from headers
    const headersList = headers();
    const userId = headersList.get('x-user-id');
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse request body
    const { sessionId, confirmation } = await req.json();
    
    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }
    
    // Validate confirmation
    const isConfirmation = confirmation?.toLowerCase() === 'yes' || confirmation?.toLowerCase() === 'confirm';
    if (!isConfirmation) {
      return NextResponse.json({ error: 'Invalid confirmation' }, { status: 400 });
    }
    
    // Add debug logging
    console.log('Processing project creation for session:', sessionId);
    console.log('User ID:', userId);
    console.log('Confirmation:', confirmation);
    
    // Call this function at the beginning of the POST handler
    await checkMessageHistory(sessionId);
    
    // Get the last agent_output message from this session
    const { data: messages, error: messagesError } = await supabaseAdmin
      .from('chat_messages')
      .select('*')
      .eq('session_id', sessionId)
      .eq('agent_id', 'project_creator_agent')
      .eq('type', 'agent_output')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (messagesError) {
      console.error('Error fetching agent output message:', messagesError);
      return NextResponse.json({ error: 'Failed to fetch agent output' }, { status: 500 });
    }
    
    if (!messages || messages.length === 0) {
      console.error('No agent_output messages found for session:', sessionId);
      return NextResponse.json({ error: 'No project creation payload found' }, { status: 404 });
    }
    
    const pendingProjectMsg = messages[0];
    console.log('Found pending project message:', pendingProjectMsg.id);
    
    // Extract the project creation payload
    function extractCreateProjectPayload(content: string) {
      const match = content.match(/::create_project::\s*({[\s\S]+})/);
      if (!match) return null;
      try {
        return JSON.parse(match[1]);
      } catch (e) {
        console.error('Error parsing project payload:', e);
        return null;
      }
    }
    
    const projectPayload = extractCreateProjectPayload(pendingProjectMsg.content);
    
    if (!projectPayload) {
      console.error('Failed to extract project payload from message content:', pendingProjectMsg.content);
      return NextResponse.json({ error: 'Invalid project creation payload' }, { status: 400 });
    }
    
    console.log('Extracted project payload:', projectPayload);
    
    // Create the project
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .insert([{
        user_id: userId,
        name: projectPayload.name || 'Untitled Project',
        idea: projectPayload.idea || '',
        tg: projectPayload.tg || '',
        problems: projectPayload.problems || '',
        features: Array.isArray(projectPayload.features) ? projectPayload.features : [],
        tech_stack: Array.isArray(projectPayload.tech_stack) ? projectPayload.tech_stack : [],
        usp: Array.isArray(projectPayload.usp) ? projectPayload.usp : [],
        status: projectPayload.status || 'draft',
        creation_method: 'agent_wizard',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();
    
    if (projectError) {
      console.error('Error creating project:', projectError);
      return NextResponse.json({ error: `Failed to create project: ${projectError.message}` }, { status: 500 });
    }
    
    if (!project) {
      console.error('Project creation returned no data');
      return NextResponse.json({ error: 'Project creation failed' }, { status: 500 });
    }
    
    console.log('Project created successfully:', project.id);
    
    // Update the original message with the project ID
    const { error: updateError } = await supabaseAdmin
      .from('chat_messages')
      .update({ 
        project_id: project.id,
        is_final: true
      })
      .eq('id', pendingProjectMsg.id);
      
    if (updateError) {
      console.error('Error updating original message:', updateError);
      // Continue anyway since the project was created
    } else {
      console.log('Updated original message with project ID');
    }
    
    // Add a confirmation message
    const { error: insertError } = await supabaseAdmin
      .from('chat_messages')
      .insert({
        session_id: sessionId,
        user_id: userId,
        role: 'assistant',
        content: `✅ Project "${project.name}" has been created.\n📁 View it here: /project/${project.id}`,
        type: 'system',
        agent_id: 'project_creator_agent',
        project_id: project.id,
        created_at: new Date().toISOString()
      });
      
    if (insertError) {
      console.error('Error inserting confirmation message:', insertError);
      // Continue anyway since the project was created
    } else {
      console.log('Added confirmation message to chat');
    }
    
    return NextResponse.json({ 
      success: true, 
      project: { 
        id: project.id, 
        name: project.name 
      } 
    });
  } catch (error: any) {
    console.error('Unexpected error in create-project endpoint:', error);
    return NextResponse.json({ error: error.message || 'An unexpected error occurred' }, { status: 500 });
  }
}
