import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { OAuth2Client } from "google-auth-library";
import { createClient } from "@supabase/supabase-js";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || "";
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || "";
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || "https://localhost:3000/api/auth/google/callback";
const SCOPES = ['https://www.googleapis.com/auth/drive.file'];

// Initialize Supabase client directly
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

export async function GET(req: NextRequest) {
  console.log("Starting Google auth route");
  
  // Create a direct Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  // Create a Supabase auth client to get the session
  const cookieStore = cookies();
  const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore });
  const { data: { session } } = await supabaseAuth.auth.getSession();
  
  if (!session) {
    console.log("No session found, redirecting to login");
    return NextResponse.redirect(new URL('/auth/login?error=auth_required', req.url));
  }
  
  // Get the user ID from the session
  const userId = session.user.id;
  console.log("User ID from session:", userId);
  
  // Get the redirect URL from the query string if available
  const url = new URL(req.url);
  const redirectUrl = url.searchParams.get('redirect') || '/dashboard';
  
  console.log("Creating OAuth client");
  const oauth2Client = new OAuth2Client(
    GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET,
    REDIRECT_URI
  );
  
  console.log("Generating auth URL with scopes:", SCOPES);
  // Generate the url that will be used for authorization
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: SCOPES,
    prompt: 'consent', // Force to always get refresh_token
    state: `${userId}|${redirectUrl}` // Pass user ID and redirect URL as state
  });
  
  console.log("Redirecting to Google auth URL");
  return NextResponse.redirect(authUrl);
}
