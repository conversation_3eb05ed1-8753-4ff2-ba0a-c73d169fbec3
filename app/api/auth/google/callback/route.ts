import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { OAuth2Client } from "google-auth-library";
import { createClient } from "@supabase/supabase-js";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || "";
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || "";
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || "https://localhost:3000/api/auth/google/callback";

// Initialize Supabase client directly
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

export async function GET(request: NextRequest) {
  console.log("Starting Google auth callback route");
  
  // Create a direct Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  // Get the authorization code from the query string
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  
  // Parse the state parameter to get the user ID and redirect URL
  const stateParam = url.searchParams.get('state') || '';
  let userId = stateParam;
  let redirectUrl = '/dashboard';

  // Check if state contains both userId and redirectUrl
  if (stateParam.includes('|')) {
    const stateParts = stateParam.split('|');
    userId = stateParts[0];
    redirectUrl = stateParts[1] || '/dashboard';
  }
  
  console.log("Received code and state:", code ? "Code received" : "No code", userId);
  
  if (!code) {
    console.error("No authorization code received");
    return NextResponse.redirect(new URL('/dashboard?error=no_code', request.url));
  }
  
  try {
    // Create OAuth client
    const oauth2Client = new OAuth2Client(
      GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET,
      REDIRECT_URI
    );
    
    // Exchange the code for tokens
    const { tokens } = await oauth2Client.getToken(code);
    console.log("Received tokens:", tokens.access_token ? "Access token received" : "No access token");
    
    if (!tokens.access_token) {
      throw new Error("No access token received");
    }
    
    // Store tokens in database
    const { error } = await supabase
      .from('user_oauth_tokens')
      .upsert({
        user_id: userId,
        provider: 'google',
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expiry_date: tokens.expiry_date,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id,provider'
      });
    
    if (error) {
      console.error('Error storing tokens:', error);
      return NextResponse.redirect(new URL('/dashboard?error=token_storage', request.url));
    }
    
    // Redirect back to the original page
    console.log("Redirecting back to:", redirectUrl);
    const finalRedirectUrl = new URL(redirectUrl, request.url);
    
    // Add a success parameter to indicate the connection was successful
    finalRedirectUrl.searchParams.set('google_connected', 'true');
    
    return NextResponse.redirect(finalRedirectUrl);
    
  } catch (error) {
    console.error("Error in Google auth callback:", error);
    return NextResponse.redirect(new URL('/dashboard?error=auth_failed', request.url));
  }
}
