import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";
import { getSupabaseCookieConfig, getDeploymentInfo } from "@/lib/supabase-client";

export async function GET(req: NextRequest) {
  // Get all cookies
  const cookieStore = cookies();
  const allCookies = cookieStore.getAll();

  // Get current cookie configuration
  const cookieConfig = getSupabaseCookieConfig();
  const deploymentInfo = getDeploymentInfo();

  // Look for domain-specific Supabase auth cookie
  const supabaseCookie = cookieStore.get(cookieConfig.name);

  // Also check for old cookie names for debugging
  const oldCookie = cookieStore.get('sb-kaemtxmgccbihlnodwzd-auth-token');
  const genericCookie = cookieStore.get('sb-auth-token');

  // Create a Supabase client
  const supabase = createRouteHandlerClient({ cookies });
  const { data: { session } } = await supabase.auth.getSession();

  // Find any Supabase auth tokens
  const supabaseTokens = allCookies.filter(c =>
    c.name.includes('sb-') && c.name.includes('auth-token')
  );

  return NextResponse.json({
    authenticated: !!session,
    deploymentInfo,
    cookieConfig,
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      NEXT_PUBLIC_APP_DOMAIN: process.env.NEXT_PUBLIC_APP_DOMAIN,
      NEXT_PUBLIC_VERCEL_URL: process.env.NEXT_PUBLIC_VERCEL_URL,
      VERCEL_URL: process.env.VERCEL_URL,
    },
    cookies: {
      count: allCookies.length,
      names: allCookies.map(c => c.name),
      currentSupabaseCookie: {
        name: cookieConfig.name,
        exists: !!supabaseCookie,
        value: supabaseCookie?.value ? '[REDACTED]' : null
      },
      allSupabaseTokens: supabaseTokens.map(c => ({
        name: c.name,
        exists: true,
        value: '[REDACTED]'
      })),
      legacyCookies: {
        oldCookie: !!oldCookie,
        genericCookie: !!genericCookie
      }
    },
    session: session ? {
      userId: session.user.id,
      email: session.user.email,
      expiresAt: session.expires_at
    } : null
  });
}