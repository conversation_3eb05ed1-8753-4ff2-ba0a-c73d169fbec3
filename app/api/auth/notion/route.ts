import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";

// Notion OAuth configuration
const NOTION_CLIENT_ID = process.env.NOTION_CLIENT_ID || "";
const NOTION_REDIRECT_URI = process.env.NOTION_REDIRECT_URI || "https://localhost:3000/api/auth/notion/callback";

export async function GET(req: NextRequest) {
  console.log("Starting Notion auth route");
  
  // Create a Supabase auth client to get the session
  const cookieStore = cookies();
  const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore });
  const { data: { session } } = await supabaseAuth.auth.getSession();
  
  if (!session) {
    console.log("No session found, redirecting to login");
    return NextResponse.redirect(new URL('/auth/login?error=auth_required', req.url));
  }
  
  // Get project ID from query params to use as state
  const url = new URL(req.url);
  const projectId = url.searchParams.get('projectId') || '';
  
  // Build the Notion OAuth URL
  const notionAuthUrl = new URL('https://api.notion.com/v1/oauth/authorize');
  notionAuthUrl.searchParams.append('client_id', NOTION_CLIENT_ID);
  notionAuthUrl.searchParams.append('redirect_uri', NOTION_REDIRECT_URI);
  notionAuthUrl.searchParams.append('response_type', 'code');
  notionAuthUrl.searchParams.append('owner', 'user');
  notionAuthUrl.searchParams.append('scope', 'pages:read pages:write blocks:write');
  notionAuthUrl.searchParams.append('state', projectId);
  
  console.log(`Redirecting to Notion OAuth: ${notionAuthUrl.toString()}`);
  
  // Redirect to Notion OAuth page
  return NextResponse.redirect(notionAuthUrl);
}