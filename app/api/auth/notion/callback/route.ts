import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";
import { Client as NotionClient } from "@notionhq/client";

// Notion OAuth configuration
const NOTION_CLIENT_ID = process.env.NOTION_CLIENT_ID || "";
const NOTION_CLIENT_SECRET = process.env.NOTION_CLIENT_SECRET || "";
const NOTION_REDIRECT_URI = process.env.NOTION_REDIRECT_URI || "https://localhost:3000/api/auth/notion/callback";

// Initialize Supabase client directly
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

export async function GET(req: NextRequest) {
  console.log("Starting Notion OAuth callback");
  
  // Create a direct Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  // Create a Supabase auth client to get the session
  const cookieStore = await cookies();
  const supabaseAuth = createRouteHandlerClient({ cookies: () => cookieStore });
  const { data: { session } } = await supabaseAuth.auth.getSession();
  
  if (!session) {
    console.log("No session found, redirecting to login");
    return NextResponse.redirect(new URL('/auth/login?error=auth_required', req.url));
  }
  
  const userId = session.user.id;
  
  // Get the authorization code and state from the URL
  const url = new URL(req.url);
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state'); // This should be the project ID
  const error = url.searchParams.get('error');
  
  // Handle errors from Notion
  if (error) {
    console.error("Notion OAuth error:", error);
    return NextResponse.redirect(new URL(`/dashboard/project/${state}?error=notion_auth_failed`, req.url));
  }
  
  if (!code) {
    console.error("No code received from Notion");
    return NextResponse.redirect(new URL(`/dashboard/project/${state}?error=no_code`, req.url));
  }
  
  try {
    // Exchange the code for an access token
    const tokenResponse = await fetch('https://api.notion.com/v1/oauth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${Buffer.from(`${NOTION_CLIENT_ID}:${NOTION_CLIENT_SECRET}`).toString('base64')}`
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        code,
        redirect_uri: NOTION_REDIRECT_URI
      })
    });
    
    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json();
      console.error("Token exchange error:", errorData);
      throw new Error(`Token exchange failed: ${errorData.error}`);
    }
    
    const tokenData = await tokenResponse.json();
    
    // Initialize Notion client with the user's access token
    const notion = new NotionClient({
      auth: tokenData.access_token,
    });
    
    // Create a root page for exports in the user's workspace
    console.log("Creating root export page in Notion workspace");

    try {
      // Search for existing "Provibe Exports" page first
      const searchResponse = await notion.search({
        query: "Provibe Exports",
        filter: {
          value: "page",
          property: "object"
        }
      });
      
      let rootPageId;
      
      // Check if we already have a Provibe Exports page
      const existingPage = searchResponse.results.find(page => 
        page.object === 'page' && 
        page.properties?.title?.title?.[0]?.text?.content === 'Provibe Exports'
      );
      
      if (existingPage) {
        console.log("Found existing Provibe Exports page:", existingPage.id);
        rootPageId = existingPage.id;
      } else {
        // Create a new root page at the workspace level
        try {
          const newRootPage = await notion.pages.create({
            parent: { type: "workspace", workspace: true },
            properties: {
              title: {
                title: [
                  { text: { content: "Provibe Exports" } }
                ]
              }
            },
            children: [
              {
                object: "block",
                paragraph: {
                  rich_text: [
                    { text: { content: "This page contains all your exports from Provibe." } }
                  ]
                }
              }
            ]
          });
          rootPageId = newRootPage.id;
          console.log("Created new Provibe Exports page at workspace level:", rootPageId);
        } catch (workspaceError) {
          console.error("Error creating page at workspace level:", workspaceError);
          
          // Fallback: If workspace-level creation fails, find a parent page to use
          console.log("Falling back to creating page under an existing page");
          const fallbackSearchResponse = await notion.search({
            query: "",
            filter: {
              value: "page",
              property: "object"
            },
            page_size: 1
          });
          
          if (fallbackSearchResponse.results.length === 0) {
            throw new Error("Could not find any pages in Notion workspace to use as parent");
          }
          
          const parentPageId = fallbackSearchResponse.results[0].id;
          console.log("Using existing page as parent:", parentPageId);
          
          // Create the Provibe Exports page under this parent
          const newRootPage = await notion.pages.create({
            parent: { 
              type: "page_id",
              page_id: parentPageId
            },
            properties: {
              title: {
                title: [
                  { text: { content: "Provibe Exports" } }
                ]
              }
            },
            children: [
              {
                object: "block",
                paragraph: {
                  rich_text: [
                    { text: { content: "This page contains all your exports from Provibe." } }
                  ]
                }
              }
            ]
          });
          
          rootPageId = newRootPage.id;
          console.log("Created new Provibe Exports page under parent:", rootPageId);
        }
      }
      
      // Store the access token and root page ID in the database
      console.log("Storing Notion token and root page ID:", rootPageId);

      // First, check if there's an existing record to get current metadata
      const { data: existingToken, error: fetchError } = await supabase
        .from('user_oauth_tokens')
        .select('metadata')
        .eq('user_id', userId)
        .eq('provider', 'notion')
        .maybeSingle();

      // Prepare metadata object, preserving existing data if any
      let metadata = {
        workspace_id: tokenData.workspace_id,
        bot_id: tokenData.bot_id,
        owner: tokenData.owner
      };

      // If we have existing metadata, merge with it
      if (!fetchError && existingToken?.metadata) {
        metadata = {
          ...existingToken.metadata,
          ...metadata
        };
        console.log("Merged with existing metadata");
      }

      // Now upsert with the complete metadata AND the dedicated column
      const { error: upsertError } = await supabase
        .from('user_oauth_tokens')
        .upsert({
          user_id: userId,
          provider: 'notion',
          access_token: tokenData.access_token,
          refresh_token: null, // Notion doesn't provide refresh tokens in this flow
          expiry_date: null, // Notion tokens don't expire by default
          scopes: ['pages:read', 'pages:write', 'blocks:write'],
          metadata: metadata,
          notion_parent_page_id: rootPageId, // Store as dedicated column
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,provider'
        });

      if (upsertError) {
        console.error("Error storing Notion token:", upsertError);
        throw new Error(`Failed to store token: ${upsertError.message}`);
      }

      console.log("Successfully stored Notion token with parent page ID:", rootPageId);
      
      console.log("Notion integration successful, redirecting back to project");
      
      // Redirect back to the project page
      return NextResponse.redirect(new URL(`/dashboard/project/${state}?notion=connected`, req.url));
      
    } catch (notionError) {
      console.error("Error creating Notion root page:", notionError);
      // Continue with token storage even if root page creation fails
      
      // Store just the access token in the database
      const { error: upsertError } = await supabase
        .from('user_oauth_tokens')
        .upsert({
          user_id: userId,
          provider: 'notion',
          access_token: tokenData.access_token,
          refresh_token: null,
          expiry_date: null,
          scopes: ['pages:read', 'pages:write', 'blocks:write'],
          metadata: {
            workspace_id: tokenData.workspace_id,
            bot_id: tokenData.bot_id,
            owner: tokenData.owner
          },
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,provider'
        });
      
      if (upsertError) {
        console.error("Error storing Notion token:", upsertError);
        throw new Error(`Failed to store token: ${upsertError.message}`);
      }
      
      // Redirect back with a warning
      return NextResponse.redirect(new URL(`/dashboard/project/${state}?notion=connected&warning=root_page_failed`, req.url));
    }
    
  } catch (error) {
    console.error("Error in Notion OAuth callback:", error);
    return NextResponse.redirect(new URL(`/dashboard/project/${state}?error=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`, req.url));
  }
}
