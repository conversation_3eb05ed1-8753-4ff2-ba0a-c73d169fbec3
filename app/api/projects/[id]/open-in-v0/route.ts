import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { customAlphabet } from 'nanoid';
import { Buffer } from 'buffer';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

const nanoid = customAlphabet('0123456789abcdefghijklmnopqrstuvwxyz', 10);
const V0_BASE_URL = "https://v0.dev"; // Base URL for v0 chat
const APP_BASE_URL = process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"; // Your app's base URL

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { params } = context;
    const { id: projectId } = await params;
    
    // Get user ID using multiple authentication methods
    let userId;
    
    // Method 1: Try to get session from cookies
    const cookieStore = await cookies();
    const supabaseUserClient = createRouteHandlerClient({ 
      cookies: () => cookieStore 
    });
    
    const { data: { session }, error: sessionError } = await supabaseUserClient.auth.getSession();
    
    if (session && session.user) {
      userId = session.user.id;
      console.log("User ID from session:", userId);
    } else {
      console.log("No session found or session error:", sessionError);
      
      // Method 2: Try to get user ID from custom header
      const userIdHeader = req.headers.get('x-user-id');
      if (userIdHeader) {
        userId = userIdHeader;
        console.log("User ID from header:", userId);
      } else {
        console.log("No user ID header found");
      }
      
      // Method 3: Debug cookie information
      const allCookies = cookieStore.getAll();
      console.log("All cookies:", allCookies.map(c => c.name));
      
      // Try to find any Supabase auth cookies
      const supabaseCookies = allCookies.filter(c => c.name.includes('auth-token'));
      if (supabaseCookies.length > 0) {
        console.log("Found Supabase auth cookies:", supabaseCookies.map(c => c.name));
      }
    }
    
    if (!userId) {
      console.error('No user ID found through any authentication method');
      return NextResponse.json({ 
        error: 'Unauthorized', 
        needsAuth: true,
        loginUrl: "/auth/login"
      }, { status: 401 });
    }

    // Initialize Supabase Admin Client with checks
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl) {
      console.error('SUPABASE_URL environment variable is not set in open-in-v0 API.');
      return NextResponse.json({ error: 'Server configuration error: SUPABASE_URL missing' }, { status: 500 });
    }
    if (!supabaseServiceRoleKey) {
      console.error('SUPABASE_SERVICE_ROLE_KEY environment variable is not set in open-in-v0 API.');
      return NextResponse.json({ error: 'Server configuration error: SUPABASE_SERVICE_ROLE_KEY missing' }, { status: 500 });
    }
    const supabaseAdminClient = createClient(
      supabaseUrl,
      supabaseServiceRoleKey
    );

    // Fetch project with refined_idea
    const { data: project, error: projErr } = await supabaseAdminClient
      .from('projects')
      .select('id, user_id, name, idea, refined_idea') // Add name, idea, and refined_idea
      .eq('id', projectId)
      .eq('user_id', userId) // Check ownership
      .single();

    if (projErr || !project) {
      console.error('Error loading project:', projErr);
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Use the project idea in the prompt for v0
    const projectIdea = project.refined_idea || project.idea || '';
    const projectName = project.name || 'My Project';

    // Create a more specific prompt that includes the project idea
    const promptText = `Generate a UI for "${projectName}": ${projectIdea}. Use the attached documents as guidance on the user experience and design.`;
    const encodedPrompt = encodeURIComponent(promptText); // Ensure prompt is encoded

    // Generate the unique parts for the URL and storage path
    const nanoidPart = nanoid();
    const timestampPart = Date.now().toString();
    console.log(`[open-in-v0] Generated nanoid part: ${nanoidPart}, timestamp part: ${timestampPart}`);

    // Fetch all docs for this project
    const { data: docs, error: fetchErr } = await supabaseAdminClient
      .from('project_documents')
      .select('type, content')
      .eq('project_id', projectId);

    if (fetchErr || !docs) {
      console.error('Error fetching project docs:', fetchErr);
      return NextResponse.json({ error: 'Failed to fetch project docs' }, { status: 500 });
    }

    // Upload docs to a temporary location and generate URLs pointing to your custom proxy route
    const proxiedFileUrls = await Promise.all(docs.map(async ({ type, content }) => {
      const docType = type;
      // Define the path for storing the file in Supabase Storage.
      // Structure: `temp/NANOID/DOC_TYPE.md`
      const storageFilePath = `temp/${nanoidPart}/${docType}.md`;
      console.log(`[open-in-v0] Attempting to upload to Supabase Storage: ${storageFilePath}`);

      // Ensure content is a string before uploading
      const contentString = typeof content === 'string' ? content : JSON.stringify(content);

      const { data: uploadData, error: uploadError } = await supabaseAdminClient.storage
        .from('temp-docs') // Assuming 'temp-docs' is your designated temporary bucket
        .upload(storageFilePath, Buffer.from(contentString), {
           contentType: 'text/markdown',
           upsert: true // Use upsert in case a doc type is regenerated quickly
         });

      if (uploadError) {
        console.error(`[open-in-v0] Error uploading ${storageFilePath} to Supabase Storage:`, uploadError);
        throw new Error(`Failed to upload ${docType}.md: ${uploadError.message}`);
      }
      // Supabase upload data might return 'path' or 'fullPath', use the one that matches your bucket structure expectation
      const uploadedPathInBucket = uploadData?.path || storageFilePath; // Fallback to storageFilePath if data.path is null/undefined
      console.log(`[open-in-v0] Successfully uploaded to bucket path: ${uploadedPathInBucket}`);

      // Generate a URL pointing to your custom proxy endpoint
      // New Structure: `${APP_BASE_URL}/api/temp-docs-files/NANOID/TIMESTAMP/DOC_NAME.md`
      const proxyUrlPath = `${nanoidPart}/${timestampPart}/${docType}.md`;
      const proxiedUrl = `${APP_BASE_URL}/api/temp-docs-files/${proxyUrlPath}`;

      console.log(`[open-in-v0] Generated proxied URL: ${proxiedUrl}`);
      return proxiedUrl; // Return the URL to your custom proxy
    }));

    const filesList = proxiedFileUrls.join(',');
    const v0Link = `${V0_BASE_URL}/chat?q=${encodedPrompt}&files=${filesList}`;

    console.log(`[open-in-v0] Final v0 link generated: ${v0Link}`);
    return NextResponse.json({ url: v0Link });
  } catch (error) {
    console.error('Unhandled error in API route:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
