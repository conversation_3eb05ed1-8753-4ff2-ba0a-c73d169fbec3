// This file should be moved to the correct path: app/api/projects/[id]/documents/route.ts
import { NextResponse, type NextRequest } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateAIDocument } from '@/lib/ai-service';

// Initialize Supabase Admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { params } = context;
    const { id } = await params;
    console.log("Document generation request for project:", id);
    
    const body = await req.json();
    const { selectedDocuments, projectPlan, refinedIdea, userId, requestId } = body;
    
    console.log(`🔍 API ROUTE: Document generation request received`);
    console.log(`🔍 Request ID: ${requestId || 'NOT_PROVIDED'}`);
    console.log(`🔍 Project ID: ${id}`);
    console.log(`🔍 Selected documents: ${selectedDocuments}`);
    console.log(`🔍 User ID: ${userId}`);
    console.log(`🔍 Timestamp: ${new Date().toISOString()}`);
    
    if (!selectedDocuments || !Array.isArray(selectedDocuments) || selectedDocuments.length === 0) {
      return NextResponse.json({ error: 'No documents selected' }, { status: 400 });
    }
    
    if (!userId) {
      console.error('No userId provided in request body');
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    // Validate that the user exists using admin client
    const { data: user, error: userError } = await supabaseAdmin
      .from('profiles')
      .select('id, credits_remaining')
      .eq('id', userId)
      .single();
      
    if (userError || !user) {
      console.error("User not found:", userError);
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    const { data: project, error: projectError } = await supabaseAdmin
      .from('projects')
      .select('*') // Select all necessary fields for generateAIDocument
      .eq('id', id)
      .eq('user_id', userId)
      .single();
      
    if (projectError || !project) {
      console.error("Project not found:", projectError);
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }
    
    // Use the user data we already fetched
    const userData = user;

    // Fetch all selected document types' details from the database
    const { data: dbDocumentTypes, error: dbDocTypesError } = await supabaseAdmin
      .from('document_types')
      .select('id, title, cost') // Fetch only id, title, and cost
      .in('id', selectedDocuments)
      .eq('active', true); // Optionally, ensure only active templates are used

    if (dbDocTypesError) {
      console.error("Error fetching document types from DB:", dbDocTypesError);
      return NextResponse.json({ error: 'Failed to fetch document type information.' }, { status: 500 });
    }

    // Validate that all selectedDocuments were found and are active
    if (!dbDocumentTypes || dbDocumentTypes.length !== selectedDocuments.length) {
      const foundIds = dbDocumentTypes ? dbDocumentTypes.map(dt => dt.id) : [];
      const missingIds = selectedDocuments.filter(id => !foundIds.includes(id));
      console.warn(`Some document types not found or inactive in DB: ${missingIds.join(', ')}`);
      return NextResponse.json({ error: `Following document types are not available: ${missingIds.join(', ')}` }, { status: 400 });
    }

    // Recalculate totalCost based on actual data from DB
    const actualTotalCost = dbDocumentTypes.reduce((sum, doc) => sum + (doc.cost || 0), 0);

    if (userData.credits_remaining < actualTotalCost) {
      return NextResponse.json({ error: 'Insufficient credits for the selected documents.' }, { status: 402 });
    }

    const generatedDocsData = []; // To potentially return some info about what was generated

    for (const documentTypeInfo of dbDocumentTypes) { // Iterate over the fetched document types
      // documentTypeInfo now contains id, title, cost, ai_prompt, system_prompt from the DB
      // We will now pass only the ID to generateAIDocument, assuming it fetches its own prompts
      // Call AI service
      const aiResponse = await generateAIDocument(
        documentTypeInfo.id, // Pass only the document type ID (string)
        project.id, // Pass the project ID (string)
        userId, // Pass the user ID from request body
        projectPlan || "" // Pass project plan
      );

      let markdownToSave: string;

      // Extract the actual markdown string
      if (typeof aiResponse === 'string') {
        // If generateAIDocument itself returns a raw string (less likely given the metadata)
        markdownToSave = aiResponse;
      } else if (aiResponse && typeof aiResponse.content === 'string') {
        // If generateAIDocument returns an object { content: "markdown...", ... }
        markdownToSave = aiResponse.content;
      } else {
        console.error(`Unexpected response structure from generateAIDocument for ${documentTypeInfo.id}:`, aiResponse);
        // Handle error: skip this document or return an error response
        // For now, let's assume it might be an error and we save a placeholder or skip
        markdownToSave = `Error: Could not retrieve valid markdown content for ${documentTypeInfo.title}.`;
        // Or: throw new Error(`Invalid content structure for ${docType}`);
      }

      const { error: docError } = await supabaseAdmin
        .from('project_documents')
        .insert({
          project_id: id,
          title: documentTypeInfo.title,
          type: documentTypeInfo.id, // Use the ID from documentTypeInfo
          content: markdownToSave, // Save only the extracted markdown string
          status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          user_id: userId,
        });

      if (docError) {
        console.error(`Error creating document record for ${documentTypeInfo.id}:`, docError);
        // Decide if one failure should stop all, or collect errors
        throw new Error(`Failed to create document record for ${documentTypeInfo.id}: ${docError.message}`);
      }
      generatedDocsData.push({ type: documentTypeInfo.id, title: documentTypeInfo.title, content: markdownToSave });
    }
    
    const { error: creditError } = await supabaseAdmin
      .from('profiles')
      .update({ credits_remaining: userData.credits_remaining - actualTotalCost })
      .eq('id', userData.id);
      
    if (creditError) {
      console.error("Error updating user credits:", creditError);
      // Potentially throw an error or handle this case, as credit deduction failed
    } else {
      // Log credit usage
      const { error: logError } = await supabaseAdmin
        .from('credit_usage_log')
        .insert({
          user_id: userData.id,
          project_id: id, // Project ID from params
          document_id: null, // Set to null as this log covers batch generation
          action: 'document_generation',
          credits_used: actualTotalCost,
          // created_at will be set by default in the DB
        });
      if (logError) {
        console.error("Error logging credit usage:", logError);
      }
    }
    
    // Return the actual content of the first generated document if only one, or a success message
    if (selectedDocuments.length === 1 && generatedDocsData.length === 1) {
      return NextResponse.json({
        success: true,
        message: `Generated ${generatedDocsData[0].title} successfully.`,
        content: generatedDocsData[0].content, // Return the raw markdown content
        documentType: generatedDocsData[0].type,
      });
    }

    return NextResponse.json({ 
      success: true, 
      message: `Generated ${generatedDocsData.length} documents successfully.`,
      documentCount: generatedDocsData.length
    });
  } catch (error) {
    console.error('Error in document generation API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
