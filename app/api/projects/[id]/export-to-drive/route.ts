export const runtime = 'nodejs';
import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { google } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { createClient } from "@supabase/supabase-js";
import { marked } from 'marked';
import { Readable } from 'stream';


// Google OAuth configuration
const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID || "";
const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET || "";
const REDIRECT_URI = process.env.GOOGLE_REDIRECT_URI || "https://localhost:3000/api/auth/google/callback";

// Initialize Supabase client directly
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

// ↓ we no longer destructure params directly; we'll await them
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const { params } = context;
  const { id: projectId } = await params;

  console.log("Starting export-to-drive API route");
  
  // Create a direct Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  // Properly await cookies(), then wrap into a function for Supabase's helper
  const cookieStore = await cookies();
  const supabaseAuth = createRouteHandlerClient({
    // Supabase helper will invoke cookies(), so we give it a function
    cookies: () => cookieStore
  });
  
  const { data: { session } } = await supabaseAuth.auth.getSession();
  
  // Get user ID from Supabase session
  let userId;
  if (session && session.user) {
    userId = session.user.id;
    console.log("User ID from Supabase session:", userId);
  } else {
    // We need to handle cookies properly in Next.js 14
    console.log("No session found, checking for alternative auth methods");
    
    // Try to get the user ID from the request headers if your frontend sets it
    const authHeader = req.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      try {
        // ...your JWT verify logic if you need it
        const token = authHeader.substring(7);
        // Verify the token and extract user ID
        // For now, we'll just continue with the session approach
      } catch (error) {
        console.error("Error parsing auth header:", error);
      }
    }
  }

  // Example of manually reading a cookie via the awaited store:
  // const authCookie = cookieStore.get('sb-kaemtxmgccbihlnodwzd-auth-token');
  // console.log('Manual cookie read:', authCookie?.value);
  
  if (!userId) {
    console.log("No auth found, returning 401");
    return NextResponse.json({ 
      error: "You must be logged in to use this feature", 
      needsAuth: true,
      loginUrl: "/auth/login"
    }, { status: 401 });
  }
  
  try {
    const { documentIds, projectName } = await req.json();
    
    console.log(`API: Exporting ${documentIds?.length || 0} documents for project ${projectId} to Google Drive for user ${userId}`);
    
    if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
      return NextResponse.json({ error: "No document IDs provided" }, { status: 400 });
    }
    
    // Fetch the actual document details from Supabase
    const { data: projectDocuments, error: docError } = await supabase
      .from('project_documents')
      .select('id, title, content, type')
      .in('id', documentIds)
      .eq('project_id', projectId);

    if (docError || !projectDocuments || projectDocuments.length === 0) {
      console.error("Error fetching documents from Supabase:", docError);
      return NextResponse.json({ 
        error: `Failed to retrieve documents for export. ${docError ? docError.message : 'No documents found or permission denied.'}` 
      }, { status: docError ? 500 : 404 });
    }
    
    console.log("Checking for Google OAuth tokens");
    // Get the user's Google OAuth tokens from your database
    const { data: userTokens, error: tokenError } = await supabase
      .from("user_oauth_tokens")
      .select("access_token, refresh_token, expiry_date")
      .eq("user_id", userId)
      .eq("provider", "google")
      .maybeSingle();
      
    console.log("Token check result:", userTokens ? "Tokens found" : "No tokens", tokenError ? `Error: ${tokenError.message}` : "No error");
    
    if (tokenError && tokenError.code !== 'PGRST116') {
      console.error("Token fetch error:", tokenError);
      return NextResponse.json({ 
        error: "Failed to retrieve Google authorization", 
        details: tokenError.message,
        needsAuth: true
      }, { status: 401 });
    }
    
    if (!userTokens) {
      console.log("No Google tokens found, returning 401 with needsAuth");
      return NextResponse.json({ 
        error: "Google Drive access not authorized. Please connect your Google account first.",
        needsAuth: true,
        authUrl: "/api/auth/google",
        message: "You need to connect your Google Drive account to export documents."
      }, { status: 401 });
    }
    
    // Create OAuth client
    const oauth2Client = new OAuth2Client(
      GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET,
      REDIRECT_URI
    );
    
    // Set credentials
    oauth2Client.setCredentials({
      access_token: userTokens.access_token,
      refresh_token: userTokens.refresh_token,
      expiry_date: userTokens.expiry_date
    });

    // Refresh token if needed
    try {
      await oauth2Client.getAccessToken(); // will refresh token if expired
    } catch (refreshError) {
      console.error("OAuth token refresh failed:", refreshError);
      return NextResponse.json({
        error: "Google Drive token refresh failed. Please reconnect.",
        needsReauth: true
      }, { status: 401 });
    }
    
    // Create Drive client
    const drive = google.drive({ version: 'v3', auth: oauth2Client });
    
    // Find or create a common folder for all exports
    async function findOrCreateCommonFolder(drive, userId) {
      const commonFolderName = "Provibe Exports"; // Name of the common folder
      
      try {
        // Search for existing folder with exact name match
        const searchResponse = await drive.files.list({
          q: `name='${commonFolderName}' and mimeType='application/vnd.google-apps.folder' and trashed=false and 'root' in parents`,
          fields: 'files(id, name, webViewLink)',
          spaces: 'drive',
        });
        
        // If folder exists, use it
        if (searchResponse.data.files && searchResponse.data.files.length > 0) {
          console.log("Found existing common folder:", searchResponse.data.files[0].name);
          return {
            id: searchResponse.data.files[0].id,
            url: searchResponse.data.files[0].webViewLink
          };
        }
        
        // Otherwise create a new common folder
        console.log("Creating new common folder");
        const folderResponse = await drive.files.create({
          requestBody: {
            name: commonFolderName,
            mimeType: 'application/vnd.google-apps.folder',
            description: 'Common folder for all AI-generated project documents',
          },
          fields: 'id, webViewLink',
        });
        
        return {
          id: folderResponse.data.id,
          url: folderResponse.data.webViewLink
        };
      } catch (error) {
        console.error("Error finding/creating common folder:", error);
        throw error;
      }
    }

    // First find or create the common folder
    const commonFolder = await findOrCreateCommonFolder(drive, userId);
    console.log("Using common folder with ID:", commonFolder.id);
    
    // Create a unique subfolder name with timestamp to avoid duplicates
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const exportFolderName = `${projectName} - ${timestamp}`;
    
    // Then create a subfolder for this specific export
    const folderResponse = await drive.files.create({
      requestBody: {
        name: exportFolderName,
        mimeType: 'application/vnd.google-apps.folder',
        parents: [commonFolder.id], // This places the folder inside the common folder
        description: `Documents exported from ${projectName} project on ${new Date().toLocaleString()}`,
      },
      fields: 'id, webViewLink',
    });
    
    const folderId = folderResponse.data.id;
    const folderUrl = folderResponse.data.webViewLink;
    
    if (!folderId) {
      return NextResponse.json({ error: "Failed to create Google Drive folder" }, { status: 500 });
    }
    
    // Upload each document as a native Google Doc
    await Promise.all(projectDocuments.map(async (doc) => {
      const title     = doc.title || doc.type;
      const mdContent = typeof doc.content === 'string'
                        ? doc.content
                        : JSON.stringify(doc.content);

      // 1. Convert Markdown → HTML
      const html = marked.parse(mdContent);

      // 2. Wrap the HTML in a Readable stream
      const htmlStream = Readable.from([html]);

      // 3. Upload, asking Drive to convert it into a Google Doc
      const res = await drive.files.create({
        requestBody: {
          name:     title,
          mimeType: 'application/vnd.google-apps.document',
          parents:  [folderId],
        },
        media: {
          mimeType: 'text/html',
          body:     htmlStream,
        },
        fields: 'id, webViewLink',
      });

      console.log(`Uploaded "${title}" as Google Doc:`, res.data.webViewLink);
    }));

    // Return success with both folder URLs
    return NextResponse.json({
      success: true,
      message: `Successfully exported ${projectDocuments.length} documents to Google Drive`,
      folderUrl: folderUrl,
      commonFolderUrl: commonFolder.url,
      documentCount: projectDocuments.length
    });
    
  } catch (error) {
    console.error("Error exporting to Google Drive:", error);
    
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to export to Google Drive",
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
