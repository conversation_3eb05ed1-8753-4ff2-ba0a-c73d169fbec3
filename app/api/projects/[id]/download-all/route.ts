import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import J<PERSON><PERSON><PERSON> from "jszip";

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

export async function POST(req: Request, { params }: { params: { id: string } }) {
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    const { documentIds, format = "markdown" } = await req.json();
    const projectId = params.id;
    
    console.log(`API: Downloading ${documentIds.length} documents for project ${projectId}`);
    
    if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
      return NextResponse.json({ error: "No document IDs provided" }, { status: 400 });
    }
    
    // First, get the project name
    const { data: project, error: projectError } = await supabase
      .from("projects")
      .select("name")
      .eq("id", projectId)
      .single();
      
    if (projectError) {
      console.error("Error fetching project:", projectError);
      // Continue with projectId as fallback
    }
    
    // Use project name or fallback to project ID
    const projectName = project?.name || `project-${projectId}`;
    // Sanitize the project name for use in a filename
    const safeProjectName = projectName.replace(/[^a-z0-9]/gi, '-').toLowerCase();
    
    // Fetch all requested documents from the database
    const { data: documents, error } = await supabase
      .from("project_documents")
      .select("*")
      .eq("project_id", projectId)
      .in("type", documentIds);
      
    if (error) {
      console.error("Database error:", error);
      throw error;
    }
    
    if (!documents || documents.length === 0) {
      return NextResponse.json({ error: "No documents found" }, { status: 404 });
    }
    
    console.log(`API: Found ${documents.length} documents`);
    
    // Create a zip file
    const zip = new JSZip();
    
    // Add each document to the zip
    documents.forEach(doc => {
      const docType = doc.type;
      const title = doc.title || docType;
      const filename = `${title.replace(/\s+/g, "-").toLowerCase()}.md`;
      
      // Use the raw markdown content from the database
      let content = "";
      
      if (typeof doc.content === "string") {
        content = doc.content;
      } else if (doc.content && typeof doc.content === "object") {
        // If content is stored as an object (like Lexical state), try to extract text
        content = "# " + (doc.title || "Document") + "\n\n";
        content += "Content could not be extracted in markdown format.";
      }
      
      // Add file to zip
      zip.file(filename, content);
    });
    
    // Generate zip file as an array buffer
    const zipContent = await zip.generateAsync({
      type: "arraybuffer",
      compression: "DEFLATE",
      compressionOptions: {
        level: 9
      }
    });
    
    // Convert to a Buffer for the response
    const buffer = Buffer.from(zipContent);
    
    // Return the zip file as a downloadable attachment with project name
    return new Response(buffer, {
      status: 200,
      headers: {
        "Content-Type": "application/zip",
        "Content-Disposition": `attachment; filename="${safeProjectName}-documents.zip"`,
        "Content-Length": buffer.length.toString()
      }
    });
  } catch (error) {
    console.error("Error creating zip file:", error);
    return NextResponse.json({ error: "Failed to create zip file" }, { status: 500 });
  }
}
