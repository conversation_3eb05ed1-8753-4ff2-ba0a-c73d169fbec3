
import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";
import { Client } from "@notionhq/client";

// Initialize Supabase client directly
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  // Fix 1: Properly await params in Next.js 14
  const { params } = context;
  const { id: projectId } = await params;

  console.log("Starting export-to-notion API route for project:", projectId);
  
  // Create a direct Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  // Fix 2: Properly await cookies() in Next.js 14
  const cookieStore = await cookies();
  const supabaseAuth = createRouteHandlerClient({
    // Supabase helper will invoke cookies(), so we give it a function
    cookies: () => cookieStore
  });
  
  const { data: { session } } = await supabaseAuth.auth.getSession();
  
  // Get user ID from Supabase session
  let userId;
  if (session && session.user) {
    userId = session.user.id;
    console.log("User ID from Supabase session:", userId);
  } else {
    console.log("No session found, returning 401");
    return NextResponse.json({ 
      error: "You must be logged in to use this feature", 
      needsAuth: true,
      loginUrl: "/auth/login"
    }, { status: 401 });
  }
  
  try {
    const { documentIds, projectName } = await req.json();
    
    console.log(`API: Exporting ${documentIds?.length || 0} documents for project ${projectId} to Notion for user ${userId}`);
    
    if (!documentIds || !Array.isArray(documentIds) || documentIds.length === 0) {
      return NextResponse.json({ error: "No document IDs provided" }, { status: 400 });
    }
    
    // Fetch the actual document details from Supabase
    const { data: projectDocuments, error: docError } = await supabase
      .from('project_documents')
      .select('id, title, content, type')
      .in('id', documentIds)
      .eq('project_id', projectId);

    if (docError || !projectDocuments || projectDocuments.length === 0) {
      console.error("Error fetching documents from Supabase:", docError);
      return NextResponse.json({ 
        error: `Failed to retrieve documents for export. ${docError ? docError.message : 'No documents found or permission denied.'}` 
      }, { status: docError ? 500 : 404 });
    }
    
    console.log("Checking for Notion OAuth tokens");
    // Get the user's Notion OAuth tokens from your database
    const { data: userTokens, error: tokenError } = await supabase
      .from("user_oauth_tokens")
      .select("access_token, metadata, notion_parent_page_id")
      .eq("user_id", userId)
      .eq("provider", "notion")
      .maybeSingle();
      
    console.log("Token check result:", userTokens ? "Tokens found" : "No tokens", tokenError ? `Error: ${tokenError.message}` : "No error");
    
    if (tokenError) {
      console.error("Token fetch error:", tokenError);
      return NextResponse.json({ 
        error: "Failed to retrieve Notion authorization", 
        details: tokenError.message,
        needsAuth: true
      }, { status: 401 });
    }
    
    if (!userTokens || !userTokens.access_token) {
      console.log("No Notion tokens found, returning 401 with needsAuth");
      return NextResponse.json({ 
        error: "Notion access not authorized. Please connect your Notion account first.",
        needsAuth: true,
        authUrl: "/api/auth/notion",
        message: "You need to connect your Notion account to export documents."
      }, { status: 401 });
    }
    
    // Initialize Notion client with the user's access token
    const notion = new Client({
      auth: userTokens.access_token,
    });

    // Get the parent page ID from the dedicated column
    const notionParentPageId = userTokens.notion_parent_page_id;
    console.log("Retrieved parent page ID from tokens:", notionParentPageId);

    let rootPageId;
    let provibeFolderId;

    if (notionParentPageId) {
      // Use the stored parent page ID as the Provibe Exports folder
      provibeFolderId = notionParentPageId;
      console.log("Using existing Provibe Exports folder ID:", provibeFolderId);
    } else {
      // No parent page ID stored, need to find root and create Provibe folder
      console.log("No Provibe folder ID stored, searching for root page");
      const searchResponse = await notion.search({
        query: "",
        filter: {
          value: "page",
          property: "object"
        },
        page_size: 1
      });
      
      if (searchResponse.results.length === 0) {
        return NextResponse.json({ 
          error: "Could not find any pages in your Notion workspace. Please reconnect your Notion account.",
          needsAuth: true,
          authUrl: "/api/auth/notion",
        }, { status: 400 });
      }
      
      // Use the first page as root
      rootPageId = searchResponse.results[0].id;
      console.log("Found existing page to use as root:", rootPageId);
      
      // Now search for an existing "Provibe Exports" page
      const provibeFolderSearch = await notion.search({
        query: "Provibe Exports",
        filter: {
          value: "page",
          property: "object"
        }
      });
      
      // Check if we already have a Provibe Exports page
      const existingProvibePage = provibeFolderSearch.results.find(page => 
        page.object === 'page' && 
        page.properties?.title?.title?.[0]?.text?.content === 'Provibe Exports'
      );
      
      if (existingProvibePage) {
        console.log("Found existing Provibe Exports page:", existingProvibePage.id);
        provibeFolderId = existingProvibePage.id;
      } else {
        // Create a new Provibe Exports page
        console.log("Creating new Provibe Exports page");
        const provibePage = await notion.pages.create({
          parent: {
            type: "page_id",
            page_id: rootPageId
          },
          properties: {
            title: {
              title: [
                {
                  text: {
                    content: "Provibe Exports",
                  },
                },
              ],
            },
          },
          children: [
            {
              object: "block",
              paragraph: {
                rich_text: [
                  {
                    text: {
                      content: "This page contains all your exports from Provibe.",
                    },
                  },
                ],
              },
            },
          ],
        });
        
        provibeFolderId = provibePage.id;
        console.log("Created new Provibe Exports page:", provibeFolderId);
      }
      
      // Store the Provibe Exports page ID for future use
      await supabase
        .from('user_oauth_tokens')
        .update({
          notion_parent_page_id: provibeFolderId
        })
        .eq('user_id', userId)
        .eq('provider', 'notion');
    }

    // Check if this project already has a dedicated Notion page
    const { data: projectData, error: projectError } = await supabase
      .from('projects')
      .select('notion_projectpage_id')
      .eq('id', projectId)
      .single();

    let parentPage;
    if (!projectError && projectData?.notion_projectpage_id) {
      // Use existing project page
      console.log("Using existing project Notion page:", projectData.notion_projectpage_id);
      try {
        // Verify the page still exists
        parentPage = await notion.pages.retrieve({ page_id: projectData.notion_projectpage_id });
        console.log("Retrieved existing project page:", parentPage.id);
        
        // Check if the page is archived
        if (parentPage.archived) {
          console.log("Existing page is archived, will create a new one");
          
          // Try to unarchive the page first
          try {
            await notion.pages.update({
              page_id: parentPage.id,
              archived: false
            });
            console.log("Successfully unarchived the page");
          } catch (unarchiveError) {
            console.log("Failed to unarchive page, will create a new one:", unarchiveError.message);
            parentPage = null;
          }
        }
      } catch (error) {
        console.log("Existing page not found or inaccessible, will create new one:", error.message);
        parentPage = null;
      }
    }

    if (!parentPage) {
      // Format current timestamp as YYYYMMDD-HHMMSS
      const now = new Date();
      const timestamp = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;
      
      // Create a unique parent page for this specific project export with consistent timestamp format
      const parentPageTitle = `${projectName || 'Project'} ${timestamp}`;

      // Create a parent page for this export under the Provibe Exports page
      try {
        parentPage = await notion.pages.create({
          parent: {
            type: "page_id",
            page_id: provibeFolderId
          },
          properties: {
            title: {
              title: [
                {
                  text: {
                    content: parentPageTitle,
                  },
                },
              ],
            },
          },
          children: [
            {
              object: "block",
              paragraph: {
                rich_text: [
                  {
                    text: {
                      content: `Documents exported from ${projectName} project on ${new Date().toLocaleString()}`,
                    },
                  },
                ],
              },
            },
          ],
        });
        
        // Save the new page ID in the projects table
        const { error: updateError } = await supabase
          .from('projects')
          .update({ notion_projectpage_id: parentPage.id })
          .eq('id', projectId);
          
        if (updateError) {
          console.error("Error saving project page ID:", updateError);
        } else {
          console.log(`Saved Notion project page ID ${parentPage.id} for project ${projectId}`);
        }
        
        console.log(`Created project export page in Notion: ${parentPage.id}`);
      } catch (error) {
        console.error("Error creating project export page:", error);
        return NextResponse.json({ 
          error: "Failed to create project export page in Notion", 
          details: error.message
        }, { status: 500 });
      }
    }

    // Function to convert markdown to Notion blocks
    const markdownToNotionBlocks = (markdown) => {
      // This is a simplified conversion - for a full conversion you'd need a proper markdown parser
      const lines = markdown.split('\n');
      const blocks = [];
      
      for (const line of lines) {
        // Skip empty lines
        if (!line.trim()) continue;
        
        // Check for headings
        if (line.startsWith('# ')) {
          blocks.push({
            object: "block",
            heading_1: {
              rich_text: [{ text: { content: line.substring(2) } }],
            },
          });
        } else if (line.startsWith('## ')) {
          blocks.push({
            object: "block",
            heading_2: {
              rich_text: [{ text: { content: line.substring(3) } }],
            },
          });
        } else if (line.startsWith('### ')) {
          blocks.push({
            object: "block",
            heading_3: {
              rich_text: [{ text: { content: line.substring(4) } }],
            },
          });
        } else {
          // Default to paragraph
          blocks.push({
            object: "block",
            paragraph: {
              rich_text: [{ text: { content: line } }],
            },
          });
        }
      }
      
      return blocks;
    };

    // Function to chunk blocks into groups of 100 or fewer
    const chunkBlocks = (blocks, maxSize = 100) => {
      const chunks = [];
      for (let i = 0; i < blocks.length; i += maxSize) {
        chunks.push(blocks.slice(i, i + maxSize));
      }
      return chunks;
    };

    // Create a page for each document with better error handling and retries
    const documentPages = await Promise.allSettled(
      projectDocuments.map(async (doc) => {
        // Format current timestamp as YYYYMMDD-HHMMSS
        const now = new Date();
        const timestamp = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;
        
        // Append timestamp to document title
        const baseTitle = doc.title || doc.type || 'Untitled Document';
        const title = `${baseTitle} ${timestamp}`;
        const content = typeof doc.content === 'string' ? doc.content : JSON.stringify(doc.content);
        
        console.log(`Starting export of document: ${title} (${doc.id})`);
        
        try {
          // Convert markdown to blocks
          const blocks = markdownToNotionBlocks(content);
          console.log(`Converted ${blocks.length} blocks for document: ${title}`);
          
          // Create a page for this document with initial content (first 100 blocks max)
          let page;
          const maxRetries = 3;
          let retryCount = 0;
          
          while (retryCount < maxRetries) {
            try {
              page = await notion.pages.create({
                parent: {
                  type: "page_id",
                  page_id: parentPage.id // Use the project-specific parent page
                },
                properties: {
                  title: {
                    title: [
                      {
                        text: {
                          content: title,
                        },
                      },
                    ],
                  },
                },
                // Include first chunk of blocks (up to 100)
                children: blocks.slice(0, 100),
              });
              
              // If successful, break out of retry loop
              break;
            } catch (createError) {
              retryCount++;
              console.error(`Error creating page (attempt ${retryCount}):`, createError);
              
              if (retryCount >= maxRetries) {
                throw createError; // Re-throw after max retries
              }
              
              // Wait before retrying (exponential backoff)
              await new Promise(resolve => setTimeout(resolve, 500 * Math.pow(2, retryCount)));
            }
          }
          
          console.log(`Created page for document: ${title} (${page.id})`);
          
          // If we have more than 100 blocks, append them in chunks
          if (blocks.length > 100) {
            const remainingBlocks = blocks.slice(100);
            const chunks = chunkBlocks(remainingBlocks);
            
            console.log(`Appending ${chunks.length} additional chunks for document: ${title}`);
            
            // Append each chunk to the page
            for (let i = 0; i < chunks.length; i++) {
              const chunk = chunks[i];
              let appendRetryCount = 0;
              
              while (appendRetryCount < maxRetries) {
                try {
                  await notion.blocks.children.append({
                    block_id: page.id,
                    children: chunk,
                  });
                  
                  // If successful, break out of retry loop
                  break;
                } catch (appendError) {
                  appendRetryCount++;
                  console.error(`Error appending chunk ${i+1}/${chunks.length} (attempt ${appendRetryCount}):`, appendError);
                  
                  if (appendRetryCount >= maxRetries) {
                    console.warn(`Failed to append chunk ${i+1} after ${maxRetries} attempts, continuing with next chunk`);
                    break; // Continue with next chunk after max retries
                  }
                  
                  // Wait before retrying (exponential backoff)
                  await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, appendRetryCount)));
                }
              }
              
              // Add a delay between chunks to avoid rate limits
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
          
          console.log(`Successfully exported document to Notion: ${title} (${page.id})`);
          
          return page;
        } catch (error) {
          console.error(`Error exporting document "${title}" to Notion:`, error);
          // Return the error so we can track which documents failed
          return { 
            error: true, 
            docId: doc.id, 
            title, 
            message: error.message || "Unknown error" 
          };
        }
      })
    );

    // Process results to separate successful and failed exports
    const successfulPages = [];
    const failedDocuments = [];

    documentPages.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        const value = result.value;
        if (value && !value.error) {
          successfulPages.push(value);
        } else if (value && value.error) {
          failedDocuments.push(value);
        }
      } else {
        // Promise rejected
        failedDocuments.push({
          docId: projectDocuments[index].id,
          title: projectDocuments[index].title || projectDocuments[index].type,
          message: result.reason?.message || "Promise rejected"
        });
      }
    });

    console.log(`Export summary: ${successfulPages.length} documents exported successfully, ${failedDocuments.length} failed`);

    // Return success with page URL and details about failures
    const pageUrl = `https://notion.so/${parentPage.id.replace(/-/g, '')}`;
    return NextResponse.json({
      success: true,
      message: `Successfully exported ${successfulPages.length} documents to Notion`,
      pageUrl,
      pageId: parentPage.id,
      documentCount: successfulPages.length,
      failedCount: failedDocuments.length,
      failedDocuments: failedDocuments.length > 0 ? failedDocuments : undefined
    });
    
  } catch (error) {
    console.error("Error exporting to Notion:", error);
    
    // Check if it's an API error from Notion
    if (error.status && error.body) {
      return NextResponse.json({ 
        error: `Notion API error: ${error.body.message || error.status}`,
        details: error.body
      }, { status: error.status });
    }
    
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : "Failed to export to Notion",
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}

