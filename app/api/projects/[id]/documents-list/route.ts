// /Users/<USER>/Documents/Provibe-20250521/app/api/projects/[id]/documents-list/route.ts
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/types/supabase'; // Adjust path as needed

const supabaseAdmin = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const projectId = params.id; // The dynamic segment is now 'id', so we use params.id.
  const userId = request.headers.get("x-user-id"); // Assuming you still want to scope by user

  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!projectId) {
    return NextResponse.json({ error: 'Project ID is required' }, { status: 400 });
  }

  try {
    // Optional: Verify user has access to this project first if necessary,
    // or rely on row-level security if documents are user-scoped.
    // For simplicity, directly fetching documents for the project:
    const { data: documents, error } = await supabaseAdmin
      .from('project_documents')
      .select('id, title') // Only fetch necessary fields
      .eq('project_id', projectId)
      // .eq('user_id', userId) // If documents are directly tied to user_id or use RLS
      .order('title', { ascending: true });

    if (error) {
      console.error('Error fetching documents list:', error);
      return NextResponse.json({ error: 'Failed to fetch documents list', details: error.message }, { status: 500 });
    }

    return NextResponse.json(documents);
  } catch (e: any) {
    console.error('Unexpected error fetching documents list:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}