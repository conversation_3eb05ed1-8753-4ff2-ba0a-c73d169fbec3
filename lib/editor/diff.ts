import { diff_match_patch } from 'diff-match-patch'
import { <PERSON>hem<PERSON>, Node as ProsemirrorNode, Fragment } from 'prosemirror-model'

export enum DiffType {
  Unchanged = 0,
  Inserted = 1,
  Deleted = -1,
}

export function diffEditor(
  schema: Schema,
  oldDoc: any,
  newDoc: any,
): ProsemirrorNode {
  const oldNode = ProsemirrorNode.fromJSON(schema, oldDoc)
  const newNode = ProsemirrorNode.fromJSON(schema, newDoc)

  const dmp = new diff_match_patch()
  const diff = dmp.diff_main(oldNode.textContent, newNode.textContent)
  dmp.diff_cleanupSemantic(diff)

  const insertedMark = schema.marks['diffMark']
    ? schema.mark('diffMark', { type: DiffType.Inserted })
    : null
  const deletedMark = schema.marks['diffMark']
    ? schema.mark('diffMark', { type: DiffType.Deleted })
    : null

  const nodes = diff.map(([type, text]) => {
    if (type === 0) {
      return schema.text(text)
    }
    const mark = type === 1 ? insertedMark : deletedMark
    return schema.text(text, mark ? [mark] : [])
  })

  const paragraph = schema.nodes['paragraph']
    ? schema.nodes['paragraph'].create(null, Fragment.fromArray(nodes))
    : schema.node('paragraph', null, Fragment.fromArray(nodes))
  return schema.node('doc', null, Fragment.fromArray([paragraph]))
}
