// /Users/<USER>/Documents/Provibe-20250521/lib/agents/index.ts
import { strategyMentor } from './strategyMentor';
import { marketingGuru } from './marketingGuru';
import { uxCoach } from './uxCoach';
import { projectCreatorAgent } from './projectCreatorAgent';

// Define a type for the agent objects for better type safety
export type Agent = {
  id: string;
  name: string;
  description: string;
  icon: string;
  systemPrompt: string;
};

// Define the structure of the agents object
export type AgentsExport = {
  [key: string]: Agent;
};

export const agents = {
  strategy_mentor: strategyMentor,
  marketing_guru: marketingGuru,
  ux_coach: uxCoach,
  project_creator_agent: projectCreatorAgent,
} as AgentsExport; // Cast to the defined type
