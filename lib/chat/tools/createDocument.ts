// /Users/<USER>/Documents/Provibe-20250521/lib/chat/tools/createDocument.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

export const createDocumentTool = {
  type: 'function',
  function: {
    name: "create_document",
    description: "Creates a new document and saves it to the database. The AI should generate the document title and content based on the user's request and provide them as arguments. The document will be associated with the provided project ID.",
    parameters: {
      type: "object",
      properties: {
        projectId: {
          type: "string",
          description: "The ID of the project to associate this document with. This is a required field."
        },
        title: {
          type: "string",
          description: "The title for the new document. This is a required field."
        },
        content: {
          type: "string",
          description: "The AI-generated content for the document. This is a required field."
        },
        documentType: {
          type: "string",
          description: "Optional type for the document (e.g., 'brand_guideline', 'report'). Defaults to 'general_document'."
        }
      },
      required: ["projectId", "title", "content"]
    },
    execute: async (context: { supabaseAdmin: SupabaseClient<Database>, userId: string }, args: { projectId: string; title: string; content: string; documentType?: string }) => {
      try {
        console.log(`Attempting to create document: "${args.title}" for project ID: ${args.projectId}`);
        const { data, error } = await context.supabaseAdmin
          .from('project_documents')
          .insert({
            project_id: args.projectId,
            title: args.title,
            content: args.content,
            type: args.documentType || 'general_document',
            status: 'completed',
            user_id: context.userId,
            source_type: 'create_document_tool'
          })
          .select('id, title')
          .single();

        if (error) {
          console.error("Error creating document in Supabase:", error);
          throw error;
        }

        console.log(`Document "${data.title}" created successfully with ID ${data.id}.`);
        return JSON.stringify({
          success: true,
          document: data,
          message: `Document "${data.title}" created successfully with ID ${data.id}.`
        });
      } catch (error: any) {
        return JSON.stringify({ success: false, error: `Failed to create document: ${error.message || 'Unknown error'}` });
      }
    }
  }
};