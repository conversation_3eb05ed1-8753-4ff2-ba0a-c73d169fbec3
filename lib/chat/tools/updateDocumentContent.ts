// /Users/<USER>/Documents/Provibe-20250521/lib/chat/tools/updateDocumentContent.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

export const updateDocumentContentTool = {
  type: 'function',
  function: {
    name: "update_document_content",
    description: "Updates the title, content, or type of an existing document after user confirmation. The AI must have already discussed the changes with the user and received explicit approval for the new values.",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The UUID of the document to update. This is a required field."
        },
        projectId: {
          type: "string",
          description: "The UUID of the project this document belongs to. This is required for verification."
        },
        title: {
          type: "string",
          description: "The new title for the document. If omitted, the title will not be changed."
        },
        content: {
          type: "string",
          description: "The new full content for the document. If omitted, the content will not be changed."
        },
        documentType: {
          type: "string",
          description: "The new type for the document (e.g., 'brand_guideline', 'report'). If omitted, the type will not be changed."
        }
      },
      required: ["documentId", "projectId"]
    },
    execute: async (context: { supabaseAdmin: SupabaseClient<Database>, userId: string }, args: { documentId: string; projectId: string; title?: string; content?: string; documentType?: string }) => {
      try {
        console.log(`Attempting to update document ID: ${args.documentId} for project ID: ${args.projectId}`);
        
        const updatePayload: { title?: string; content?: string; type?: string; updated_at?: string } = {};
        if (args.title !== undefined) updatePayload.title = args.title;
        if (args.content !== undefined) updatePayload.content = args.content;
        if (args.documentType !== undefined) updatePayload.type = args.documentType;

        if (Object.keys(updatePayload).length === 0) {
          return JSON.stringify({ success: false, error: "No update fields provided (title, content, or documentType).", documentId: args.documentId });
        }
        updatePayload.updated_at = new Date().toISOString();

        const { data: docData, error: docError } = await context.supabaseAdmin.from('project_documents').select('project_id, user_id').eq('id', args.documentId).single();
        if (docError || !docData) throw new Error(docError?.message || `Document ${args.documentId} not found.`);
        if (docData.project_id !== args.projectId) throw new Error(`Document ${args.documentId} does not belong to project ${args.projectId}.`);
        if (docData.user_id !== context.userId) throw new Error(`User does not have permission to update document ${args.documentId}.`);
        
        const { data, error } = await context.supabaseAdmin
          .from('project_documents')
          .update(updatePayload)
          .eq('id', args.documentId)
          .select('id, title, type')
          .single();

        if (error) throw error;

        return JSON.stringify({ success: true, document: data, message: `Document "${data.title}" (ID: ${data.id}) updated.` });
      } catch (error: any) {
        return JSON.stringify({ success: false, error: `Failed to update document: ${error.message || 'Unknown error'}`, documentId: args.documentId });
      }
    }
  }
};