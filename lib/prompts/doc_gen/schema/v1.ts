export const prompt = {
    id: 'schema_v1',
    docType: 'schema',
    title: 'Database Schema',
    description: 'Generate a normalized database schema with table structures, relationships, and primary keys based on the app’s data model.',
    systemPrompt: 'You are a senior database architect designing a PostgreSQL/Supabase schema.',
    aiPrompt: `Generate a production-ready PostgreSQL schema (compatible with Supabase) for "{{productName}}".
Infer the necessary tables, columns, relationships (primary keys, foreign keys), and indexes based on the provided key features and other inputs. If optional entities/relationships are provided, prioritize them. Follow SQL best practices.
  
  INPUT
  -----
  {{productName}}
  {{keyFeatures}}            ← list of features  
  {{entities}}               ← optional array of high-level entities if pre-identified  
  {{relationships}}          ← optional array of explicit relations  
  {{needsRealtime}}          ← true/false  
  {{needsFileStorage}}       ← true/false  
  
  OUTPUT RULES
  ------------
  1. Output in markdown format.  
  2. Use \`gen_random_uuid()\` for PKs (\`uuid\` type) and \`pgcrypto\` extension.  
  3. Table names in \`snake_case\`; every table has \`created_at\` & \`updated_at\` (timestamptz).  
  4. Define Foreign Key (FK) constraints using \`references <table>(id)\`. Choose \`on delete cascade\`, \`on delete set null\`, or \`on delete restrict\` appropriately based on the inferred relationship (default to \`on delete restrict\` or \`on delete set null\` if unsure).
  5. Create indexes on FK columns.  
  6. If \`needsRealtime\` = true → include \`realtime_enabled\` comment for Supabase.  
  7. If \`needsFileStorage\` = true → include storage-related columns (e.g., \`file_path TEXT\`, \`bucket_id TEXT\`, \`mime_type TEXT\`) in tables likely to reference stored files (e.g., user profiles, documents, project assets).`
  };
  