

export const prompt = {
  id: 'architecture_v1',
  docType: 'architecture',
  title: 'System Architecture',
  description: 'Outline the backend and frontend components, infrastructure, and data flow needed to support the product.',
  systemPrompt: 'You are a senior software architect. Your task is to generate a high-level technical architecture document in markdown format suitable for AI coding agents, detailing the system setup, tech stack, data flow, and component responsibilities based on the provided inputs.',
  aiPrompt: `Generate a high-level technical architecture document in markdown format for "{{productName}}". This document should guide AI coding agents.

Use the inputs below to generate architecture sections with clarity and implementation focus.

---

INPUT VARS
-----------
{{productName}}
{{refinedIdea}}
{{targetAudience}}
{{usp}}
{{keyFeatures}}       ← list of feature names. Properly number the features
{{clarifyingQuestions}}    ← [{question,suggestedAnswer,dimension}]
{{frontendTech}}
{{backendTech}}
{{database}} (*Implicitly needed for Data Flow/Storage*)

---

💡 OUTPUT FORMAT: (markdown)

# System Architecture (AI-Build Ready)

## 1. Frontend
- Tech Stack: List from \`frontendTech\`
- Responsibilities: Rendering UI, collecting user inputs, making API calls
- Notes: Mention key choices like SSR (if applicable), potential component libraries, state management approach (e.g., Zustand, Redux Toolkit).

## 2. Backend
- Tech Stack: List from \`backendTech\`
- Responsibilities: API endpoints, business logic, database integration, auth
- Notes: Async processing, webhook handling, rate limits

## 3. AI/ML Integration
- Purpose: Where AI is used (e.g., document generation, sentiment analysis)
- Model Type: GPT (OpenAI), Gemini, or Hugging Face
- Interaction: How the backend communicates with LLMs

## 4. Data Flow Example
- Describe the data flow for a primary user action (e.g., related to the first key feature): From user input through frontend, backend, database, and any AI services, to the final output/UI update.

## 5. Infrastructure & Deployment
- CI/CD: Vercel or similar
- Auth: Supabase or Clerk
- Storage: Supabase buckets, edge caching, etc.

---

✅ RULES:
- Don't generate diagrams
- Be exact about stack roles and interactions
- Focus on how components connect and communicate
- Mention any specific architectural decisions driven by key features or clarifying questions.`
};