export type Json = string | number | boolean | null | { [key: string]: J<PERSON> | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          full_name: string | null
          avatar_url: string | null
          subscription_tier: "free" | "pro"
          credits_remaining: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: "free" | "pro"
          credits_remaining?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: "free" | "pro"
          credits_remaining?: number
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          user_id: string
          name: string
          idea: string | null
          refined_idea: string | null
          voice_note_url: string | null
          selected_tools: string[] | null
          product_details: Json | null
          project_plan: string | null
          status: "draft" | "completed" | "generating_docs"
          created_at: string
          updated_at: string
          clarifying_questions: Json | null
          last_creation_step: number | null
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          idea?: string | null
          refined_idea?: string | null
          voice_note_url?: string | null
          selected_tools?: string[] | null
          product_details?: Json | null
          project_plan?: string | null
          status?: "draft" | "completed" | "generating_docs"
          created_at?: string
          updated_at?: string
          clarifying_questions?: Json | null
          last_creation_step?: number | null
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          idea?: string | null
          refined_idea?: string | null
          voice_note_url?: string | null
          selected_tools?: string[] | null
          product_details?: Json | null
          project_plan?: string | null
          status?: "draft" | "completed" | "generating_docs"
          created_at?: string
          updated_at?: string
          clarifying_questions?: Json | null
          last_creation_step?: number | null
        }
      }
      project_documents: {
        Row: {
          id: string
          project_id: string
          title: string
          type: string
          content: string | null
          status: "pending" | "generating" | "completed" | "error"
          error_message: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          title: string
          type: string
          content?: string | null
          status?: "pending" | "generating" | "completed" | "error"
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          title?: string
          type?: string
          content?: string | null
          status?: "pending" | "generating" | "completed" | "error"
          error_message?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      credit_usage_log: {
        Row: {
          id: string
          user_id: string
          project_id: string | null
          document_id: string | null
          action: "idea_refinement" | "ai_answer" | "plan_generation" | "document_generation" | "document_regeneration"
          credits_used: number
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          project_id?: string | null
          document_id?: string | null
          action: "idea_refinement" | "ai_answer" | "plan_generation" | "document_generation" | "document_regeneration"
          credits_used: number
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          project_id?: string | null
          document_id?: string | null
          action?: "idea_refinement" | "ai_answer" | "plan_generation" | "document_generation" | "document_regeneration"
          credits_used?: number
          created_at?: string
        }
      }
      ai_tools: {
        Row: {
          id: string
          name: string
          category: string | null
          logo: string
          description: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          name: string
          category?: string | null
          logo: string
          description: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          category?: string | null
          logo?: string
          description?: string
          created_at?: string
          updated_at?: string
        }
      }
      document_types: {
        Row: {
          id: string
          title: string
          icon: string
          cost: number
          description: string
          active: boolean
          ai_prompt: string
          system_prompt: string
          display_order: number | null
        }
        Insert: {
          id: string
          title: string
          icon: string
          cost: number
          description: string
          active?: boolean
          ai_prompt?: string
          system_prompt?: string
          display_order?: number | null
        }
        Update: {
          id?: string
          title?: string
          icon?: string
          cost?: number
          description?: string
          active?: boolean
          ai_prompt?: string
          system_prompt?: string
          display_order?: number | null
        }
      }
      prompt_templates: {
        Row: {
          id: string
          task_slug: string
          task_name: string
          content: string
          free_model: string
          pro_model: string
          enterprise_model: string
          max_tokens: number
          temperature: number
          top_p: number
          created_at: string
          updated_at: string | null
          cost: number | null
        }
        Insert: {
          id?: string
          task_slug: string
          task_name: string
          content: string
          free_model: string
          pro_model: string
          enterprise_model: string
          max_tokens?: number
          temperature?: number
          top_p?: number
          created_at?: string
          updated_at?: string | null
          cost?: number | null
        }
        Update: {
          id?: string
          task_slug?: string
          task_name?: string
          content?: string
          free_model?: string
          pro_model?: string
          enterprise_model?: string
          max_tokens?: number
          temperature?: number
          top_p?: number
          created_at?: string
          updated_at?: string | null
          cost?: number | null
        }
      }
      prompt_executions: {
        Row: {
          id: string
          user_id: string
          project_id: string | null
          api_name: string
          model: string
          tokens_in: number
          tokens_out: number
          latency: number
          executed_at: string
          success: boolean
          error_message: string | null
          prompt_template_id: string | null
          document_id: string | null
        }
        Insert: {
          id?: string
          user_id: string
          project_id?: string | null
          api_name: string
          model: string
          tokens_in: number
          tokens_out: number
          latency: number
          executed_at?: string
          success: boolean
          error_message?: string | null
          prompt_template_id?: string | null
          document_id?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          project_id?: string | null
          api_name?: string
          model?: string
          tokens_in?: number
          tokens_out?: number
          latency?: number
          executed_at?: string
          success?: boolean
          error_message?: string | null
          prompt_template_id?: string | null
          document_id?: string | null
        }
      }
      prompts: {
        Row: {
          id: string
          slug: string
          task: string
          language: string
          template: string
          model: string
          temperature: number | null
          max_tokens: number | null
          created_at: string | null
          created_by: string | null
        }
        Insert: {
          id?: string
          slug: string
          task: string
          language?: string
          template: string
          model: string
          temperature?: number | null
          max_tokens?: number | null
          created_at?: string | null
          created_by?: string | null
        }
        Update: {
          id?: string
          slug?: string
          task?: string
          language?: string
          template?: string
          model?: string
          temperature?: number | null
          max_tokens?: number | null
          created_at?: string | null
          created_by?: string | null
        }
      }
      tier_config: {
        Row: {
          tier_slug: string
          task: string
          prompt_slug: string
          model: string
          temperature: number
          max_tokens_cap: number
          credit_cost: number
          created_at: string | null
        }
        Insert: {
          tier_slug: string
          task: string
          prompt_slug: string
          model: string
          temperature?: number
          max_tokens_cap?: number
          credit_cost?: number
          created_at?: string | null
        }
        Update: {
          tier_slug?: string
          task?: string
          prompt_slug?: string
          model?: string
          temperature?: number
          max_tokens_cap?: number
          credit_cost?: number
          created_at?: string | null
        }
      }
      user_settings: {
        Row: {
          id: string
          user_id: string
          language: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          language?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          language?: string
          created_at?: string | null
          updated_at?: string | null
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
