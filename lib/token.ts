// File: /Users/<USER>/Documents/Provibe-V0/lib/token.ts
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_DOC_SECRET;

if (!JWT_SECRET) {
  console.warn(
    'Warning: JWT_DOC_SECRET is not defined in environment variables. Using a default, insecure secret for development only.'
  );
}
const effectiveJwtSecret = JWT_SECRET || 'your-default-insecure-secret-for-development';

export interface DocAccessPayload {
  projectId: string;
  docType: string;
  iat?: number;
  exp?: number;
}

export function issueDocToken(
  projectId: string,
  docType: string,
  expiresIn: string | number = '5m'
): string {
  if (!effectiveJwtSecret) {
    throw new Error('JWT_DOC_SECRET is not configured.');
  }
  return jwt.sign({ projectId, docType }, effectiveJwtSecret, { expiresIn });
}

export function verifyDocToken(token: string): DocAccessPayload {
  if (!effectiveJwtSecret) {
    throw new Error('JWT_DOC_SECRET is not configured.');
  }
  return jwt.verify(token, effectiveJwtSecret) as DocAccessPayload;
}