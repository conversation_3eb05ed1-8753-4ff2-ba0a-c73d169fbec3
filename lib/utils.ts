import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import type { CoreAssistantMessage, CoreToolMessage, UIMessage } from 'ai';
import type { Document } from '@/lib/db/schema';
import { ChatSDKError, type ErrorCode } from './errors';


export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export const fetcher = (url: string) => fetch(url).then((res) => res.json());

export const fetchWithErrorHandlers = async (url: string, options?: RequestInit) => {
  // Add default headers
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'x-user-id': '2fbff9b7-8492-4068-b80d-df4d9a5d096d', // For demo purposes, use a valid UUID
  };

  const response = await fetch(url, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options?.headers,
    },
  });
  
  if (!response.ok) {
    const error = await response.text();
    throw new Error(error || `HTTP error! status: ${response.status}`);
  }
  
  return response;
};

export function getLocalStorage(key: string) {
  if (typeof window !== 'undefined') {
    return JSON.parse(localStorage.getItem(key) || '[]');
  }
  return [];
}

type ResponseMessageWithoutId = CoreToolMessage | CoreAssistantMessage;
type ResponseMessage = ResponseMessageWithoutId & { id: string };

export function getMostRecentUserMessage(messages: Array<UIMessage>) {
  const userMessages = messages.filter((message) => message.role === 'user');
  return userMessages.at(-1);
}

export function getDocumentTimestampByIndex(
  documents: Array<Document>,
  index: number,
) {
  if (!documents) return new Date();
  if (index > documents.length) return new Date();

  return documents[index].createdAt;
}

export function getTrailingMessageId({
  messages,
}: {
  messages: Array<ResponseMessage>;
}): string | null {
  const trailingMessage = messages.at(-1);

  if (!trailingMessage) return null;

  return trailingMessage.id;
}

export function sanitizeText(text: string) {
  return text.replace('<has_function_call>', '');
}

// Note: The versions of `fetcher`, `fetchWithErrorHandlers`, and `generateUUID` 
// from `utils copy.ts` were not added to `utils.ts` to avoid overwriting
// the existing implementations, as per the requirement "without changing the original code".
