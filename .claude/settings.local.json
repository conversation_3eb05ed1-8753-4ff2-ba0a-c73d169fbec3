{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "Bash(rm:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(ls:*)", "mcp__ide__getDiagnostics", "Bash(npm run dev:*)", "Bash(npx shadcn@latest add:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(jobs)", "Bash(npx tsc:*)", "Bash(cp:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mv:*)"]}, "enableAllProjectMcpServers": false}