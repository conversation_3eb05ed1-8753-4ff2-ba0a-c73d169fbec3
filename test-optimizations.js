#!/usr/bin/env node

/**
 * Quick optimization verification script
 * Run this to check if the optimizations are working correctly
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Project Page Optimizations...\n');

// Test 1: File size reduction
const pagePath = './app/dashboard/project/[id]/page.tsx';
const pageContent = fs.readFileSync(pagePath, 'utf8');
const lineCount = pageContent.split('\n').length;

console.log('📊 File Size Analysis:');
console.log(`- Current page.tsx: ${lineCount} lines`);
console.log(`- Original (from context): ~1,294 lines`);
console.log(`- Reduction: ${1294 - lineCount} lines (${Math.round((1294 - lineCount) / 1294 * 100)}%)`);

if (lineCount < 700) {
  console.log('✅ PASS: Significant size reduction achieved');
} else {
  console.log('❌ FAIL: File still too large');
}

// Test 2: Custom hooks created
const hooksDir = './app/dashboard/project/[id]/hooks';
const hookFiles = fs.readdirSync(hooksDir).filter(f => f.endsWith('.ts'));

console.log('\n🔧 Custom Hooks Analysis:');
console.log(`- Hooks created: ${hookFiles.length}`);
hookFiles.forEach(hook => {
  console.log(`  - ${hook}`);
});

if (hookFiles.length >= 4) {
  console.log('✅ PASS: Custom hooks created for separation of concerns');
} else {
  console.log('❌ FAIL: Not enough custom hooks');
}

// Test 3: Lazy loading implementation
const lazyCount = (pageContent.match(/lazy\(/g) || []).length;
const suspenseCount = (pageContent.match(/Suspense/g) || []).length;

console.log('\n⚡ Lazy Loading Analysis:');
console.log(`- Lazy imports: ${lazyCount}`);
console.log(`- Suspense boundaries: ${suspenseCount}`);

if (lazyCount >= 5 && suspenseCount >= 5) {
  console.log('✅ PASS: Proper lazy loading implemented');
} else {
  console.log('❌ FAIL: Insufficient lazy loading');
}

// Test 4: Zustand selector optimization
const selectorCount = (pageContent.match(/useProjectStore\(\(state\)/g) || []).length;
const jsonStringifyCount = (pageContent.match(/JSON\.stringify/g) || []).length;

console.log('\n🎯 Store Optimization Analysis:');
console.log(`- Individual selectors: ${selectorCount}`);
console.log(`- JSON.stringify usage: ${jsonStringifyCount}`);

if (selectorCount > 5 && jsonStringifyCount === 0) {
  console.log('✅ PASS: Zustand selectors optimized');
} else {
  console.log('❌ FAIL: Store selectors need optimization');
}

// Test 5: Race condition prevention
const projectDataHook = fs.readFileSync('./app/dashboard/project/[id]/hooks/useProjectData.ts', 'utf8');
const cancellationCount = (projectDataHook.match(/isCancelled/g) || []).length;
const cleanupCount = (projectDataHook.match(/return \(\) => \{/g) || []).length;

console.log('\n🏁 Race Condition Prevention:');
console.log(`- Cancellation checks: ${cancellationCount}`);
console.log(`- Cleanup functions: ${cleanupCount}`);

if (cancellationCount > 5 && cleanupCount > 0) {
  console.log('✅ PASS: Race condition prevention implemented');
} else {
  console.log('❌ FAIL: Need better race condition handling');
}

// Summary
console.log('\n📋 OPTIMIZATION SUMMARY:');
console.log('- Bundle size reduced through lazy loading');
console.log('- Custom hooks for better organization');
console.log('- Optimized Zustand selectors');
console.log('- Race condition prevention');
console.log('- Page Visibility API for tab handling');

console.log('\n🚀 To test performance:');
console.log('1. Run `npm run dev`');
console.log('2. Open browser DevTools');
console.log('3. Navigate to /dashboard/project/[id]');
console.log('4. Check Network tab for bundle sizes');
console.log('5. Use Performance tab to measure load times');
console.log('6. Test navigation between projects');
console.log('7. Test tab switching (leave tab for 5+ minutes, come back)');