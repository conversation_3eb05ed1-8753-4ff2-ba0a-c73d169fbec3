#!/usr/bin/env node

/**
 * Deployment script for count columns and triggers
 * This script applies the database migration to add project_count and doc_count
 * fields to the profiles table with automatic triggers.
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

async function deployCountTriggers() {
  console.log('🚀 Deploying count columns and triggers...');
  
  // Check for environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing required environment variables:');
    console.error('  - NEXT_PUBLIC_SUPABASE_URL');
    console.error('  - SUPABASE_SERVICE_ROLE_KEY');
    process.exit(1);
  }
  
  // Initialize Supabase client with service role key
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '../supabase/migrations/add_count_columns_and_triggers.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Executing migration...');
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql: migrationSQL 
    });
    
    if (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
    
    console.log('✅ Migration completed successfully!');
    
    // Verify the columns were added
    const { data: profilesTest, error: testError } = await supabase
      .from('profiles')
      .select('project_count, doc_count')
      .limit(1);
    
    if (testError) {
      console.warn('⚠️  Could not verify columns:', testError.message);
    } else {
      console.log('✅ Count columns verified in profiles table');
    }
    
    console.log('🎉 Deployment complete! The following features are now active:');
    console.log('  • project_count column in profiles table');
    console.log('  • doc_count column in profiles table'); 
    console.log('  • Automatic increment/decrement triggers');
    console.log('  • Initial count calculation for existing users');
    
  } catch (err) {
    console.error('❌ Deployment failed:', err.message);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution if RPC is not available
async function deployCountTriggersDirectSQL() {
  console.log('🚀 Deploying count columns and triggers (Direct SQL method)...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing required environment variables');
    process.exit(1);
  }
  
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    // Read and split the migration into individual statements
    const migrationPath = path.join(__dirname, '../supabase/migrations/add_count_columns_and_triggers.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split by semicolons and filter out empty statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📄 Executing ${statements.length} SQL statements...`);
    
    // Execute each statement individually
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`  ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
      
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error(`❌ Statement ${i + 1} failed:`, error.message);
        console.error('Statement:', statement);
        process.exit(1);
      }
    }
    
    console.log('✅ All statements executed successfully!');
    console.log('🎉 Count triggers deployment complete!');
    
  } catch (err) {
    console.error('❌ Deployment failed:', err.message);
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  // Try the main method first, fallback to direct SQL if needed
  deployCountTriggers().catch(() => {
    console.log('🔄 Retrying with direct SQL method...');
    deployCountTriggersDirectSQL();
  });
}

module.exports = { deployCountTriggers, deployCountTriggersDirectSQL };