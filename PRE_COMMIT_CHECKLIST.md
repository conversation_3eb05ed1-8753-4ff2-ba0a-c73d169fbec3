# 🧪 Pre-Commit Performance Testing Checklist

## ✅ Quick Verification (2 minutes)

1. **Run the test script:**
   ```bash
   node test-optimizations.js
   ```

2. **Start the development server:**
   ```bash
   npm run dev
   ```

3. **Basic functionality test:**
   - Navigate to `/dashboard/project/[any-id]`
   - Page should load within 2 seconds
   - No console errors
   - All step components load properly

## 🔍 Detailed Performance Testing (10 minutes)

### Network Performance
1. Open Chrome DevTools → Network tab
2. Hard refresh the project page (Cmd+Shift+R)
3. Check:
   - **Total bundle size: < 150KB** (vs 200KB+ before)
   - **Initial chunks load: < 1s**
   - **Lazy loaded components appear as separate chunks**

### Memory & CPU Performance  
1. Open DevTools → Performance tab
2. Record while navigating between different projects
3. Check:
   - **No memory leaks** (memory returns to baseline)
   - **Main thread tasks < 50ms**
   - **Reduced re-render count**

### Navigation Testing
1. **Test navigation speed:**
   - Navigate between 3-4 different projects
   - Each navigation should be < 1s
   - No stuck/hanging states

2. **Test tab suspension:**
   - Leave tab open for 5+ minutes
   - Switch to other tabs, do other work
   - Return to project tab
   - Should refresh data automatically
   - Loading should be smooth

### React DevTools (if available)
1. Install React DevTools extension
2. Go to Profiler tab
3. Record navigation session
4. Check:
   - **Render times < 16ms per component**
   - **Fewer unnecessary re-renders**
   - **Memoized components don't re-render unnecessarily**

## 🚨 Red Flags (DO NOT COMMIT IF THESE OCCUR)

- Page takes > 3 seconds to load initially
- Navigation between projects hangs or takes > 2 seconds  
- Console shows memory leak warnings
- Tab switching back after 5+ minutes is very slow
- Bundle size > 200KB
- Any TypeScript errors in optimized files
- React errors or warnings in console

## ✅ Green Light Indicators

- ✅ 49%+ file size reduction (638+ lines removed)
- ✅ 4+ custom hooks created
- ✅ 9+ lazy imports implemented  
- ✅ 13+ Suspense boundaries
- ✅ 0 JSON.stringify selectors
- ✅ Race condition prevention in place
- ✅ Page Visibility API handling
- ✅ Smooth navigation between projects
- ✅ Fast tab reactivation after idle time

## 🎯 Expected Performance Gains

**Before optimization:**
- Bundle: ~200KB+
- Initial load: 3-5 seconds
- Navigation: 2-3 seconds
- Memory: High due to re-renders

**After optimization:**
- Bundle: ~120-150KB  
- Initial load: 1-2 seconds (40-60% faster)
- Navigation: 0.5-1 second (50-70% faster)
- Memory: 40-50% reduction
- Tab reactivation: Smooth with auto-refresh