// src/components/ui/tag-input.tsx (or wherever you place custom UI components)
import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label"; // Import Label
import { X, Plus } from 'lucide-react';
import { cn } from "@/lib/utils"; // Assuming you have a utility for classnames

interface TagInputProps {
  id: string;
  label: string;
  placeholder: string;
  items: string[];
  onAdd: (item: string) => void;
  onRemove: (index: number) => void;
  className?: string;
  tagClassName?: string; // Allow custom styling for tags
  disabled?: boolean;
}

export const TagInput: React.FC<TagInputProps> = ({
  id,
  label,
  placeholder,
  items,
  onAdd,
  onRemove,
  className,
  tagClassName = "bg-muted text-muted-foreground", // Default tag style
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState('');

  const handleAddItem = () => {
    const value = inputValue.trim();
    if (value && !disabled) {
      onAdd(value);
      setInputValue('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddItem();
    }
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id} className="font-medium"> {/* Use Label */}
        {label}
      </Label>
      {/* Input with integrated button */}
      <div className="relative flex items-center">
        <Input
          id={id}
          placeholder={placeholder}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className="pr-10" // Padding for the button
        />
        <Button
          type="button" // Prevent form submission if inside a form
          size="icon"
          variant="ghost"
          onClick={handleAddItem}
          disabled={disabled || !inputValue.trim()}
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 text-muted-foreground hover:bg-accent rounded-full"
          aria-label={`Add ${label}`}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      {/* Tag list */}
      <div className="flex flex-wrap gap-2 min-h-[24px]"> {/* Added min-height */}
        {items?.map((item, index) => (
          <div
            key={index}
            className={cn(
              "flex items-center px-3 py-1 rounded-full text-sm group transition-all duration-150 ease-in-out",
              tagClassName // Apply specific tag style
            )}
          >
            <span>{item}</span>
            <button
              type="button"
              onClick={() => !disabled && onRemove(index)}
              disabled={disabled}
              className={cn(
                "ml-1.5 p-0.5 rounded-full opacity-60 group-hover:opacity-100 focus:opacity-100 focus:ring-1 focus:ring-ring focus:outline-none transition-opacity",
                "disabled:opacity-40 disabled:cursor-not-allowed", // Disabled style for remove button
                "hover:bg-background/50" // Subtle hover on remove button
              )}
              aria-label={`Remove ${item}`}
            >
              <X className="h-3.5 w-3.5" />
            </button>
          </div>
        ))}
        {items?.length === 0 && !disabled && (
           <p className="text-xs text-muted-foreground italic px-1">No {label.toLowerCase()} added yet.</p> // Optional empty state text
        )}
      </div>
    </div>
  );
};