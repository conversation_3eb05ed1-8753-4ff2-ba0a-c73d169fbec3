import React, { Suspense, useState, useEffect } from "react"
import Markdown from "react-markdown"
import remarkGfm from "remark-gfm"

import { cn } from "@/lib/utils"
import { CopyButton } from "@/components/ui/copy-button"

interface MarkdownRendererProps {
  children: string
}

export function MarkdownRenderer({ children }: MarkdownRendererProps) {
  return ( // Added px-6 for left and right padding
    <div className="space-y-3 px-6">
      <Markdown remarkPlugins={[remarkGfm]} components={COMPONENTS}>
        {children}
      </Markdown>
    </div>
  )
}

interface HighlightedPre extends React.HTMLAttributes<HTMLPreElement> {
  children: string
  language: string
}

const HighlightedPre = React.memo(
  ({ children, language, ...props }: HighlightedPre) => {
    const [tokens, setTokens] = useState<any>(null);
    const [isLoading, setIsLoading] = useState(true);
    
    // Use useEffect instead of async component
    useEffect(() => {
      let isMounted = true;
      
      const loadSyntaxHighlighting = async () => {
        try {
          const { codeToTokens, bundledLanguages } = await import("shiki");
          
          if (!isMounted) return;
          
          if (!(language in bundledLanguages)) {
            setIsLoading(false);
            return;
          }
          
          const result = await codeToTokens(children, {
            lang: language as keyof typeof bundledLanguages,
            defaultColor: false,
            themes: {
              light: "github-light",
              dark: "github-dark",
            },
          });
          
          if (isMounted) {
            setTokens(result.tokens);
            setIsLoading(false);
          }
        } catch (error) {
          console.error("Error highlighting code:", error);
          if (isMounted) {
            setIsLoading(false);
          }
        }
      };
      
      loadSyntaxHighlighting();
      
      return () => {
        isMounted = false;
      };
    }, [children, language]);
    
    if (isLoading) {
      return <pre {...props}>{children}</pre>;
    }
    
    if (!tokens) {
      return <pre {...props}>{children}</pre>;
    }
    
    // Render highlighted code with tokens
    return (
      <pre {...props}>
        {tokens.map((line: any, i: number) => (
          <div key={`line-${i}`} className="line">
            {line.map((token: any, j: number) => (
              <span key={`token-${i}-${j}`} style={{ color: token.color }}>
                {token.content}
              </span>
            ))}
          </div>
        ))}
      </pre>
    );
  }
);
HighlightedPre.displayName = "HighlightedCode"

interface CodeBlockProps extends React.HTMLAttributes<HTMLPreElement> {
  children: React.ReactNode
  className?: string
  language: string
}

const CodeBlock = ({
  children,
  className,
  language,
  ...restProps
}: CodeBlockProps) => {
  const code =
    typeof children === "string"
      ? children
      : childrenTakeAllStringContents(children)

  const preClass = cn(
    "whitespace-pre-wrap break-words rounded-md border bg-background/50 p-4 font-mono text-sm", // Replaced overflow-x-scroll with wrapping classes
    className
  )

  return (
    <div className="group/code relative mb-4">
      <Suspense
        fallback={
          <pre className={preClass} {...restProps}>
            {children}
          </pre>
        }
      >
        <HighlightedPre language={language} className={preClass}>
          {code}
        </HighlightedPre>
      </Suspense>

      <div className="invisible absolute right-2 top-2 flex space-x-1 rounded-lg p-1 opacity-0 transition-all duration-200 group-hover/code:visible group-hover/code:opacity-100">
        <CopyButton content={code} copyMessage="Copied code to clipboard" />
      </div>
    </div>
  )
}

function childrenTakeAllStringContents(element: any): string {
  if (typeof element === "string") {
    return element
  }

  if (element?.props?.children) {
    let children = element.props.children

    if (Array.isArray(children)) {
      return children
        .map((child) => childrenTakeAllStringContents(child))
        .join("")
    } else {
      return childrenTakeAllStringContents(children)
    }
  }

  return ""
}

const COMPONENTS = {
  h1: withClass("h1", "text-2xl font-semibold break-words leading-tight"),
  h2: withClass("h2", "font-semibold text-xl break-words"),
  h3: withClass("h3", "font-semibold text-lg break-words"),
  h4: withClass("h4", "font-semibold text-base break-words"),
  h5: withClass("h5", "font-medium break-words"),
  h6: withClass("h6", "font-medium text-sm break-words"), // Added h6 with break-words
  strong: withClass("strong", "font-semibold"),
  a: withClass("a", "text-primary underline underline-offset-2"),
  blockquote: withClass("blockquote", "border-l-2 border-primary pl-4 text-sm font-normal break-words"),
  code: ({ children, className, node, ...rest }: any) => {
    const match = /language-(\w+)/.exec(className || "")
    return match ? (
      <CodeBlock className={className} language={match[1]} {...rest}>
        {children}
      </CodeBlock>
    ) : (
      <code
        className={cn(
          "font-mono [:not(pre)>&]:rounded-md [:not(pre)>&]:bg-background/50 [:not(pre)>&]:px-1 [:not(pre)>&]:py-0.5 break-words text-sm font-normal"
        )}
        {...rest}
      >
        {children}
      </code>
    )
  },
  pre: ({ children }: any) => children,
  ol: withClass("ol", "list-decimal space-y-2 pl-10 ml-6"), // Added ml-4 for block indentation
  ul: withClass("ul", "list-disc space-y-2 pl-10 ml-6"), // Added ml-4 for block indentation
  li: withClass("li", "my-1.5 break-words text-sm font-normal"), // Added font-normal
  table: withClass(
    "table",
    "w-full border-collapse overflow-y-auto rounded-md border border-foreground/20"
  ),
  th: withClass(
    "th",
    "border border-foreground/20 px-4 py-2 text-left font-bold [&[align=center]]:text-center [&[align=right]]:text-right break-words text-sm"
  ),
  td: withClass(
    "td",
    "border border-foreground/20 px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right break-words text-sm font-normal"
  ),
  tr: withClass("tr", "m-0 border-t p-0 even:bg-muted"),
  p: withClass("p", "whitespace-pre-wrap break-words text-sm font-normal"), // Added font-normal
  hr: withClass("hr", "border-foreground/20"),
}

function withClass(Tag: keyof JSX.IntrinsicElements, classes: string) {
  const Component = ({ node, ...props }: any) => (
    <Tag className={classes} {...props} />
  )
  Component.displayName = Tag
  return Component
}

export default MarkdownRenderer
