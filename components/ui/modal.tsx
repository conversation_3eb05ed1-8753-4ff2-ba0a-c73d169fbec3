'use client';

import React from 'react';
import { cn } from '@/lib/utils'; // Ensure this path is correct

interface ModalProps {
  children: React.ReactNode;
  onClose: () => void;
  size?: 'default' | 'large' | 'full-width';
  contentClassName?: string; // Class for the direct content wrapper
}

export default function Modal({ children, onClose, size = 'default', contentClassName }: ModalProps) {
  const containerClasses = cn(
    "fixed inset-0 flex z-50",
    // For non-full-width modals, center them and add padding around them so they don't touch screen edges.
    // For full-width, items-stretch and justify-stretch allow the modalBox to fill the container.
    size !== 'full-width' ? "items-center justify-center p-4" : "items-stretch justify-stretch"
  );

  const modalBoxClasses = cn(
    "relative bg-white z-10 overflow-y-auto", // Base styles: relative positioning, background, z-index, and scrolling
    {
      // Default: standard dialog size, includes padding (p-6)
      "p-6 rounded-lg shadow-xl max-w-lg max-h-[90vh]": size === 'default',
      // Large: wider dialog, includes padding (p-6)
      "p-6 rounded-lg shadow-xl sm:max-w-xl md:max-w-2xl lg:max-w-4xl max-h-[90vh]": size === 'large',
      // Full-width: takes up entire viewport. NO padding, rounding, or shadow by default from modalBox itself.
      "w-full h-full": size === 'full-width',
    },
    contentClassName // Allows further customization of the modal box appearance
  );

  // Content for children, handling padding specifically for the full-width scenario
  let childrenContent = children;
  if (size === 'full-width') {
    // For full-width, wrap children in a div that provides necessary padding,
    // especially top padding to accommodate the absolute positioned close button.
    // This inner div will also handle scrolling for the content within the full-width modal.
    childrenContent = (
      <div className="p-4 md:p-6 pt-12 md:pt-14 h-full w-full overflow-y-auto">
        {children}
      </div>
    );
  }
  // For 'default' or 'large' sizes, children are rendered directly because modalBoxClasses already includes p-6.

  return (
    <div className={containerClasses}>
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} /> {/* Standard overlay */}

      {/* Modal Content */}
      <div className={modalBoxClasses}>
        <button 
          onClick={onClose} 
          className={cn(
            "absolute z-20 p-1 rounded-full text-gray-500 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-500", // More accessible focus state
            "top-3 right-3 hover:bg-gray-200/70" // Consistent position and subtle hover
          )}
          aria-label="Close modal"
        >
          {/* Using a consistent SVG X icon for clarity */}
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        {childrenContent}
      </div>
    </div>
  );
}