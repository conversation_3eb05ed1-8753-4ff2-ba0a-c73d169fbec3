import { useState, forwardRef } from 'react' // Added forwardRef
import {
  CHECK_LIST,
  ELEMENT_TRANSFORMERS,
  MULTILINE_ELEMENT_TRANSFORMERS,
  TEXT_FORMAT_TRANSFORMERS,
  TEXT_MATCH_TRANSFORMERS,
} from '@lexical/markdown'

import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin'
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin'
import { ClearEditorPlugin } from '@lexical/react/LexicalClearEditorPlugin'
import { ClickableLinkPlugin } from '@lexical/react/LexicalClickableLinkPlugin'
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary'
import { HashtagPlugin } from '@lexical/react/LexicalHashtagPlugin'
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin'
import { HorizontalRulePlugin } from '@lexical/react/LexicalHorizontalRulePlugin'
import { ListPlugin } from '@lexical/react/LexicalListPlugin'
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin'
import { TablePlugin } from '@lexical/react/LexicalTablePlugin'

import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button' // Added Button for trigger
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  // DropdownMenuItem, // Not directly used, plugins render their own controls
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu' // Added Dropdown components

import { BlockFormatDropDown } from '@/components/editor/plugins/toolbar/block-format-toolbar-plugin'
import { FormatBulletedList } from '@/components/editor/plugins/toolbar/block-format/format-bulleted-list'
import { FormatCheckList } from '@/components/editor/plugins/toolbar/block-format/format-check-list'
import { FormatCodeBlock } from '@/components/editor/plugins/toolbar/block-format/format-code-block'
import { FormatHeading } from '@/components/editor/plugins/toolbar/block-format/format-heading'
import { FormatNumberedList } from '@/components/editor/plugins/toolbar/block-format/format-numbered-list'
import { FormatParagraph } from '@/components/editor/plugins/toolbar/block-format/format-paragraph'
import { FormatQuote } from '@/components/editor/plugins/toolbar/block-format/format-quote'
import { BlockInsertPlugin } from '@/components/editor/plugins/toolbar/block-insert-plugin'
import { InsertCollapsibleContainer } from '@/components/editor/plugins/toolbar/block-insert/insert-collapsible-container'
import { InsertColumnsLayout } from '@/components/editor/plugins/toolbar/block-insert/insert-columns-layout'
import { InsertEmbeds } from '@/components/editor/plugins/toolbar/block-insert/insert-embeds'
import { InsertExcalidraw } from '@/components/editor/plugins/toolbar/block-insert/insert-excalidraw'
import { InsertHorizontalRule } from '@/components/editor/plugins/toolbar/block-insert/insert-horizontal-rule'
import { InsertImage } from '@/components/editor/plugins/toolbar/block-insert/insert-image'
import { InsertInlineImage } from '@/components/editor/plugins/toolbar/block-insert/insert-inline-image'
import { InsertPageBreak } from '@/components/editor/plugins/toolbar/block-insert/insert-page-break'
import { InsertPoll } from '@/components/editor/plugins/toolbar/block-insert/insert-poll'
import { InsertTable } from '@/components/editor/plugins/toolbar/block-insert/insert-table'
import { ClearFormattingToolbarPlugin } from '@/components/editor/plugins/toolbar/clear-formatting-toolbar-plugin'
import { CodeLanguageToolbarPlugin } from '@/components/editor/plugins/toolbar/code-language-toolbar-plugin'
import { ElementFormatToolbarPlugin } from '@/components/editor/plugins/toolbar/element-format-toolbar-plugin'
import { FontBackgroundToolbarPlugin } from '@/components/editor/plugins/toolbar/font-background-toolbar-plugin'
import { FontColorToolbarPlugin } from '@/components/editor/plugins/toolbar/font-color-toolbar-plugin'
import { FontFamilyToolbarPlugin } from '@/components/editor/plugins/toolbar/font-family-toolbar-plugin'
import { FontFormatToolbarPlugin } from '@/components/editor/plugins/toolbar/font-format-toolbar-plugin'
import { FontSizeToolbarPlugin } from '@/components/editor/plugins/toolbar/font-size-toolbar-plugin'
import { HistoryToolbarPlugin } from '@/components/editor/plugins/toolbar/history-toolbar-plugin'
import { LinkToolbarPlugin } from '@/components/editor/plugins/toolbar/link-toolbar-plugin'
import { SubSuperToolbarPlugin } from '@/components/editor/plugins/toolbar/subsuper-toolbar-plugin'

import { ActionsPlugin } from '@/components/editor/plugins/actions/actions-plugin'
import { CharacterLimitPlugin } from '@/components/editor/plugins/actions/character-limit-plugin'
import { ClearEditorActionPlugin } from '@/components/editor/plugins/actions/clear-editor-plugin'
import { CounterCharacterPlugin } from '@/components/editor/plugins/actions/counter-character-plugin'
import { EditModeTogglePlugin } from '@/components/editor/plugins/actions/edit-mode-toggle-plugin'
import { ImportExportPlugin } from '@/components/editor/plugins/actions/import-export-plugin'
import { MarkdownTogglePlugin } from '@/components/editor/plugins/actions/markdown-toggle-plugin'
import { MaxLengthPlugin } from '@/components/editor/plugins/actions/max-length-plugin'
import { ShareContentPlugin } from '@/components/editor/plugins/actions/share-content-plugin'
import { SpeechToTextPlugin } from '@/components/editor/plugins/actions/speech-to-text-plugin'
import { TreeViewPlugin } from '@/components/editor/plugins/actions/tree-view-plugin'
import { AutoLinkPlugin } from '@/components/editor/plugins/auto-link-plugin'
import { AutocompletePlugin } from '@/components/editor/plugins/autocomplete-plugin'
import { CodeActionMenuPlugin } from '@/components/editor/plugins/code-action-menu-plugin'
import { CodeHighlightPlugin } from '@/components/editor/plugins/code-highlight-plugin'
import { CollapsiblePlugin } from '@/components/editor/plugins/collapsible-plugin'
import { ComponentPickerMenuPlugin } from '@/components/editor/plugins/component-picker-menu-plugin'
import { ContextMenuPlugin } from '@/components/editor/plugins/context-menu-plugin'
import { DragDropPastePlugin } from '@/components/editor/plugins/drag-drop-paste-plugin'
import { DraggableBlockPlugin } from '@/components/editor/plugins/draggable-block-plugin'
import { AutoEmbedPlugin } from '@/components/editor/plugins/embeds/auto-embed-plugin'
import { FigmaPlugin } from '@/components/editor/plugins/embeds/figma-plugin'
import { TwitterPlugin } from '@/components/editor/plugins/embeds/twitter-plugin'
import { YouTubePlugin } from '@/components/editor/plugins/embeds/youtube-plugin'
import { EmojiPickerPlugin } from '@/components/editor/plugins/emoji-picker-plugin'
import { EmojisPlugin } from '@/components/editor/plugins/emojis-plugin'
import { EquationsPlugin } from '@/components/editor/plugins/equations-plugin'
import { ExcalidrawPlugin } from '@/components/editor/plugins/excalidraw-plugin'
import { FloatingLinkEditorPlugin } from '@/components/editor/plugins/floating-link-editor-plugin'
import { FloatingTextFormatToolbarPlugin } from '@/components/editor/plugins/floating-text-format-plugin'
import { ImagesPlugin } from '@/components/editor/plugins/images-plugin'
import { InlineImagePlugin } from '@/components/editor/plugins/inline-image-plugin'
import { KeywordsPlugin } from '@/components/editor/plugins/keywords-plugin'
import { LayoutPlugin } from '@/components/editor/plugins/layout-plugin'
import { LinkPlugin } from '@/components/editor/plugins/link-plugin'
import { ListMaxIndentLevelPlugin } from '@/components/editor/plugins/list-max-indent-level-plugin'
import { MentionsPlugin } from '@/components/editor/plugins/mentions-plugin'
import { PageBreakPlugin } from '@/components/editor/plugins/page-break-plugin'
import { PollPlugin } from '@/components/editor/plugins/poll-plugin'
import { TabFocusPlugin } from '@/components/editor/plugins/tab-focus-plugin'
import { TableActionMenuPlugin } from '@/components/editor/plugins/table-action-menu-plugin'
import { TableCellResizerPlugin } from '@/components/editor/plugins/table-cell-resizer-plugin'
import { TableHoverActionsPlugin } from '@/components/editor/plugins/table-hover-actions-plugin'
import { ToolbarPlugin } from '@/components/editor/plugins/toolbar/toolbar-plugin'
import { TypingPerfPlugin } from '@/components/editor/plugins/typing-pref-plugin'
import { ContentEditable } from '@/components/editor/editor-ui/content-editable'

import { AlignmentPickerPlugin } from '@/components/editor/plugins/picker/alignment-picker-plugin'
import { ParagraphPickerPlugin } from '@/components/editor/plugins/picker/paragraph-picker-plugin'
import { HeadingPickerPlugin } from '@/components/editor/plugins/picker/heading-picker-plugin'
import { DynamicTablePickerPlugin, TablePickerPlugin } from '@/components/editor/plugins/picker/table-picker-plugin'
import { EmbedsPickerPlugin } from '@/components/editor/plugins/picker/embeds-picker-plugin'
import { CheckListPickerPlugin } from '@/components/editor/plugins/picker/check-list-picker-plugin'
import { NumberedListPickerPlugin } from '@/components/editor/plugins/picker/numbered-list-picker-plugin'
import { BulletedListPickerPlugin } from '@/components/editor/plugins/picker/bulleted-list-picker-plugin'
import { QuotePickerPlugin } from '@/components/editor/plugins/picker/quote-picker-plugin'
import { CodePickerPlugin } from '@/components/editor/plugins/picker/code-picker-plugin'
import { DividerPickerPlugin } from '@/components/editor/plugins/picker/divider-picker-plugin'
import { PageBreakPickerPlugin } from '@/components/editor/plugins/picker/page-break-picker-plugin'
import { ImagePickerPlugin } from '@/components/editor/plugins/picker/image-picker-plugin'
import { ExcalidrawPickerPlugin } from '@/components/editor/plugins/picker/excalidraw-picker-plugin'
import { PollPickerPlugin } from '@/components/editor/plugins/picker/poll-picker-plugin'
import { EquationPickerPlugin } from '@/components/editor/plugins/picker/equation-picker-plugin'
import { CollapsiblePickerPlugin } from '@/components/editor/plugins/picker/collapsible-picker-plugin'
import { ColumnsLayoutPickerPlugin } from '@/components/editor/plugins/picker/columns-layout-picker-plugin'

import { EMOJI } from '@/components/editor/transformers/markdown-emoji-transformer'
import { EQUATION } from '@/components/editor/transformers/markdown-equation-transofrmer'
import { HR } from '@/components/editor/transformers/markdown-hr-transformer'
import { IMAGE } from '@/components/editor/transformers/markdown-image-transformer'
import { TABLE } from '@/components/editor/transformers/markdown-table-transformer'
import { TWEET } from '@/components/editor/transformers/markdown-tweet-transformer'

// Import icons for dropdown triggers
import {
  CaseSensitive, // For Font controls
  AlignLeft as AlignLeftIconLucide, // For Alignment/Indentation (avoid conflict with component)
  Wand2, // For Advanced formatting
  Palette, // For Font Style dropdown
  Sparkles, // For AI Refine button
} from 'lucide-react'

export const placeholder = 'Press / for commands...'
const defaultMaxLength = 500; // Renamed from maxLength to avoid conflict with prop

// --- Editor Toolbar ---
// Added type annotation for props
export function EditorToolbar({ floatingAnchorElem }: { floatingAnchorElem: HTMLElement | null }) {
  // 1. Add state for AI overlay
  const [showAiOverlay, setShowAiOverlay] = useState(false);
  return (
    <ToolbarPlugin>
      {({ blockType }) => (
        // Removed sticky/top-0/z-10, apply styling in the page layout
        // Adjusted padding and gap for consistency
        <div className="flex flex-wrap items-center gap-1 bg-card p-1 px-2 md:gap-2 relative">
          
          <HistoryToolbarPlugin />


          <Separator orientation="vertical" className="h-6 md:h-8" />
          <BlockFormatDropDown>
            <FormatParagraph />
            <FormatHeading levels={['h1', 'h2', 'h3']} />
            <FormatNumberedList />
            <FormatBulletedList />
            <FormatCheckList />
            <FormatCodeBlock />
            <FormatQuote />
          </BlockFormatDropDown>
          {blockType === 'code' ? (
            <CodeLanguageToolbarPlugin />
          ) : (
            <>
          <Separator orientation="vertical" className="h-6 md:h-8" />

                <FontFamilyToolbarPlugin />
                <FontSizeToolbarPlugin />

              <Separator orientation="vertical" className="h-6 md:h-8" />

              {/* Font Formatting Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 px-2">
                    <CaseSensitive className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuGroup className="flex items-center gap-1 p-1">
                    <FontFormatToolbarPlugin format="bold" />
                    <FontFormatToolbarPlugin format="italic" />
                    <FontFormatToolbarPlugin format="underline" />
                    <FontFormatToolbarPlugin format="strikethrough" />
                  
                    <FontColorToolbarPlugin />
                    <FontBackgroundToolbarPlugin />
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Alignment & Indentation Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 px-2">
                    <AlignLeftIconLucide className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuGroup className="flex items-center gap-1 p-1">
                    <ElementFormatToolbarPlugin />
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Advanced Formatting Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 px-2">
                    <Wand2 className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuGroup className="flex items-center gap-1 p-1">
                    <SubSuperToolbarPlugin />
                    <LinkToolbarPlugin />
                    <ClearFormattingToolbarPlugin />
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu> {/* This is the correct closing tag */}
              {/* AI Refine Button */}
              {/* Updated AI Button: Text, Icon, Border, Color */}
              <div className="relative">
                <Button
                  variant="outline" // Use outline for a border
                  size="sm"
                  className="h-8 px-2 gap-1 bg-purple-100 text-purple-800 border-purple-300 hover:bg-purple-200"
                  onClick={() => setShowAiOverlay(prev => !prev)}
                  title="Refine selection with AI"
                >
                  AI
                  <Sparkles className="h-4 w-4" /> {/* Icon next to text */}
                </Button>
                {/* 3. Render overlay if showAiOverlay is true */}
                {showAiOverlay && (
                  <div className="absolute top-12 right-4 z-50 w-64 p-4 bg-white border rounded shadow-xl">
                    <p className="text-sm font-medium mb-2">AI Tools</p>
                    <ul className="space-y-2 text-sm">
                      <li className="cursor-pointer hover:underline">Refine Selection</li>
                      <li className="cursor-pointer hover:underline">Summarize</li>
                      <li className="cursor-pointer hover:underline">Generate Outline</li>
                    </ul>
                  </div>
                )}
              </div>

              <Separator orientation="vertical" className="h-6 md:h-8" />

              <BlockInsertPlugin>
                <InsertImage />
                <InsertInlineImage />
                <InsertCollapsibleContainer />
                <InsertExcalidraw />
                <InsertTable />
                <InsertPoll />
                <InsertColumnsLayout />
                <InsertHorizontalRule />
                <InsertPageBreak />
                <InsertEmbeds />
              </BlockInsertPlugin>

              <ShareContentPlugin /> 
              <EditModeTogglePlugin /> 
                <ClearEditorActionPlugin />
                <ClearEditorPlugin />




            </>
          )}
          {/* Floating elements need the anchor */}
           <FloatingLinkEditorPlugin anchorElem={floatingAnchorElem} sideOffset={6} alignOffset={6} />
           <FloatingTextFormatToolbarPlugin anchorElem={floatingAnchorElem} sideOffset={6} alignOffset={6} />
           {/* Add other floating plugins here if needed */}
        </div>
      )}
    </ToolbarPlugin>
  )
}

// --- Editor Content Area ---
// Use forwardRef to get the div for floatingAnchorElem
// Added type annotations for props
export const EditorContent = forwardRef<HTMLDivElement, {
  placeholder?: string;
  floatingAnchorElem: HTMLElement | null; // Receive anchor for floating plugins
  // Removed onRef, use the standard `ref` from forwardRef
}>(({ placeholder: contentPlaceholder = placeholder, floatingAnchorElem }, ref) => {
  return (
    // This outer div is crucial for positioning and scrolling within the page layout
    // Removed relative class, as the inner div handles it
    <div className="h-full flex-1 overflow-y-auto px-4 py-2 md:px-8 md:py-4">
       {/* The ref is attached here for floating elements */}
      <div className="relative" ref={ref}>
        <RichTextPlugin
          contentEditable={
            // Removed editor-container wrapper div
            // Removed inner div, ref is now on the direct parent
            <ContentEditable
              placeholder={contentPlaceholder}
              className='ContentEditable__root relative block min-h-[50vh] focus:outline-none' // Adjusted min-h
            />
          }
          placeholder={null} // Placeholder handled by ContentEditable directly
          ErrorBoundary={LexicalErrorBoundary}
        />
        {/* Core & Node Specific Plugins */}
        <AutoFocusPlugin />
        <CheckListPlugin />
        <ListPlugin />
        <TabIndentationPlugin />
        <HorizontalRulePlugin />
        <TablePlugin />
        <HashtagPlugin />
        <HistoryPlugin /> {/* Keep HistoryPlugin here for undo/redo */}
        <MentionsPlugin />
        <PageBreakPlugin />
        <KeywordsPlugin />
        <EmojisPlugin />
        <ImagesPlugin />
        <InlineImagePlugin />
        <ExcalidrawPlugin />
        <PollPlugin />
        <LayoutPlugin />
        <EquationsPlugin />
        <CollapsiblePlugin />
        <CodeHighlightPlugin />

        {/* Embed Plugins */}
        <AutoEmbedPlugin />
        <FigmaPlugin />
        <TwitterPlugin />
        <YouTubePlugin />

        {/* Link Handling */}
        <ClickableLinkPlugin />
        <AutoLinkPlugin />
        <LinkPlugin />

        {/* Markdown & Shortcuts */}
        <MarkdownShortcutPlugin
          transformers={[
            TABLE, HR, IMAGE, EMOJI, EQUATION, TWEET, CHECK_LIST,
            ...ELEMENT_TRANSFORMERS, ...MULTILINE_ELEMENT_TRANSFORMERS,
            ...TEXT_FORMAT_TRANSFORMERS, ...TEXT_MATCH_TRANSFORMERS,
          ]}
        />

        {/* UI/UX Enhancing Plugins */}
        <ComponentPickerMenuPlugin
           baseOptions={[
            ParagraphPickerPlugin(),
            HeadingPickerPlugin({ n: 1 }),
            HeadingPickerPlugin({ n: 2 }),
            HeadingPickerPlugin({ n: 3 }),
            TablePickerPlugin(),
            CheckListPickerPlugin(),
            NumberedListPickerPlugin(),
            BulletedListPickerPlugin(),
            QuotePickerPlugin(),
            CodePickerPlugin(),
            DividerPickerPlugin(),
            PageBreakPickerPlugin(),
            ExcalidrawPickerPlugin(),
            PollPickerPlugin(),
            EmbedsPickerPlugin({ embed: 'figma' }),
            EmbedsPickerPlugin({ embed: 'tweet' }),
            EmbedsPickerPlugin({ embed: 'youtube-video' }),
            EquationPickerPlugin(),
            ImagePickerPlugin(),
            CollapsiblePickerPlugin(),
            ColumnsLayoutPickerPlugin(),
            AlignmentPickerPlugin({ alignment: 'left' }),
            AlignmentPickerPlugin({ alignment: 'center' }),
            AlignmentPickerPlugin({ alignment: 'right' }),
            AlignmentPickerPlugin({ alignment: 'justify' }),
           ]}
           dynamicOptionsFn={DynamicTablePickerPlugin}
        />
        <ContextMenuPlugin />
        <DragDropPastePlugin />
        <EmojiPickerPlugin />
        <ListMaxIndentLevelPlugin />
        <TabFocusPlugin />
        <AutocompletePlugin />

        {/* Plugins requiring anchorElem */}
        <DraggableBlockPlugin anchorElem={floatingAnchorElem} />
        <CodeActionMenuPlugin anchorElem={floatingAnchorElem} />
        <TableCellResizerPlugin />
        <TableHoverActionsPlugin anchorElem={floatingAnchorElem} />
        <TableActionMenuPlugin anchorElem={floatingAnchorElem} cellMerge={true} />
        {/* FloatingLinkEditorPlugin & FloatingTextFormatToolbarPlugin moved to EditorToolbar */}

        {/* Performance/Debug */}
        <TypingPerfPlugin />
      </div>
    </div>
  )
});
EditorContent.displayName = 'EditorContent'; // Add display name for DevTools

// --- Editor Footer ---
// Added type annotation for props
export function EditorFooter({ maxLength = defaultMaxLength }: { maxLength?: number }) {
  return (
    <ActionsPlugin>
      {/* Removed clear-both, flex layout handles this */}
      {/* Adjusted padding and gap */}
      <div className="flex flex-wrap items-center justify-between gap-2 border-t bg-background p-1 px-2">
        <div className='flex flex-wrap items-center justify-start gap-2'>
          <MaxLengthPlugin maxLength={maxLength} />
          <CharacterLimitPlugin maxLength={maxLength} charset="UTF-16" />
        </div>
        <div className='flex flex-wrap items-center justify-center gap-2'>
          <CounterCharacterPlugin charset="UTF-16" />
        </div>
        <div className="flex flex-wrap items-center justify-end gap-1 md:gap-2">

    

       </div>
      </div>
    </ActionsPlugin>
  )
}

// Original combined component (kept for potential backward compatibility or other uses)
// Note: This combined component won't work correctly with the refactored structure
// without managing the floatingAnchorElem state properly here.
// It's generally better to use the separated components in the page layout.
export function EditorPlugins() {
  const [floatingAnchorElem, setFloatingAnchorElem] = useState<HTMLDivElement | null>(null)

  // This ref handling is simplified and might not be robust for the combined component
  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem)
    }
  }

  return (
    <div className="relative">
      {/* Pass the state down */}
      <EditorToolbar floatingAnchorElem={floatingAnchorElem} />
      {/* Need to wrap EditorContent in a way to capture the ref */}
      {/* This simple structure won't work directly with forwardRef */}
      {/* <EditorContent
        placeholder={placeholder}
        floatingAnchorElem={floatingAnchorElem}
        ref={onRef} // This won't work directly with forwardRef like this
      /> */}
      {/* Placeholder div to show where content would go */}
      <div ref={onRef} className="p-4 border-y">Editor Content Area (Refactor needed for combined component)</div>
      <EditorFooter maxLength={defaultMaxLength} />
    </div>
  )
}
