// /components/blocks/editor-x/editor.tsx
'use client'

import { ReactNode } from 'react';
import {
  InitialConfigType,
  LexicalComposer,
} from '@lexical/react/LexicalComposer';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { EditorState, SerializedEditorState } from 'lexical';

import { $convertFromMarkdownString, TRANSFORMERS } from '@lexical/markdown';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';

// Keep context providers if they are essential across Toolbar/Content/Footer
// import { FloatingLinkContext } from '@/components/editor/context/floating-link-context';
// import { SharedAutocompleteContext } from '@/components/editor/context/shared-autocomplete-context';
import { editorTheme } from '@/components/editor/themes/editor-theme';
import { TooltipProvider } from '@/components/ui/tooltip';

import { nodes } from './nodes';

const DEFAULT_EDITOR_STATE = {
  root: {
    children: [
      {
        children: [
          { detail: 0, format: 0, mode: 'normal', style: '', text: '', type: 'text', version: 1 },
        ],
        direction: 'ltr', format: '', indent: 0, type: 'paragraph', version: 1,
      },
    ],
    direction: 'ltr', format: '', indent: 0, type: 'root', version: 1,
  },
};

// Base editor config
const baseEditorConfig: Omit<InitialConfigType, 'editorState' | 'editable'> = { // Exclude editorState and editable here
  namespace: 'EditorX',
  theme: editorTheme,
  nodes,
  onError: (error: Error) => {
    console.error("Lexical Error:", error);
  },
};

interface EditorProps {
  editorSerializedState?: SerializedEditorState | string | null | undefined;
  onSerializedChange?: (state: SerializedEditorState) => void;
  children: ReactNode;
  editable?: boolean; // Make editable optional, default to true
}

export function Editor({
  editorSerializedState,
  onSerializedChange,
  children,
  editable = true, // Default to true
}: EditorProps) {

  // Function to safely prepare the initial state for LexicalComposer
  const getInitialEditorState = () => {
    return () => {
      // No need for complex logic here - we'll handle it in the initialConfig
    };
  };

  // Create the final initial config
  const initialConfig: InitialConfigType = {
    ...baseEditorConfig,
    editorState: editorSerializedState
      ? typeof editorSerializedState === 'string'
        ? () => $convertFromMarkdownString(editorSerializedState, TRANSFORMERS)
        : JSON.stringify(editorSerializedState)
      : JSON.stringify(DEFAULT_EDITOR_STATE),
    editable: editable,
  };

  // Handle editor changes
  const onChange = (editorState: EditorState) => {
    if (onSerializedChange) {
      editorState.read(() => {
        const serialized = editorState.toJSON();
        onSerializedChange(serialized);
      });
    }
  };

  return (
    <TooltipProvider>
      <LexicalComposer initialConfig={initialConfig}>
        <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
        {/* Render the children passed from the page */}
        {children}

        {/* OnChangePlugin listens for changes within the composer context */}
        {/* ignoreSelectionChange is often useful, set back to true if needed */}
        {onSerializedChange && <OnChangePlugin onChange={onChange} ignoreSelectionChange={false} />}
      </LexicalComposer>
    </TooltipProvider>
  );
}
