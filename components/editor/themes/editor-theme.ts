import { EditorThemeClasses } from 'lexical'

import './editor-theme.css'

export const editorTheme: EditorThemeClasses = {
  ltr: 'text-left',
  rtl: 'text-right',
  root: 'max-w-4xl mx-auto px-16 py-6 bg-white min-h-screen font-inter',
  heading: {
    h1: 'text-4xl font-bold mb-6 mt-1 text-gray-900 leading-tight',
    h2: 'text-3xl font-bold mb-4 mt-1 text-gray-900 leading-tight',
    h3: 'text-2xl font-bold mb-3 mt-1 text-gray-900 leading-tight',
    h4: 'text-xl font-bold mb-3 mt-1 text-gray-900 leading-tight',
    h5: 'text-lg font-bold mb-2 mt-1 text-gray-900 leading-tight',
    h6: 'text-base font-bold mb-2 mt-1 text-gray-900 leading-tight',
  },
  paragraph: 'text-base leading-7 mb-4 text-gray-800 font-inter',
  quote: 'my-6 border-l-4 border-gray-300 pl-6 italic text-gray-700 bg-gray-50 py-4',
  link: 'text-blue-600 underline hover:text-blue-800 cursor-pointer',
  list: {
    checklist: 'relative my-2',
    listitem: 'mb-2 text-base leading-7 text-gray-800',
    listitemChecked:
      'relative pl-8 mb-2 text-base leading-7 text-gray-600 line-through list-none outline-none before:content-[""] before:w-5 before:h-5 before:top-0.5 before:left-0 before:cursor-pointer before:block before:absolute before:border-2 before:border-blue-500 before:rounded before:bg-blue-500 after:content-[""] after:cursor-pointer after:border-white after:border-solid after:absolute after:block after:top-[8px] after:w-[4px] after:left-[8px] after:h-[8px] after:rotate-45 after:border-r-2 after:border-b-2 after:border-l-0 after:border-t-0',
    listitemUnchecked:
      'relative pl-8 mb-2 text-base leading-7 text-gray-800 list-none outline-none before:content-[""] before:w-5 before:h-5 before:top-0.5 before:left-0 before:cursor-pointer before:block before:absolute before:border-2 before:border-gray-400 before:rounded before:bg-white',
    nested: {
      listitem: 'list-none before:hidden after:hidden mb-1',
    },
    ol: 'my-4 pl-6 text-base leading-7 text-gray-800 [&>li]:mb-2 [&>li]:pl-2',
    olDepth: [
      'list-decimal',
      'list-[upper-roman]',
      'list-[lower-roman]',
      'list-[upper-alpha]',
      'list-[lower-alpha]',
    ],
    ul: 'my-4 pl-6 text-base leading-7 text-gray-800 [&>li]:mb-2 [&>li]:pl-2 [&>li]:list-disc',
  },
  hashtag: 'text-blue-600 bg-blue-50 rounded-md px-2 py-1 text-sm font-medium',
  text: {
    bold: 'font-bold text-gray-900',
    code: 'bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded font-mono text-sm border',
    italic: 'italic',
    strikethrough: 'line-through text-gray-600',
    subscript: 'align-sub text-sm',
    superscript: 'align-super text-sm',
    underline: 'underline decoration-2',
    underlineStrikethrough: 'underline line-through decoration-2 text-gray-600',
  },
  image: 'relative inline-block select-none cursor-default editor-image',
  inlineImage:
    'relative inline-block select-none cursor-default inline-editor-image',
  keyword: 'text-purple-900 font-bold',
  code: 'EditorTheme__code',
  codeHighlight: {
    atrule: 'EditorTheme__tokenAttr',
    attr: 'EditorTheme__tokenAttr',
    boolean: 'EditorTheme__tokenProperty',
    builtin: 'EditorTheme__tokenSelector',
    cdata: 'EditorTheme__tokenComment',
    char: 'EditorTheme__tokenSelector',
    class: 'EditorTheme__tokenFunction',
    'class-name': 'EditorTheme__tokenFunction',
    comment: 'EditorTheme__tokenComment',
    constant: 'EditorTheme__tokenProperty',
    deleted: 'EditorTheme__tokenProperty',
    doctype: 'EditorTheme__tokenComment',
    entity: 'EditorTheme__tokenOperator',
    function: 'EditorTheme__tokenFunction',
    important: 'EditorTheme__tokenVariable',
    inserted: 'EditorTheme__tokenSelector',
    keyword: 'EditorTheme__tokenAttr',
    namespace: 'EditorTheme__tokenVariable',
    number: 'EditorTheme__tokenProperty',
    operator: 'EditorTheme__tokenOperator',
    prolog: 'EditorTheme__tokenComment',
    property: 'EditorTheme__tokenProperty',
    punctuation: 'EditorTheme__tokenPunctuation',
    regex: 'EditorTheme__tokenVariable',
    selector: 'EditorTheme__tokenSelector',
    string: 'EditorTheme__tokenSelector',
    symbol: 'EditorTheme__tokenProperty',
    tag: 'EditorTheme__tokenProperty',
    url: 'EditorTheme__tokenOperator',
    variable: 'EditorTheme__tokenVariable',
  },
  characterLimit: 'bg-destructive/50',
  table: 'EditorTheme__table w-full my-6 border-collapse border border-gray-300 shadow-sm',
  tableCell:
    'EditorTheme__tableCell relative border border-gray-300 px-4 py-3 text-left text-base bg-white [&[align=center]]:text-center [&[align=right]]:text-right',
  tableCellActionButton:
    'EditorTheme__tableCellActionButton bg-background block border-0 rounded-2xl w-5 h-5 text-foreground cursor-pointer',
  tableCellActionButtonContainer:
    'EditorTheme__tableCellActionButtonContainer block right-1 top-1.5 absolute z-10 w-5 h-5',
  tableCellEditing: 'EditorTheme__tableCellEditing rounded-sm shadow-sm',
  tableCellHeader:
    'EditorTheme__tableCellHeader bg-gray-50 border border-gray-300 px-4 py-3 text-left font-bold text-gray-900 [&[align=center]]:text-center [&[align=right]]:text-right',
  tableCellPrimarySelected:
    'EditorTheme__tableCellPrimarySelected border border-primary border-solid block h-[calc(100%-2px)] w-[calc(100%-2px)] absolute -left-[1px] -top-[1px] z-10',
  tableCellResizer:
    'EditorTheme__tableCellResizer absolute -right-1 h-full w-2 cursor-ew-resize z-10 top-0',
  tableCellSelected: 'EditorTheme__tableCellSelected bg-muted',
  tableCellSortedIndicator:
    'EditorTheme__tableCellSortedIndicator block opacity-50 absolute bottom-0 left-0 w-full h-1 bg-muted',
  tableResizeRuler:
    'EditorTheme__tableCellResizeRuler block absolute w-[1px] h-full bg-primary top-0',
  tableRowStriping:
    'EditorTheme__tableRowStriping m-0 border-t p-0 even:bg-muted',
  tableSelected: 'EditorTheme__tableSelected ring-2 ring-primary ring-offset-2',
  tableSelection: 'EditorTheme__tableSelection bg-transparent',
  layoutItem: 'border border-dashed px-4 py-2',
  layoutContainer: 'grid gap-2.5 my-2.5 mx-0',
  autocomplete: 'text-muted-foreground',
  blockCursor: '',
  embedBlock: {
    base: 'select-none',
    focus: 'ring-2 ring-primary ring-offset-2',
  },
  hr: 'my-8 border-none h-px bg-gray-300 cursor-pointer [&.selected]:ring-2 [&.selected]:ring-blue-500 [&.selected]:ring-offset-2 [&.selected]:select-none',
  indent: '[--lexical-indent-base-value:40px]',
  mark: '',
  markOverlap: '',
}