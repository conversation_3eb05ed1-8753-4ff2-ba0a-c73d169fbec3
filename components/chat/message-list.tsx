import {
  ChatMessage,
  type ChatMessageProps,
  type Message,
} from "@/components/chat/chat-message"
import { TypingIndicator } from "@/components/ui/typing-indicator"

type AdditionalMessageOptions = Omit<ChatMessageProps, keyof Message>

interface MessageListProps {
  messages: Message[]
  showTimeStamps?: boolean
  isTyping?: boolean
  messageOptions?:
    | AdditionalMessageOptions
    | ((message: Message) => AdditionalMessageOptions)
  onAction?: (action: { type: string; payload: any }) => Promise<any>
}

export function MessageList({
  messages,
  showTimeStamps,
  isTyping,
  messageOptions,
  onAction,
}: MessageListProps) {
  // Add debugging
  console.log("MessageList messages:", messages.map(m => ({
    id: m.id,
    role: m.role,
    type: m.type,
    action_type: m.action_type,
    hasActionPayload: !!m.action_payload
  })));

  return (
    <div className="space-y-6 py-8">
      {messages.map((message) => {
        const extraOptions =
          typeof messageOptions === "function"
            ? messageOptions(message)
            : messageOptions

        return (
          <ChatMessage
            key={message.id}
            {...message}
            {...extraOptions}
            showTimeStamp={showTimeStamps}
            onAction={onAction}
          />
        )
      })}
      {isTyping && (
        <div className="px-4 py-2">
          <TypingIndicator />
        </div>
      )}
    </div>
  )
}
