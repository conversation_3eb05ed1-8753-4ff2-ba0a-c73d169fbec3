"use client"

import React, { use<PERSON>emo, useState, useRef } from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { motion } from "framer-motion"
import { Ban, ChevronRight, Code2, Loader2, Terminal, Check } from "lucide-react"

import { cn } from "@/lib/utils"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { FilePreview } from "@/components/ui/file-preview"
import { MarkdownRenderer } from "@/components/ui/markdown-renderer"
import { Button } from "@/components/ui/button"

const chatBubbleVariants = cva(
  "group/message relative break-words rounded-3xl pt-3 pr-6 pl-6 pb-0 text-sm sm:max-w-[100%]",
  {
    variants: {
      isUser: {
        true: "bg-sky-100 text-sky-900 dark:bg-sky-700 dark:text-sky-50 sm:max-w-[80%]", // Light blue for user
        false: "text-foreground", // No explicit background for assistant, text color adapts
      },
      animation: {
        none: "",
        slide: "duration-300 animate-in fade-in-0",
        scale: "duration-300 animate-in fade-in-0 zoom-in-75",
        fade: "duration-500 animate-in fade-in-0",
      },
    },
    compoundVariants: [
      {
        isUser: true,
        animation: ["slide", "scale", "fade"], // Apply to all animations for user
        class: "slide-in-from-right",
      },
      {
        isUser: false,
        animation: ["slide", "scale", "fade"], // Apply to all animations for assistant
        class: "slide-in-from-left",
      },
      {
        isUser: true,
        animation: "scale",
        class: "origin-bottom-right",
      },
      {
        isUser: false,
        animation: "scale",
        class: "origin-bottom-left",
      },
    ],
  }
)

type Animation = VariantProps<typeof chatBubbleVariants>["animation"]

interface Attachment {
  name?: string
  contentType?: string
  url: string
}

interface PartialToolCall {
  state: "partial-call"
  toolName: string
}

interface ToolCall {
  state: "call"
  toolName: string
}

interface ToolResult {
  state: "result"
  toolName: string
  result: {
    __cancelled?: boolean
    [key: string]: any
  }
}

type ToolInvocation = PartialToolCall | ToolCall | ToolResult

interface ReasoningPart {
  type: "reasoning"
  reasoning: string
}

interface ToolInvocationPart {
  type: "tool-invocation"
  toolInvocation: ToolInvocation
}

interface TextPart {
  type: "text"
  text: string
}

// For compatibility with AI SDK types, not used
interface SourcePart {
  type: "source"
}

type ActionPart = {
  type: "action"
  actionType: string
  actionPayload: any
}

type MessagePart = TextPart | ReasoningPart | ToolInvocationPart | ActionPart

export interface Message {
  id: string
  role: "user" | "assistant" | (string & {})
  content: string
  createdAt?: Date
  experimental_attachments?: Attachment[]
  toolInvocations?: ToolInvocation[]
  parts?: MessagePart[]
}

export interface ChatMessageProps {
  id?: string
  content: string
  role: "user" | "assistant" | "system" | "function" | "data" | "tool"
  createdAt?: Date
  toolInvocations?: ToolInvocation[]
  parts?: MessagePart[]
  showTimeStamp?: boolean
  animation?: "none" | "animate"
  actions?: React.ReactNode
  type?: string
  action_type?: string
  action_payload?: any
  onAction?: (action: { type: string; payload: any }) => void
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  role,
  content,
  createdAt,
  showTimeStamp = false,
  animation = "scale",
  actions,
  experimental_attachments,
  toolInvocations,
  parts,
  type = "normal",
  action_type,
  action_payload,
  onAction,
}) => {
  // Add state for button
  const [buttonState, setButtonState] = useState<"idle" | "loading" | "success" | "error">("idle");
  
  // Add a ref to track if the action is in progress
  const actionInProgressRef = useRef(false);
  
  // Add debugging
  console.log("ChatMessage props:", { 
    role, content, type, action_type, 
    hasActionPayload: !!action_payload,
    actionPayload: action_payload
  });

  const files = useMemo(() => {
    return experimental_attachments?.map((attachment) => {
      const dataArray = dataUrlToUint8Array(attachment.url)
      const file = new File([dataArray], attachment.name ?? "Unknown")
      return file
    })
  }, [experimental_attachments])

  const isUser = role === "user"

  const formattedTime = createdAt?.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  })

  // Modify the handleCreateProject function to prevent race conditions
  const handleCreateProject = async () => {
    if (action_payload?.project && onAction) {
      try {
        // Prevent multiple clicks using both state and ref
        if (buttonState === "loading" || buttonState === "success" || actionInProgressRef.current) {
          console.log("Action already in progress, ignoring click");
          return;
        }
        
        // Set the ref immediately to prevent race conditions
        actionInProgressRef.current = true;
        setButtonState("loading");
        
        console.log("Starting project creation...");
        
        // Call the onAction handler with the project payload
        await onAction({
          type: "confirm-project",
          payload: { project: action_payload.project }
        });
        
        console.log("Project creation completed successfully");
        
        // If successful, set to success
        setButtonState("success");
      } catch (error) {
        console.error("Error creating project:", error);
        setButtonState("error");
      } finally {
        // Always reset the ref
        actionInProgressRef.current = false;
      }
    }
  };

  if (isUser) {
    return (
      <div
        className={cn("flex flex-col", isUser ? "items-end" : "items-start")}
      >
        {files ? (
          <div className="mb-1 flex flex-wrap gap-2">
            {files.map((file, index) => {
              return <FilePreview file={file} key={index} />
            })}
          </div>
        ) : null}

        <div className={cn(chatBubbleVariants({ isUser, animation }))}>
          <MarkdownRenderer>{content}</MarkdownRenderer>
        </div>

        {showTimeStamp && createdAt ? (
          <time
            dateTime={createdAt.toISOString()}
            className={cn(
              "mt-1 block px-1 text-xs opacity-50",
              animation !== "none" && "duration-500 animate-in fade-in-0"
            )}
          >
            {formattedTime}
          </time>
        ) : null}
      </div>
    )
  }

  // Modify the renderParts function to handle action parts with button state
  function renderParts() {
    if (!parts?.length) return null

    return parts.map((part, index) => {
      if (part.type === "text") {
        return (
          <div
            className={cn(
              "flex flex-col",
              isUser ? "items-end" : "items-start"
            )}
            key={`text-${index}`}
          >
            <div className={cn(chatBubbleVariants({ isUser, animation }))}>
              <MarkdownRenderer>{part.text}</MarkdownRenderer>
              {actions ? (
                <div className="absolute -bottom-4 right-2 flex space-x-1 rounded-lg border bg-background p-1 text-foreground opacity-0 transition-opacity group-hover/message:opacity-100">
                  {actions}
                </div>
              ) : null}
            </div>

            {showTimeStamp && createdAt ? (
              <time
                dateTime={createdAt.toISOString()}
                className={cn(
                  "mt-1 block px-1 text-xs opacity-50",
                  animation !== "none" && "duration-500 animate-in fade-in-0"
                )}
              >
                {formattedTime}
              </time>
            ) : null}
          </div>
        )
      } else if (part.type === "reasoning") {
        return <ReasoningBlock key={`reasoning-${index}`} part={part} />
      } else if (part.type === "tool-invocation") {
        return (
          <ToolCall
            key={`tool-${index}`}
            toolInvocations={[part.toolInvocation]}
          />
        )
      } else if (part.type === "action" && part.actionType === "confirm_project_creation") {
        // Use the same renderActionButton function for consistency
        return (
          <div key={`action-${index}`} className="mt-2">
            {renderActionButton()}
          </div>
        )
      }
      return null
    })
  }

  // Update renderActionButton to handle button states
  const renderActionButton = () => {
    if (type === "action" && action_type === "confirm_project_creation" && action_payload?.project) {
      return (
        <div className="mt-2">
          <Button 
            onClick={handleCreateProject}
            className="flex items-center gap-2"
            variant="default"
            disabled={buttonState === "loading" || buttonState === "success" || actionInProgressRef.current}
          >
            {buttonState === "loading" ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Creating Project...
              </>
            ) : buttonState === "success" ? (
              <>
                <Check className="h-4 w-4" />
                Project Created
              </>
            ) : buttonState === "error" ? (
              <>
                <Ban className="h-4 w-4" />
                Failed - Try Again
              </>
            ) : (
              <>
                <Check className="h-4 w-4" />
                Create Project
              </>
            )}
          </Button>
        </div>
      )
    }
    return null;
  };

  if (toolInvocations && toolInvocations.length > 0) {
    return <ToolCall toolInvocations={toolInvocations} />
  }

  return (
    <div className={cn("flex flex-col", isUser ? "items-end" : "items-start")}>
      <div className={cn(chatBubbleVariants({ isUser, animation }))}>
        <MarkdownRenderer>{content}</MarkdownRenderer>
        {/* Remove this button from here - we'll only use renderActionButton */}
        {actions ? (
          <div className="absolute -bottom-4 right-2 flex space-x-1 rounded-lg border bg-background p-1 text-foreground opacity-0 transition-opacity group-hover/message:opacity-100">
            {actions}
          </div>
        ) : null}
      </div>

      {/* Only render the action button here */}
      {renderActionButton()}

      {showTimeStamp && createdAt ? (
        <time
          dateTime={createdAt.toISOString()}
          className={cn(
            "mt-1 block px-1 text-xs opacity-50",
            animation !== "none" && "duration-500 animate-in fade-in-0"
          )}
        >
          {formattedTime}
        </time>
      ) : null}
    </div>
  )
}

function dataUrlToUint8Array(data: string) {
  const base64 = data.split(",")[1]
  const buf = Buffer.from(base64, "base64")
  return new Uint8Array(buf)
}

const ReasoningBlock = ({ part }: { part: ReasoningPart }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="mb-2 flex flex-col items-start sm:max-w-[70%]">
      <Collapsible
        open={isOpen}
        onOpenChange={setIsOpen}
        className="group w-full overflow-hidden rounded-lg border bg-muted/50"
      >
        <div className="flex items-center p-2">
          <CollapsibleTrigger asChild>
            <button className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground">
              <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]:rotate-90" />
              <span>Thinking</span>
            </button>
          </CollapsibleTrigger>
        </div>
        <CollapsibleContent forceMount>
          <motion.div
            initial={false}
            animate={isOpen ? "open" : "closed"}
            variants={{
              open: { height: "auto", opacity: 1 },
              closed: { height: 0, opacity: 0 },
            }}
            transition={{ duration: 0.3, ease: [0.04, 0.62, 0.23, 0.98] }}
            className="border-t"
          >
            <div className="p-2">
              <div className="whitespace-pre-wrap text-xs">
                {part.reasoning}
              </div>
            </div>
          </motion.div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}

function ToolCall({
  toolInvocations,
}: Pick<ChatMessageProps, "toolInvocations">) {
  if (!toolInvocations?.length) return null

  return (
    <div className="flex flex-col items-start gap-2">
      {toolInvocations.map((invocation, index) => {
        const isCancelled =
          invocation.state === "result" &&
          invocation.result.__cancelled === true

        if (isCancelled) {
          return (
            <div
              key={index}
              className="flex items-center gap-2 rounded-lg border bg-muted/50 px-3 py-2 text-sm text-muted-foreground"
            >
              <Ban className="h-4 w-4" />
              <span>
                Cancelled{" "}
                <span className="font-mono">
                  {"`"}
                  {invocation.toolName}
                  {"`"}
                </span>
              </span>
            </div>
          )
        }

        switch (invocation.state) {
          case "partial-call":
          case "call":
            return (
              <div
                key={index}
                className="flex items-center gap-2 rounded-lg border bg-muted/50 px-3 py-2 text-sm text-muted-foreground"
              >
                <Terminal className="h-4 w-4" />
                <span>
                  Calling{" "}
                  <span className="font-mono">
                    {"`"}
                    {invocation.toolName}
                    {"`"}
                  </span>
                  ...
                </span>
                <Loader2 className="h-3 w-3 animate-spin" />
              </div>
            )
          case "result":
            return (
              <div
                key={index}
                className="flex flex-col gap-1.5 rounded-lg border bg-muted/50 px-3 py-2 text-sm"
              >
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Code2 className="h-4 w-4" />
                  <span>
                    Result from{" "}
                    <span className="font-mono">
                      {"`"}
                      {invocation.toolName}
                      {"`"}
                    </span>
                  </span>
                </div>
                <pre className="overflow-x-auto whitespace-pre-wrap text-foreground">
                  {JSON.stringify(invocation.result, null, 2)}
                </pre>
              </div>
            )
          default:
            return null
        }
      })}
    </div>
  )
}
