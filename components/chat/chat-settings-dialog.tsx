"use client"

import { useState } from "react"
import { Settings } from "lucide-react"
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"

interface ChatSettingsDialogProps {
  models: Array<{ id: string, name: string }>
  projects: Array<{ id: string, name: string }>
  documentTypes: Array<{ id: string, title: string }>
  selectedModel: string
  selectedProject: string
  selectedDocType: string
  onModelChange: (value: string) => void
  onProjectChange: (value: string) => void
  onDocTypeChange: (value: string) => void
  isLoading?: boolean
  agents?: Array<{ id: string, name: string, description: string, icon: string }>
  selectedAgent?: string
  onAgentChange?: (value: string) => void
}

export function ChatSettingsDialog({
  models,
  projects,
  documentTypes,
  selectedModel,
  selectedProject,
  selectedDocType,
  onModelChange,
  onProjectChange,
  onDocTypeChange,
  isLoading = false,
  agents,
  selectedAgent,
  onAgentChange
}: ChatSettingsDialogProps) {
  const [open, setOpen] = useState(false)
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8" title="Chat Settings">
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Chat Settings</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Agent selector */}
          {agents && selectedAgent && onAgentChange && (
            <div className="grid gap-2">
              <Label htmlFor="agent">Agent</Label>
              <Select 
                value={selectedAgent} 
                onValueChange={onAgentChange}
              >
                <SelectTrigger id="agent">
                  <SelectValue placeholder="Select Agent" />
                </SelectTrigger>
                <SelectContent>
                  {agents.map((agent) => (
                    <SelectItem key={agent.id} value={agent.id}>
                      <div className="flex items-center">
                        <span className="mr-2">{agent.icon}</span>
                        {agent.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          <div className="grid gap-2">
            <Label htmlFor="model">Model</Label>
            <Select 
              value={selectedModel} 
              onValueChange={onModelChange}
            >
              <SelectTrigger id="model">
                <SelectValue placeholder="Select Model" />
              </SelectTrigger>
              <SelectContent>
                {models.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    {model.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="project">Project</Label>
            <Select 
              value={selectedProject} 
              onValueChange={onProjectChange}
              disabled={isLoading}
            >
              <SelectTrigger id="project">
                <SelectValue placeholder="Select Project" />
              </SelectTrigger>
              <SelectContent>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="docType">Document Type</Label>
            <Select 
              value={selectedDocType} 
              onValueChange={onDocTypeChange}
              disabled={isLoading}
            >
              <SelectTrigger id="docType">
                <SelectValue placeholder="Select Document Type" />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map((docType) => (
                  <SelectItem key={docType.id} value={docType.id}>
                    {docType.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
