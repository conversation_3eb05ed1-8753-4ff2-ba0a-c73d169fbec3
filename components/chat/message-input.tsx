// For global trigger
declare global {
  interface Window {
    __triggerProjectWizardAgent?: () => void;
  }
}
"use client"

import * as React from "react"
import { useRef, useState, useEffect, useMemo } from "react"
import { Paperclip, SendIcon, StopCircle, X, FileText } from "lucide-react"
import { FileEditIcon, FilePlusIcon, Briefcase, Bot } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { ChatSettingsDialog } from "@/components/chat/chat-settings-dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Add this import for file previews
import { FilePreview } from "@/components/ui/file-preview"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { agents as importedAgents, Agent as AgentConfigType } from "@/lib/chat/agents"; // Import agents
import { getSlashCommands, getInlineChatCommands } from "@/lib/chat/slashcommands"

export interface MessageInputProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  value: string
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  allowAttachments?: boolean
  files?: File[]
  setFiles?: (files: File[]) => void
  stop?: () => void
  isGenerating?: boolean
  transcribeAudio?: (blob: Blob) => Promise<string>
  chatSettings?: {
    models: Array<{ id: string, name: string }>
    projects: Array<{ id: string, name: string }>
    documentTypes: Array<{ id: string, title: string }>
    selectedModel: string
    selectedProject: string
    selectedDocType: string
    onModelChange: (value: string) => void
    onProjectChange: (value: string) => void
    onDocTypeChange: (value: string) => void
    isLoading?: boolean
  }
  onSend: () => void
  selectedAgent?: { id: string; name: string; icon?: React.ReactNode };
  setSelectedAgent?: (agent: { id: string; name: string; icon?: React.ReactNode } | null) => void;
  availableAgents?: Array<{ id: string; name: string; icon?: React.ReactNode }>;
  projectDocuments?: Array<{ id: string; name: string; type?: string /* e.g., 'pdf', 'docx' for specific icons later */ }>;
}

export function MessageInput({
  className,
  value,
  onChange,
  allowAttachments,
  files = [], // Ensure files is initialized as an empty array by default
  setFiles,
  stop,
  isGenerating,
  transcribeAudio,
  chatSettings,
  onSend,
  selectedAgent, // Destructure selectedAgent
  setSelectedAgent, // Destructure setSelectedAgent
  availableAgents, // Destructure availableAgents
  projectDocuments = [], // Destructure projectDocuments, default to empty array
  ...props
}: MessageInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [isDragging, setIsDragging] = useState(false)

  // Stable helper to map agent icon strings to ReactNode icons
  const mapAgentIconStringToReactNode = React.useCallback((iconString?: string): React.ReactNode => {
    const iconProps = { className: "h-4 w-4 mr-2" };
    switch (iconString?.toLowerCase()) {
      case "briefcase":
      case "project_creator_agent": // Match by ID or a generic icon string
        return <Briefcase {...iconProps} />;
      case "fileedit":
      case "document_analyzer_agent": // Example
        return <FileEditIcon {...iconProps} />;
      case "strategy_mentor":
      case "marketing_guru":
      case "ux_coach":
      case "bot": // Explicitly for default or fallback
        return <Bot {...iconProps} />; // Placeholder, customize as needed
      default:
        return <Bot {...iconProps} />; // Default icon
    }
  }, []);

  // Configuration for the default/clear agent option
  const defaultAgentDisplayConfig = useMemo(() => ({
    id: "default", // Special ID for the default/clear option
    name: "Default Assistant",
    description: "General purpose assistant.",
    iconNode: mapAgentIconStringToReactNode("bot"), // Use the helper for consistency
  }), [mapAgentIconStringToReactNode]);

  // State for agent selector overlay
  const [showAgentSelector, setShowAgentSelector] = useState(false)
  const displayableAgents = useMemo(() => {
    const actualAgents = Object.values(importedAgents).map((agent: AgentConfigType) => ({
      id: agent.id, // Ensure all necessary properties are explicitly mapped
      name: agent.name,
      description: agent.description,
      iconNode: mapAgentIconStringToReactNode(agent.icon || agent.id),
    }));
    return [defaultAgentDisplayConfig, ...actualAgents]; // Prepend default agent
  }, [importedAgents, mapAgentIconStringToReactNode, defaultAgentDisplayConfig]);

  // State for document selector overlay
  const [showDocumentSelector, setShowDocumentSelector] = useState(false);

  // Chat-like create project flow state
  const [chatMode, setChatMode] = useState<null | "create-project">(null)
  const [chatStep, setChatStep] = useState<"confirm" | "questions" | "done" | null>(null)
  const [chatInputs, setChatInputs] = useState<{ name?: string; goal?: string }>({})

  const [activeCommand, setActiveCommand] = useState<null | any>(null)
  // Patch the create-project slash command to chat mode
  const commands = useMemo(() => {
    const base = getInlineChatCommands(setActiveCommand)
    return base.map(cmd => {
      if (cmd.id === "create-project") {
        return {
          ...cmd,
          action: () => {
            setActiveCommand(base.find(c => c.id === "create-project"))
            setChatMode("create-project")
            setChatStep("confirm")
          }
        }
      }
      return cmd
    })
  }, [setActiveCommand])

  const [showCommands, setShowCommands] = useState(false)
  const [filteredCommands, setFilteredCommands] = useState(() => commands)
  const [selectedCommandIndex, setSelectedCommandIndex] = useState(0)

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)

    if (allowAttachments && setFiles && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files)
      setFiles([...files, ...newFiles])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (allowAttachments && setFiles && e.target.files?.length) {
      // Make sure e.target.files is converted to an array properly
      const newFiles = Array.from(e.target.files);
      
      // Ensure files is treated as an array, defaulting to empty array if undefined
      const currentFiles = Array.isArray(files) ? files : [];
      
      // Now we can safely spread the arrays
      setFiles([...currentFiles, ...newFiles]);
    }
  }

  const handleKeyDown = async (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showCommands) {
      if (e.key === "ArrowDown") {
        e.preventDefault()
        setSelectedCommandIndex((prev) => (prev + 1) % filteredCommands.length)
        return
      }
      if (e.key === "ArrowUp") {
        e.preventDefault()
        setSelectedCommandIndex((prev) => (prev - 1 + filteredCommands.length) % filteredCommands.length)
        return
      }
      if (e.key === "Enter") {
        e.preventDefault()
        const cmd = filteredCommands[selectedCommandIndex]
        if (cmd) {
          setShowCommands(false)
          setSelectedCommandIndex(0)
          cmd.action()
        }
        return
      }
    }
    // Chat-mode create-project flow
    if (chatMode === "create-project" && chatStep === "confirm") {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        const trimmed = value.trim().toLowerCase()
        if (trimmed === "yes") {
          setChatStep("questions")
        } else if (trimmed === "no") {
          setChatMode(null)
          setActiveCommand(null)
        }
        onChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)
        return
      }
    }
    if (chatMode === "create-project" && chatStep === "questions") {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        if (!chatInputs.name) {
          setChatInputs({ ...chatInputs, name: value.trim() })
          onChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)
          return
        }
        if (!chatInputs.goal) {
          setChatInputs({ ...chatInputs, goal: value.trim() })
          setChatStep("done")
          onChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)
          return
        }
      }
    }
    if (chatMode === "create-project" && chatStep === "done") {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        const trimmed = value.trim().toLowerCase()
        if (trimmed === "confirm") {
          const supabase = createClientComponentClient()
          const { data: session } = await supabase.auth.getSession()
          const userId = session?.session?.user?.id
          if (userId) {
            const { data: project } = await supabase
              .from("projects")
              .insert([{ title: chatInputs.name, goal: chatInputs.goal, user_id: userId }])
              .select()
              .single()
            if (project?.id) {
              chatSettings?.onProjectChange(project.id)
            }
          }
          setChatMode(null)
          setActiveCommand(null)
          setChatInputs({})
          setChatStep(null)
        } else if (trimmed === "edit") {
          setChatStep("questions")
        }
        onChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)
        return
      }
    }
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      onSend()
      return
    }
  }

  // Add a function to remove a file
  const removeFile = (index: number) => {
    if (setFiles) {
      const newFiles = [...files];
      newFiles.splice(index, 1);
      setFiles(newFiles);
    }
  };

  // Make sure we don't pass transcribeAudio to the Textarea component
  // Also, ensure selectedAgent, setSelectedAgent, and availableAgents are not passed.
  // Since they are now destructured in the function signature, they are not in `props`.
  // The `transcribeAudio: _` is also redundant as `transcribeAudio` is destructured above.
  // So, `textareaProps` will correctly be `props` (the rest of TextareaHTMLAttributes).
  const { 
    transcribeAudio: _transcribeAudio, // Explicitly excluding, though already out of `props`
    ...textareaProps } = props;

  useEffect(() => {
    const lastWord = value.split(" ").pop() || ""
    if (lastWord.startsWith("/") && lastWord.length === 1) {
      setShowCommands(true)
      setFilteredCommands([...commands])
      setSelectedCommandIndex(0)
    } else {
      setShowCommands(false)
    }
  }, [value, commands])

  // Register global trigger for project wizard agent selection
  useEffect(() => {
    window.__triggerProjectWizardAgent = () => {
      setActiveCommand({
        id: "project-wizard",
        label: "Project Wizard",
        icon: () => React.createElement(Briefcase, { className: "h-4 w-4 mr-2" })
      });
      // Optionally set an active agent if you have agent selection state
      // setActiveAgent("project_creator_agent")
      onChange({
        target: {
          value: "System: I am your project creation wizard. What would you like to call your project?"
        }
      } as React.ChangeEvent<HTMLTextAreaElement>);
    };
    return () => {
      delete window.__triggerProjectWizardAgent;
    }
  }, [onChange]);

  return (
    <div
      className={cn(
        "relative mx-auto flex w-[100%] flex-col rounded-2xl border bg-background p-2 pb-2 shadow-lg",
        isDragging && "border-primary",
        className 
      )}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* Chat-like create project flow system messages */}
      {chatMode === "create-project" && (
        <div className="mb-3 flex flex-col gap-2 text-sm">
          {chatStep === "confirm" && (
            <div className="rounded-md bg-muted/60 px-3 py-2 text-muted-foreground">
              <span className="font-semibold">System:</span> Would you like to create a new project? (yes/no)
            </div>
          )}
          {chatStep === "questions" && (
            <>
              {!chatInputs.name && (
                <div className="rounded-md bg-muted/60 px-3 py-2 text-muted-foreground">
                  <span className="font-semibold">System:</span> What is the name of your project?
                </div>
              )}
              {chatInputs.name && !chatInputs.goal && (
                <div className="rounded-md bg-muted/60 px-3 py-2 text-muted-foreground">
                  <span className="font-semibold">System:</span> What is the main goal of your project?
                </div>
              )}
            </>
          )}
          {chatStep === "done" && (
            <div className="rounded-md bg-muted/60 px-3 py-2 text-muted-foreground">
              <span className="font-semibold">System:</span> <span>
                Please confirm project creation:
                <br />
                <b>Name:</b> {chatInputs.name}
                <br />
                <b>Goal:</b> {chatInputs.goal}
                <br />
                Type <b>confirm</b> to create, or <b>edit</b> to change details.
              </span>
            </div>
          )}
        </div>
      )}

      {/* Slash command menu */}
      {showCommands && filteredCommands.length > 0 && (
        <div className="absolute bottom-[100%] mb-2 left-0 z-50 w-full rounded-md border bg-popover p-2 text-sm shadow-md">
          {filteredCommands.map((cmd, idx) => (
            <div
              key={cmd.command}
              onClick={() => {
                setShowCommands(false)
                setSelectedCommandIndex(0)
                cmd.action()
              }}
              className={cn(
                "cursor-pointer px-3 py-2 rounded-md hover:bg-accent",
                idx === selectedCommandIndex && "bg-muted"
              )}
            >
              <div className="flex items-center">
                {cmd.icon && typeof cmd.icon === "function" ? cmd.icon() : cmd.icon}
                <div className="flex flex-col">
                  <span className="font-medium">{cmd.label}</span>
                  <span className="text-xs text-muted-foreground">{cmd.description}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Agent Selector Overlay */}
      {showAgentSelector && (
        <div className="absolute bottom-[100%] mb-2 left-0 z-50 w-full rounded-md border bg-popover p-2 text-sm shadow-md">
          <div className="font-medium mb-1 px-3 text-muted-foreground">Select an Agent</div>
          {displayableAgents.map((agent) => (
            <div
              key={agent.id}
              onClick={() => {
                if (setSelectedAgent) {
                  if (agent.id === "default") {
                    setSelectedAgent(null); // Clear selected agent
                  } else {
                    setSelectedAgent({ id: agent.id, name: agent.name, icon: agent.iconNode });
                  }
                }
                setShowAgentSelector(false);
              }}
              className={cn(
                "cursor-pointer px-3 py-2 rounded-md hover:bg-accent",
                // Highlight if this is the default agent and no agent is selected,
                // OR if this agent's ID matches the selected agent's ID.
                ((selectedAgent === null && agent.id === "default") || (selectedAgent?.id === agent.id)) && "bg-muted"
              )}
            >
              <div className="flex items-center">
                {agent.iconNode}
                <div className="flex flex-col">
                  <span className="font-medium">{agent.name}</span>
                  {agent.description && <span className="text-xs text-muted-foreground">{agent.description}</span>}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Document Selector Overlay */}
      {showDocumentSelector && (
        <div className="absolute bottom-[100%] mb-2 left-0 z-50 w-full max-h-60 overflow-y-auto rounded-md border bg-popover p-2 text-sm shadow-md">
          <div className="font-medium mb-1 px-3 text-muted-foreground">
            {projectDocuments.length > 0 ? "Select a Document" : "No Documents in Project"}
          </div>
          {projectDocuments.length > 0 ? (
            projectDocuments.map((doc) => (
              <div
                key={doc.id}
                onClick={() => {
                  // For now, just closes the overlay.
                  // Later, you might want to pass the selected doc to a handler:
                  // if (onDocumentSelect) onDocumentSelect(doc);
                  setShowDocumentSelector(false);
                }}
                className="cursor-pointer px-3 py-2 rounded-md hover:bg-accent"
              >
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-muted-foreground" /> {/* Generic file icon */}
                  <span className="font-medium">{doc.name}</span>
                </div>
              </div>
            ))
          ) : null}
        </div>
      )}


      {/* Message input area - now at the top */}
      <Textarea
        ref={textareaRef}
        className="min-h-[60px] w-full resize-none border-0 p-2 shadow-none focus-visible:ring-0"
        placeholder="Send a message..."
        rows={2}
        value={value}
        onChange={onChange}
        onKeyDown={handleKeyDown}
        {...textareaProps}
      />
      
      {/* File previews */}
      {files.length > 0 && (
        <div className="flex flex-wrap gap-2 p-2 border-t">
          {files.map((file, index) => (
            <FilePreview 
              key={`${file.name}-${index}`} 
              file={file} 
              onRemove={() => removeFile(index)} 
            />
          ))}
        </div>
      )}
      
      {/* Bottom control bar with all dropdowns and buttons */}
      <div className="flex items-center gap-2 mt-2 px-2">
        <div className="flex items-center gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            className={cn(
              "h-8 w-8 px-2 text-sm font-mono",
              showCommands ? "bg-muted text-foreground" : "bg-background"
            )}
            onClick={() => {
              const nextState = !showCommands
              setShowCommands(nextState)
              setFilteredCommands([...commands])
              setSelectedCommandIndex(0)
              if (nextState) {
                textareaRef.current?.focus()
              }
            }}
          >
            /
          </Button>
          {activeCommand && (
            <div className="inline-flex items-center rounded-full bg-muted px-3 py-1 text-xs font-medium text-muted-foreground">
              {activeCommand.icon && typeof activeCommand.icon === "function" ? activeCommand.icon() : activeCommand.icon}
              <span className="ml-1 mr-1">{activeCommand.label}</span>
              <button
                type="button"
                onClick={() => setActiveCommand(null)}
                className="ml-1 text-xs text-muted-foreground hover:text-foreground"
                aria-label="Clear mode"
              >
                ×
              </button>
            </div>
          )}
          {chatSettings && (
            <>
              <ChatSettingsDialog 
                models={chatSettings.models}
                projects={chatSettings.projects}
                documentTypes={chatSettings.documentTypes}
                selectedModel={chatSettings.selectedModel}
                selectedProject={chatSettings.selectedProject}
                selectedDocType={chatSettings.selectedDocType}
                onModelChange={chatSettings.onModelChange}
                onProjectChange={chatSettings.onProjectChange}
                onDocTypeChange={chatSettings.onDocTypeChange}
                isLoading={chatSettings.isLoading}
              />
            </>
          )}
          {/* Agent Selector Trigger */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-8 px-2 flex items-center gap-1 text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            onClick={() => setShowAgentSelector(!showAgentSelector)}
            title="Select Agent"
          >
            {selectedAgent?.icon ? React.cloneElement(selectedAgent.icon as React.ReactElement, { className: "h-4 w-4" }) : <Bot className="h-4 w-4" />}
            {selectedAgent?.name && <span className="text-xs">{selectedAgent.name}</span>}
          </Button>
          {/* Document Selector Trigger */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-8 px-2 flex items-center gap-1 text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            onClick={() => setShowDocumentSelector(!showDocumentSelector)}
            title="Select Document"
            disabled={!projectDocuments || projectDocuments.length === 0} // Disable if no documents
          >
            <FileText className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="ml-auto flex items-center gap-2">
          {allowAttachments && (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                const input = document.createElement("input");
                input.type = "file";
                input.multiple = true;
                input.onchange = (e) => {
                  // Make sure we're handling the event properly
                  if (e.target && (e.target as HTMLInputElement).files) {
                    handleFileChange({ 
                      target: { 
                        files: (e.target as HTMLInputElement).files 
                      } 
                    } as React.ChangeEvent<HTMLInputElement>);
                  }
                };
                input.click();
              }}
            >
              <Paperclip className="h-4 w-4" />
              <span className="sr-only">Attach files</span>
            </Button>
          )}
          
          {isGenerating ? (
            <Button
              type="button"
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={stop}
            >
              <StopCircle className="h-4 w-4" />
              <span className="sr-only">Stop generating</span>
            </Button>
          ) : (
            <Button
              type="submit"
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              disabled={!value.trim()}
              onClick={onSend}
            >
              <SendIcon className="h-4 w-4" />
              <span className="sr-only">Send message</span>
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
