import { ChevronDown } from 'lucide-react'
import { motion } from 'framer-motion'
import { useState } from 'react'
import { cn } from '@/lib/utils'

export function MessageReasoning({ reasoning }: { reasoning: string }) {
  const [open, setOpen] = useState(false)
  return (
    <div className="text-sm">
      <button
        className="flex items-center gap-1 text-muted-foreground"
        onClick={() => setOpen(!open)}
      >
        <ChevronDown className={cn('h-4 w-4 transition-transform', open && 'rotate-180')} />
        <span>Reasoning</span>
      </button>
      <motion.div
        initial={false}
        animate={open ? 'open' : 'closed'}
        variants={{ open: { height: 'auto', opacity: 1 }, closed: { height: 0, opacity: 0 } }}
        className="overflow-hidden text-xs mt-1 whitespace-pre-wrap"
      >
        {reasoning}
      </motion.div>
    </div>
  )
}
