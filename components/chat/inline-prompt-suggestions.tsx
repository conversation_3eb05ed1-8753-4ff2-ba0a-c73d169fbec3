import { useAuth } from "@/components/auth/auth-provider"

interface PromptSuggestionsProps {
  label: string
  append: (message: { role: "user"; content: string }, options?: any) => void
  suggestions: string[]
}

export function PromptSuggestions({
  label,
  append,
  suggestions,
}: PromptSuggestionsProps) {
  const { user } = useAuth();

  const capitalizeName = (name: string) =>
    name.replace(/\b\w/g, char => char.toUpperCase());

  const rawUserName = user?.user_metadata?.full_name || user?.email?.split('@')[0] || "User";
  const userName = capitalizeName(rawUserName);

  return (
    <div className="flex flex-col items-center justify-center h-full text-center">
      <div>
        <h2 className="text-2xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 text-transparent bg-clip-text">Hello, {userName}</h2>
        <p className="text-lg font-mediumtext-gray-700 mt-1 mb-4">How can I help you today?</p>
      </div>
      {/*}
      <div className="w-full bg-gray-100 p-4 rounded-md shadow-sm mb-2">
        <h3 className="font-semibold mb-2">Summary of this content</h3>
        <p className="text-sm text-gray-600 mb-2">
          The document outlines the user flow for "EmotiLog," an application for mood tracking.
        </p>
      </div>  
      */}

      <div className="w-full flex flex-col mt-4 gap-2">
        {suggestions.map((suggestion) => (
          <button
            key={suggestion}
            onClick={() => append({ role: "user", content: suggestion })}
            className="w-full flex items-center gap-2 rounded-md border bg-white px-4 py-2 text-left hover:bg-gray-50 shadow-sm"
          >
            <span className="text-xl">💬</span>
            <span className="text-sm font-medium text-gray-700">{suggestion}</span>
          </button>
        ))}
        {/* 
        <button
          onClick={() => append({ role: "user", content: "Show me more suggestions" })}
          className="w-full text-blue-600 text-sm hover:underline text-left"
        >
          More suggestions →
        </button>
        */}

      </div>
    </div>
  )
}
