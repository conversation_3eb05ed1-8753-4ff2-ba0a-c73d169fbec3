"use client"

import {
  forwardRef,
  useC<PERSON>back,
  useRef,
  useState,
  type ReactElement,
} from "react"
import { useChat } from "ai/react" // or wherever your chat context hook is defined
import { ArrowDown, ThumbsDown, ThumbsUp } from "lucide-react"

import { cn } from "@/lib/utils"
import { useAutoScroll } from "@/hooks/use-auto-scroll"
import { Button } from "@/components/ui/button"
import { type Message } from "@/components/chat/chat-message"
import { CopyButton } from "@/components/ui/copy-button"
import { MessageInput } from "@/components/chat/message-input"
import { MessageList } from "@/components/chat/message-list"
import { PromptSuggestions } from "@/components/chat/prompt-suggestions"
import { TypingIndicator } from "./typing-indicator"

interface ChatPropsBase {
  handleSubmit: (
    event?: { preventDefault?: () => void },
    options?: { experimental_attachments?: FileList }
  ) => void
  messages: Array<Message>
  input: string
  className?: string
  handleInputChange: React.ChangeEventHandler<HTMLTextAreaElement>
  isGenerating: boolean
  stop?: () => void
  onRateResponse?: (
    messageId: string,
    rating: "thumbs-up" | "thumbs-down"
  ) => void
  setMessages?: (messages: any[]) => void
  transcribeAudio?: (blob: Blob) => Promise<string>
  chatSettings?: {
    models: Array<{ id: string, name: string }>
    projects: Array<{ id: string, name: string }>
    documentTypes: Array<{ id: string, title: string }>
    selectedModel: string
    selectedProject: string
    selectedDocType: string
    onModelChange: (value: string) => void
    onProjectChange: (value: string) => void
    onDocTypeChange: (value: string) => void
    isLoading?: boolean
  }
}

interface ChatPropsWithoutSuggestions extends ChatPropsBase {
  append?: never
  suggestions?: never
}

interface ChatPropsWithSuggestions extends ChatPropsBase {
  append: (message: { role: "user"; content: string }) => void
  suggestions: string[]
}

type ChatProps = ChatPropsWithoutSuggestions | ChatPropsWithSuggestions

export function Chat({
  messages,
  handleSubmit,
  input,
  handleInputChange,
  stop,
  isGenerating,
  append,
  suggestions,
  className,
  onRateResponse,
  setMessages,
  transcribeAudio,
  chatSettings, // Add this prop
}: ChatProps) {
  const { streamingMessage } = useChat();
  const lastMessage = messages.at(-1)
  const isEmpty = messages.length === 0
  const isTyping = lastMessage?.role === "user"

  const messagesRef = useRef(messages)
  messagesRef.current = messages

  // Enhanced stop function that marks pending tool calls as cancelled
  const handleStop = useCallback(() => {
    stop?.()

    if (!setMessages) return

    const latestMessages = [...messagesRef.current]
    const lastAssistantMessage = latestMessages.findLast(
      (m) => m.role === "assistant"
    )

    if (!lastAssistantMessage) return

    let needsUpdate = false
    let updatedMessage = { ...lastAssistantMessage }

    if (lastAssistantMessage.toolInvocations) {
      const updatedToolInvocations = lastAssistantMessage.toolInvocations.map(
        (toolInvocation) => {
          if (toolInvocation.state === "call") {
            needsUpdate = true
            return {
              ...toolInvocation,
              state: "result",
              result: {
                content: "Tool execution was cancelled",
                __cancelled: true, // Special marker to indicate cancellation
              },
            } as const
          }
          return toolInvocation
        }
      )

      if (needsUpdate) {
        updatedMessage = {
          ...updatedMessage,
          toolInvocations: updatedToolInvocations,
        }
      }
    }

    if (lastAssistantMessage.parts && lastAssistantMessage.parts.length > 0) {
      const updatedParts = lastAssistantMessage.parts.map((part: any) => {
        if (
          part.type === "tool-invocation" &&
          part.toolInvocation &&
          part.toolInvocation.state === "call"
        ) {
          needsUpdate = true
          return {
            ...part,
            toolInvocation: {
              ...part.toolInvocation,
              state: "result",
              result: {
                content: "Tool execution was cancelled",
                __cancelled: true,
              },
            },
          }
        }
        return part
      })

      if (needsUpdate) {
        updatedMessage = {
          ...updatedMessage,
          parts: updatedParts,
        }
      }
    }

    if (needsUpdate) {
      const messageIndex = latestMessages.findIndex(
        (m) => m.id === lastAssistantMessage.id
      )
      if (messageIndex !== -1) {
        latestMessages[messageIndex] = updatedMessage
        setMessages(latestMessages)
      }
    }
  }, [stop, setMessages, messagesRef])

  const messageOptions = useCallback(
    (message: Message) => ({
      actions: onRateResponse ? (
        <>
          <div className="border-r pr-1">
            <CopyButton
              content={message.content}
              copyMessage="Copied response to clipboard!"
            />
          </div>
          <Button
            size="icon"
            variant="ghost"
            className="h-6 w-6"
            onClick={() => onRateResponse(message.id, "thumbs-up")}
          >
            <ThumbsUp className="h-4 w-4" />
          </Button>
          <Button
            size="icon"
            variant="ghost"
            className="h-6 w-6"
            onClick={() => onRateResponse(message.id, "thumbs-down")}
          >
            <ThumbsDown className="h-4 w-4" />
          </Button>
        </>
      ) : (
        <CopyButton
          content={message.content}
          copyMessage="Copied response to clipboard!"
        />
      ),
    }),
    [onRateResponse]
  )

  const handleFormSubmit = async (
    event: React.FormEvent,
    options?: { experimental_attachments?: FileList }
  ) => {
    event.preventDefault();
    
    // If there are file attachments, process them
    if (options?.experimental_attachments?.length) {
      try {
        console.log("Processing file attachments:", options.experimental_attachments);
        
        // Create an array to hold file contents
        const fileContents: string[] = [];
        
        // Process each file sequentially using async/await
        for (const file of Array.from(options.experimental_attachments)) {
          try {
            console.log(`Reading file: ${file.name} (${file.type})`);
            
            // Read the file content
            const content = await readFileAsText(file);
            console.log(`Successfully read file ${file.name}, content length: ${content.length}`);
            
            // Add the file content to our array
            fileContents.push(`File: ${file.name}\n\n${content}`);
          } catch (error) {
            console.error(`Error reading file ${file.name}:`, error);
            fileContents.push(`File: ${file.name} (Error reading file: ${error.message})`);
          }
        }
        
        // Combine all file contents with the input
        const fileMessage = fileContents.join('\n\n---\n\n');
        const combinedMessage = input 
          ? `${input}\n\n---\n\nAttached files:\n\n${fileMessage}`
          : `I'm attaching these files:\n\n${fileMessage}`;
        
        console.log("Combined message with file contents:", combinedMessage.substring(0, 100) + "...");
        
        // Call the original handleSubmit with the combined message
        handleSubmit({ preventDefault: () => {} }, { message: combinedMessage });
      } catch (error) {
        console.error("Error processing file attachments:", error);
        // If there's an error, just submit the original input
        handleSubmit(event);
      }
    } else {
      // No attachments, just call handleSubmit with the input
      handleSubmit(event);
    }
  };

  // Helper function to read file content as text
  const readFileAsText = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const result = e.target?.result;
          if (typeof result === 'string') {
            resolve(result);
          } else if (result instanceof ArrayBuffer) {
            // Handle binary data by converting to base64
            const base64 = btoa(
              new Uint8Array(result)
                .reduce((data, byte) => data + String.fromCharCode(byte), '')
            );
            resolve(`[Binary data converted to base64, length: ${base64.length} characters]`);
          } else {
            reject(new Error('Unknown result type from FileReader'));
          }
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('Error reading file'));
      
      // Try to read as text first
      try {
        reader.readAsText(file);
      } catch (error) {
        // If reading as text fails, try as array buffer
        try {
          reader.readAsArrayBuffer(file);
        } catch (secondError) {
          reject(new Error(`Failed to read file: ${secondError.message}`));
        }
      }
    });
  };

  return (
    <ChatContainer className={cn("flex flex-col h-full w-full", className)}>
           {isEmpty && append && suggestions ? (
        <PromptSuggestions
          label="Try these prompts ✨"
          append={append}
          suggestions={suggestions}
        />
      ) : null}

      
      {/* This conditional rendering needs to be adjusted if suggestions are not shown, 
          we still want the message list and input form structure.
          Let's always render ChatMessages and ChatForm if not empty.
      */}
      {!isEmpty || messages.length > 0 ? ( // Show if not empty OR if there are messages (covers initial state after suggestions)
        <>
          <ChatMessages messages={messages} className="flex-1 overflow-y-auto px-4 pt-4"> {/* Adjusted padding: removed pb-4 */}
            <MessageList
              messages={messages}
              isTyping={isTyping}
              messageOptions={messageOptions}
            />
            {streamingMessage && (
              <div className="px-4 py-2 text-sm text-muted-foreground animate-pulse">
                {streamingMessage}
              </div>
            )}
          </ChatMessages>
        </>
      ) : null }



      <ChatForm
        className="bg-background px-4 pt-4 pb-[12px] border-t mt-auto" // Specific padding & mt-auto
        isPending={isGenerating || isTyping}
        handleSubmit={handleFormSubmit}
      >
        {({ files, setFiles }) => (
          <MessageInput
            value={input}
            onChange={handleInputChange}
            allowAttachments
            files={files}
            setFiles={setFiles}
            stop={handleStop}
            isGenerating={isGenerating}
            transcribeAudio={transcribeAudio}
            chatSettings={chatSettings}
            onSend={handleSubmit} // Pass the handleSubmit from ChatForm's children
          />
        )}
      </ChatForm>
    </ChatContainer>
  )
}
Chat.displayName = "Chat"

export function ChatMessages({
  messages,
  children,
  className
}: React.PropsWithChildren<{
  messages: Message[]
  className?: string
}>) {
  const {
    containerRef,
    scrollToBottom,
    handleScroll,
    shouldAutoScroll,
    handleTouchStart,
  } = useAutoScroll([messages])

  return (
    <div
      className={cn("grid grid-cols-1 overflow-y-auto pb-4", className)}
      ref={containerRef}
      onScroll={handleScroll}
      onTouchStart={handleTouchStart}
    >
      <div className="max-w-full [grid-column:1/1] [grid-row:1/1]">
        {children}
      </div>

      {!shouldAutoScroll && (
        <div className="pointer-events-none flex flex-1 items-end justify-end [grid-column:1/1] [grid-row:1/1]">
          <div className="sticky bottom-0 left-0 flex w-full justify-end">
            <Button
              onClick={scrollToBottom}
              className="pointer-events-auto h-8 w-8 rounded-full ease-in-out animate-in fade-in-0 slide-in-from-bottom-1"
              size="icon"
              variant="ghost"
            >
              <ArrowDown className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

export const ChatContainer = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("flex flex-col h-full w-full", className)} // Changed to flex-col
      {...props}
    />
  )
})
ChatContainer.displayName = "ChatContainer"

interface ChatFormProps {
  className?: string
  isPending: boolean
  handleSubmit: (
    event?: { preventDefault?: () => void },
    options?: { experimental_attachments?: FileList }
  ) => void
  children: (props: {
    files: File[] | null
    setFiles: React.Dispatch<React.SetStateAction<File[] | null>>
  }) => ReactElement
}

export const ChatForm = forwardRef<HTMLFormElement, ChatFormProps>(
  ({ children, handleSubmit, isPending, className }, ref) => {
    const [files, setFiles] = useState<File[]>([]) // Initialize as empty array

    const onSubmit = (event: React.FormEvent) => {
      event.preventDefault() // Prevent default form submission
      
      if (!files || files.length === 0) {
        handleSubmit(event)
        return
      }

      // Create a FileList from the files array
      const dataTransfer = new DataTransfer()
      for (const file of files) {
        dataTransfer.items.add(file)
      }
      
      // Pass the files to the handleSubmit function
      handleSubmit(event, { experimental_attachments: dataTransfer.files })
      
      // Clear the files after submission
      setFiles([])
    }

    return (
      <form ref={ref} onSubmit={onSubmit} className={className}>
        {children({ files, setFiles })}
      </form>
    )
  }
)
ChatForm.displayName = "ChatForm"

function createFileList(files: File[] | FileList): FileList {
  const dataTransfer = new DataTransfer()
  // Make sure files is iterable
  for (const file of Array.from(files)) {
    dataTransfer.items.add(file)
  }
  return dataTransfer.files
}
