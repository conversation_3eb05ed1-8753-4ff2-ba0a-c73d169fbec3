import React, { useState, useEffect } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ALL_PROJECTS_ITEM_VALUE } from "@/components/chat/ChatProvider"

interface ChatHeaderProps {
  agents: Array<{ id: string; icon: React.ReactNode; name: string }>
  selectedAgent: string
  setSelectedAgent: (val: string) => void
  selectedProject: string
  selectedDocumentId: string
  setSelectedDocumentId: (val: string) => void
  projectDocuments: Array<{ id: string; title: string }>
  isSidebarCollapsed: boolean
  sidebarWidth: number
}

export function ChatHeader({
  agents,
  selectedAgent,
  setSelectedAgent,
  selectedProject,
  selectedDocumentId,
  setSelectedDocumentId,
  projectDocuments,
  isSidebarCollapsed,
  sidebarWidth,
}: ChatHeaderProps) {
  // Calculate header width based on sidebar width.
  // This makes the header adjust its width according to the sidebar state.
  const headerWidth = `calc(100vw - ${sidebarWidth}px)`

  return (
    <div className="flex h-14 items-center justify-between border-b px-4" style={{ width: headerWidth }}>
      <div className="flex items-center gap-4">
        <h2 className="text-base font-semibold">Chat</h2>
      </div>
      <div className="flex items-center gap-2">
        {agents && agents.length > 0 && (
          <Select value={selectedAgent} onValueChange={setSelectedAgent}>
            <SelectTrigger className="h-8 w-[180px] text-sm">
              <SelectValue placeholder="Select agent" />
            </SelectTrigger>
            <SelectContent>
              {agents.map((agent) => (
                <SelectItem key={agent.id} value={agent.id} className="cursor-pointer text-sm">
                  <div className="flex items-center gap-1">
                    <span>{agent.icon}</span>
                    {agent.name}
                  </div>
                </SelectItem>
              ))}
              {!agents.find(a => a.id === "default") && (
                <SelectItem key="default" value="default" className="cursor-pointer text-sm">
                  <div className="flex items-center gap-1">
                    <span>🤖</span>
                    Default Assistant
                  </div>
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        )}
        {selectedProject && selectedProject !== ALL_PROJECTS_ITEM_VALUE && (
          <Select value={selectedDocumentId} onValueChange={setSelectedDocumentId}>
            <SelectTrigger className="h-8 w-[200px] text-sm">
              <SelectValue placeholder="Select document" />
            </SelectTrigger>
            <SelectContent>
              {projectDocuments.map((doc) => (
                <SelectItem key={doc.id} value={doc.id} className="text-sm">
                  {doc.title || `Document ${doc.id.substring(0,6)}...`}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  )
}