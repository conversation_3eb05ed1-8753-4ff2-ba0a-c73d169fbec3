import { ChatMessage } from './chat-message'
import { Loader2 } from 'lucide-react'
import type { UIMessage } from 'ai'
import { cn } from '@/lib/utils'

export { ChatMessage as Message } from './chat-message'

export interface PreviewMessageProps {
  chatId: string
  message: UIMessage
  isLoading?: boolean
  vote?: any
  setMessages?: (fn: (msgs: UIMessage[]) => UIMessage[]) => void
  reload?: () => void
  isReadonly?: boolean
  requiresScrollPadding?: boolean
}

export function PreviewMessage({ message }: PreviewMessageProps) {
  return <ChatMessage {...message} />
}

export function ThinkingMessage({ className }: { className?: string }) {
  return (
    <div className={cn('flex items-center gap-2 text-xs text-muted-foreground', className)}>
      <Loader2 className="h-4 w-4 animate-spin" />
      <span>Thinking...</span>
    </div>
  )
}
