import { ClipboardCopy, ThumbsUp, ThumbsDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export interface MessageActionsProps {
  onCopy?: () => void
  onVote?: (vote: 'up' | 'down') => void
  className?: string
}

export function MessageActions({ onCopy, onVote, className }: MessageActionsProps) {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      {onCopy && (
        <Button size="icon" variant="ghost" onClick={onCopy}>
          <ClipboardCopy className="h-4 w-4" />
        </Button>
      )}
      {onVote && (
        <>
          <Button size="icon" variant="ghost" onClick={() => onVote('up')}>
            <ThumbsUp className="h-4 w-4" />
          </Button>
          <Button size="icon" variant="ghost" onClick={() => onVote('down')}>
            <ThumbsDown className="h-4 w-4" />
          </Button>
        </>
      )}
    </div>
  )
}
