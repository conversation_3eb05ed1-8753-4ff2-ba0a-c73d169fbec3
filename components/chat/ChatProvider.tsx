'use client'

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react'
import { useChat as useVercelChat, type Message } from "ai/react"
import { useAuth } from "@/components/auth/auth-provider"
import { useRouter, usePathname } from "next/navigation"
import { toast } from "@/hooks/use-toast"
import {
  createChatSession,
  getChatMessages,
  saveChatMessage,
  updateChatSessionTitle
} from "@/lib/chat-service"
import { supabase } from "@/lib/supabase-client"
import { agents } from "@/lib/chat/agents"
import { processDataStream, type DataStreamDelta } from "../data-stream-handler"
import { artifactDefinitions } from "../artifact"
import { useArtifact } from "@/hooks/use-artifact"

// Define available models
const MODELS = [
  { id: "google/gemini-2.5-flash-preview", name: "Gemini 2.5 Flash"},
  { id: "openai/gpt-4o", name: "GPT-4o" },
  { id: "openai/gpt-4-turbo", name: "GPT-4 Turbo" },
  { id: "openai/gpt-3.5-turbo", name: "GPT-3.5 Turbo" },
  { id: "anthropic/claude-3-5-sonnet", name: "Claude 3.5 Sonnet" },
  { id: "anthropic/claude-3-opus", name: "Claude 3 Opus" },
  { id: "anthropic/claude-3-haiku", name: "Claude 3 Haiku" },
  { id: "google/gemini-1.5-flash", name: "Gemini 1.5 Flash" },
  { id: "google/gemini-1.5-pro", name: "Gemini 1.5 Pro" },
  { id: "google/gemini-2.0-flash-exp:free", name: "Gemini 2.0 Flash" },

]

// Define a unique, non-empty value for the "All Projects" SelectItem
export const ALL_PROJECTS_ITEM_VALUE = "__ALL_PROJECTS__";

type ChatContextType = {
  sessionId: string | null
  messages: Message[]
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void
  chatLoading: boolean
  stop: () => void
  append: (message: { role: "user"; content: string }, options?: any) => void
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>
  selectedModel: string
  setSelectedModel: React.Dispatch<React.SetStateAction<string>>
  selectedProject: string
  setSelectedProject: React.Dispatch<React.SetStateAction<string>>
  selectedDocType: string
  setSelectedDocType: React.Dispatch<React.SetStateAction<string>>
  projects: Array<{ id: string, name: string }>
  documentTypes: Array<{ id: string, title: string }>
  isLoading: boolean
  selectedAgent: string
  setSelectedAgent: React.Dispatch<React.SetStateAction<string>>
  agents: Array<{ id: string, name: string, description: string, icon: string }>
  selectedDocumentId: string
  setSelectedDocumentId: React.Dispatch<React.SetStateAction<string>>
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { artifact, setArtifact, setMetadata } = useArtifact()
  
  // Extract sessionId from pathname if it exists
  const pathSessionId = pathname.startsWith('/dashboard/chat/') && pathname !== '/dashboard/chat/'
    ? pathname.replace('/dashboard/chat/', '') 
    : null
  
  const [sessionId, setSessionId] = useState<string | null>(pathSessionId)
  // Ref to keep track of latest sessionId to avoid stale closure in async callbacks
  const sessionIdRef = useRef<string | null>(sessionId);
  // Keep sessionIdRef in sync with sessionId state
  useEffect(() => {
    sessionIdRef.current = sessionId;
  }, [sessionId]);
  const [selectedModel, setSelectedModel] = useState<string>(MODELS[0].id)
  const [selectedProject, setSelectedProject] = useState<string>("") // Default to no project selected
  const [selectedDocType, setSelectedDocType] = useState<string>("all") // Assuming "all" is a valid state for doc types
  const [projects, setProjects] = useState<Array<{ id: string, name: string }>>([
    { id: ALL_PROJECTS_ITEM_VALUE, name: "All Projects" } // Use non-empty value
  ])
  const [documentTypes, setDocumentTypes] = useState<Array<{ id: string, title: string }>>([
    { id: "all", title: "All Document Types" } // Consistent naming
  ])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedAgent, setSelectedAgent] = useState<string>("default")
  const [availableAgents, setAvailableAgents] = useState<Array<{ id: string, name: string, description: string, icon: string }>>([
    { id: "default", name: "Default Assistant", description: "General purpose assistant", icon: "🤖" }
  ])
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>("");

  // Load agents from database
  useEffect(() => {
    const fetchAgents = async () => {
      if (!user?.id) return;
      
      try {
        const { data: agentsData, error } = await supabase
          .from("chat_agents")
          .select("id, name, description, icon")
          .eq("active", true)
          .order("name", { ascending: true });
          
        if (error) throw error;
        
        // Set agents with "Default Assistant" as first option
        setAvailableAgents([
          { id: "default", name: "Default Assistant", description: "General purpose assistant", icon: "🤖" },
          ...(agentsData || [])
        ]);
      } catch (error) {
        console.error("Error fetching agents:", error);
      }
    };
    
    fetchAgents();
  }, [user?.id]);

  // Streaming message state
  const [streamingMessage, setStreamingMessage] = useState<string | null>(null);

  // Helper to get the project ID for API calls
  const getApiProjectId = useCallback((currentSelectedProject: string) => {
    if (currentSelectedProject && currentSelectedProject !== ALL_PROJECTS_ITEM_VALUE) {
      return currentSelectedProject;
    }
    return undefined; // Or null, depending on backend expectation for "no project"
  }, []);

  // Configure useChat hook - renamed to useVercelChat to avoid circular dependency
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit: aiHandleSubmit,
    stop,
    append: aiAppend,
    setMessages
  } = useVercelChat({
    api: '/api/chat',
    headers: {
      'x-user-id': user?.id || '',
    },
    body: {
      model: selectedModel, // This will be overridden by handleSubmit/append options
      projectId: getApiProjectId(selectedProject),
      documentType: selectedDocType !== "all" ? selectedDocType : undefined,
      agentId: (selectedAgent && selectedAgent !== "default") ? selectedAgent : undefined,
      sessionId: sessionId,
      documentId: selectedDocumentId || undefined, // Pass undefined if selectedDocumentId is ""
    },
    onUpdate: (partial) => {
      if (partial.role === 'assistant') {
        setStreamingMessage(prev => (prev || '') + partial.content);
      }
    },
    onFinish: async (message) => {
      const activeSessionId = sessionIdRef.current;
      const userId = user?.id;

      if (!activeSessionId || !userId || message.role !== 'assistant' || !message.content?.trim()) {
        console.warn("[ChatProvider] Skipping save in onFinish", {
          activeSessionId,
          userId,
          role: message.role,
          content: message.content,
          selectedAgent,
          sessionIdRef: sessionIdRef.current,
          sessionState: sessionId,
        });
        setStreamingMessage(null);
        return;
      }

      // Determine type based on message type
      let type: "normal" | "system" | null = "normal";
      if (message.type === "system") {
        type = "system";
      }

      // Find if this is the first assistant message after /project-wizard
      let agent_id = selectedAgent !== "default" ? selectedAgent : null;
      if (messages.length > 0) {
        // Find the last user message
        const lastUserMsgIdx = [...messages].reverse().findIndex(m => m.role === "user");
        if (lastUserMsgIdx !== -1) {
          const userMsgIdx = messages.length - 1 - lastUserMsgIdx;
          const lastUserMsg = messages[userMsgIdx];
          // If the last user message was /project-wizard and this is the next assistant message,
          // then ensure agent_id is set to selectedAgent
          if (
            lastUserMsg.content?.trim() === "/project-wizard"
            // Optionally: you could check that this is the first assistant after that
          ) {
            // If this is the first assistant message after /project-wizard
            const assistantMsgsAfter = messages.slice(userMsgIdx + 1).filter(m => m.role === "assistant");
            if (assistantMsgsAfter.length === 0) {
              agent_id = selectedAgent !== "default" ? selectedAgent : null;
            }
          }
        }
      }

      const payload = {
        session_id: activeSessionId,
        user_id: userId,
        content: message.content,
        role: "assistant",
        type,
        agent_id,
        project_id: getApiProjectId(selectedProject) || null
      };

      console.log("[ChatProvider] Attempting to save assistant message via onFinish:", payload);

      try {
        const { error } = await supabase.from("chat_messages").insert(payload);
        if (error) {
          console.error("Failed to insert assistant message:", error);
        } else {
          console.log("Assistant message inserted successfully via onFinish");
        }
      } catch (error) {
        console.error("Exception during assistant message insert:", error);
      }

      // If the message indicates a project was created, update selectedProject and show toast
      if (message.action_type === "project_created" && message.action_payload?.projectId) {
        setSelectedProject(message.action_payload.projectId);
        toast({
          title: "Project Created",
          description: `✅ Project created successfully.`,
          variant: "default"
        });
      }

      setStreamingMessage(null);
    },
    onError: (err) => {
      console.error("Chat error:", err);
      toast({ title: "Chat Error", description: err.message, variant: "destructive" })
    },
  });
  
  // isLoading from useVercelChat is chatLoading
  // isLoading in ChatContext is for initial data load (projects, doc types, chat history)
  // Renaming to avoid confusion if useVercelChat's isLoading is directly used.
  const { isLoading: vercelChatIsLoading } = useVercelChat();

  // Load chat based on sessionId from URL
  useEffect(() => {
    const loadChat = async () => {
      if (!user?.id) return;

      console.log(`[ChatProvider] Loading chat. Path session ID: ${pathSessionId}`);

      if (pathSessionId) {
        console.log(`[ChatProvider] Existing session ID found: ${pathSessionId}. Fetching messages...`);
        setSessionId(pathSessionId);
        // Fetch messages for this session
        try {
          console.log(`[ChatProvider] Calling getChatMessages for user ${user.id}, session ${pathSessionId}`);
          const messagesResponse = await getChatMessages(user.id, pathSessionId, 1000);
          console.log(`[ChatProvider] Fetched ${messagesResponse.data.length} messages for session ${pathSessionId}.`);
          const formattedMessages: Message[] = messagesResponse.data.map(m => ({
            id: m.id,
            role: m.role,
            content: m.content,
            createdAt: new Date(m.created_at),
            type: m.type || "normal",
            action_type: m.action_type,
            action_payload: m.action_payload
          }));
          setMessages(formattedMessages);
        } catch (error) {
          console.error("Error fetching chat messages:", error);
          toast({
            title: "Error",
            description: "Failed to load chat history.",
            variant: "destructive",
          });
        }
      } else {
        // No session ID - this is a new chat
        console.log("[ChatProvider] No session ID in path. Initializing new chat state.");
        setSessionId(null);
        setMessages([]); // Ensure message list is empty for new chat
      }
      
      setIsLoading(false);
    };

    loadChat();
  }, [user?.id, pathSessionId, setMessages]);

  // Wrapper for handleSubmit to create session on first message (fully mimic ChatGPT lazy session creation)
    const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e && e.preventDefault) {
      e.preventDefault()
    }

    // Add debug logging
    console.log("[ChatProvider] Submitting with project ID:", getApiProjectId(selectedProject) || "none");
    console.log("[ChatProvider] Submitting with documentId:", selectedDocumentId || "none");

    if (!user?.id) {
      toast({ title: "Error", description: "User not authenticated", variant: "destructive" });
      return;
    }

    let currentSessionId = sessionId;
    const userMessage = input.trim(); // Get the user's message
    if (!userMessage) {
      console.log("[ChatProvider] Empty message, not submitting");
      return;
    }

    // If there's no sessionId, create one and wait before sending/appending the user message
    if (!sessionId) {
      try {
        console.log("[ChatProvider] No active session ID. Creating new session...");
        const newSession = await createChatSession(user.id);
        const newSessionId = newSession.id;
        setSessionId(newSessionId);
        sessionIdRef.current = newSessionId;
        currentSessionId = newSessionId;

        // Redirect to new chat URL (shallow to avoid remount)
        router.replace(`/dashboard/chat/${newSessionId}`, { scroll: false });

        // Add a small delay to ensure session state propagation before streaming
        await new Promise(res => setTimeout(res, 50));

        // Now proceed with message submission
        console.log(`[ChatProvider] Session created. Proceeding with message: "${userMessage}"`);

        // Use Vercel's hook to send the message now
        aiHandleSubmit(e, {
          options: {
            body: {
              sessionId: newSessionId,
              model: selectedModel,
              projectId: getApiProjectId(selectedProject),
              documentType: selectedDocType !== "all" ? selectedDocType : undefined,
              agentId: (selectedAgent && selectedAgent !== "default") ? selectedAgent : undefined,
              documentId: selectedDocumentId || undefined,
            }
          }
        });

        return; // Exit early to prevent duplicate handling
      } catch (error) {
        console.error("Error creating chat session:", error);
        toast({ 
          title: "Error", 
          description: "Failed to create a new chat session", 
          variant: "destructive" 
        });
        return;
      }
    }

    // No longer handle project creation confirmation logic here
    
    // For subsequent messages, use the normal flow
    console.log(`[ChatProvider] Submitting message for existing session ID: ${currentSessionId}`);
    aiHandleSubmit(e, {
      options: {
        body: {
          sessionId: currentSessionId,
          model: selectedModel,
          projectId: getApiProjectId(selectedProject),
          documentType: selectedDocType !== "all" ? selectedDocType : undefined,
          agentId: (selectedAgent && selectedAgent !== "default") ? selectedAgent : undefined,
          documentId: selectedDocumentId || undefined, 
        }
      }
    });
  }, [
    sessionId, 
    input, 
    user?.id, 
    selectedModel, 
    selectedProject, 
    selectedDocType, 
    router, 
    aiAppend, 
    // handleInputChange, // Not directly used in this callback's logic but part of closure
    // setMessages, // Not directly used in this callback's logic but part of closure
    aiHandleSubmit,
    selectedAgent,
    selectedDocumentId,
    getApiProjectId,
    handleInputChange,
    setMessages,
  ]);

  // Wrap the append function to handle session creation
  const append = useCallback(async (message: { role: "user"; content: string }, options?: any) => {
    if (!user?.id) {
      toast({ title: "Error", description: "User not authenticated", variant: "destructive" });
      return;
    }

    const sendMessageWithStream = async (userMessage: string, sessionId: string) => {
      setStreamingMessage(""); // Reset

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-id': user?.id || '',
        },
        body: JSON.stringify({
          sessionId,
          model: selectedModel,
          projectId: getApiProjectId(selectedProject),
          documentType: selectedDocType !== "all" ? selectedDocType : undefined,
          agentId: (selectedAgent && selectedAgent !== "default") ? selectedAgent : undefined, 
          documentId: selectedDocumentId || undefined,
          messages: [
            ...messages.map(m => ({ role: m.role, content: m.content })),
            { role: 'user', content: userMessage }
          ]
        })
      });

      if (!response.ok || !response.body) {
        console.error("Failed to stream response");
        return;
      }

      const reader = response.body.getReader();
      let fullResponse = "";

      await processDataStream(reader, (delta: DataStreamDelta) => {
        if (delta.type === 'text-delta' && typeof delta.content === 'string') {
          const text = delta.content as string;
          fullResponse += text;
          setStreamingMessage(prev => (prev || '') + text);
        } else {
          const def = artifactDefinitions.find(d => d.kind === artifact.kind);
          if (def) {
            def.onStreamPart({
              streamPart: delta,
              setArtifact,
              setMetadata,
            });
          }
        }
      });

      const assistantMsg: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: fullResponse,
        createdAt: new Date(),
        type: "normal",
        action_type: null,
        action_payload: null
      };
      setMessages(prev => [...prev, assistantMsg]);
      setStreamingMessage(null);
      // --- Insert streamed assistant message into Supabase manually ---
      try {
        const { error } = await supabase.from("chat_messages").insert({
          session_id: sessionIdRef.current,
          user_id: user?.id,
          content: fullResponse,
          role: "assistant",
          type: "normal",
          agent_id: selectedAgent !== "default" ? selectedAgent : null,
          project_id: getApiProjectId(selectedProject) || null
        });

        if (error) {
          console.error("[Manual Stream] Failed to insert assistant message:", error);
        } else {
          console.log("[Manual Stream] Assistant message inserted via stream");
        }
      } catch (err) {
        console.error("[Manual Stream] Insert exception:", err);
      }
      // --- End manual Supabase insert ---
    };

    let currentSessionId = sessionId;

    // If no session exists yet, create one first
    if (!currentSessionId) {
      try {
        console.log("[ChatProvider] No active session ID. Creating new session for suggestion...");
        const newSession = await createChatSession(user.id);
        currentSessionId = newSession.id;
        console.log(`[ChatProvider] New session created with ID: ${currentSessionId} for suggestion.`);
        setSessionId(currentSessionId);
        
        // Update URL to the new path-based format
        router.replace(`/dashboard/chat/${currentSessionId}`, { scroll: false });
        
        // Give state and URL a moment to update
        await new Promise(resolve => setTimeout(resolve, 0));
      } catch (error) {
        console.error("Error creating chat session for suggestion:", error);
        toast({ 
          title: "Error", 
          description: "Failed to create a new chat session", 
          variant: "destructive" 
        });
        return;
      }
    }

    // Log documentId before appending message
    console.log("[ChatProvider] Appending message with documentId:", selectedDocumentId || "none");

    // Now append the message with the session ID and agent ID
    aiAppend(message, {
      options: {
        body: {
          sessionId: currentSessionId,
          model: selectedModel,
          projectId: getApiProjectId(selectedProject),
          documentType: selectedDocType !== "all" ? selectedDocType : undefined,
          agentId: (selectedAgent && selectedAgent !== "default") ? selectedAgent : undefined,
          documentId: selectedDocumentId || undefined,
        },
        ...options?.options
      },
      ...options
    });
  }, [sessionId, user?.id, selectedModel, selectedProject, selectedDocType, router, aiAppend, selectedAgent, selectedDocumentId]);

  // Load projects and document types
  useEffect(() => {
    const fetchProjectsAndDocTypes = async () => {
      if (!user?.id) {
        setIsLoading(false);
        return;
      }

      try {
        // Fetch projects from Supabase
        const { data: projectsData, error: projectsError } = await supabase
          .from("projects")
          .select("id, name")
          .eq("user_id", user.id)
          .order("name", { ascending: true });

        if (projectsError) throw projectsError;
        
        setProjects([
          { id: ALL_PROJECTS_ITEM_VALUE, name: "All Projects" },
          ...(projectsData || []).map(p => ({ id: p.id, name: p.name }))
        ]);

        // Fetch document types from Supabase
        const { data: docTypesData, error: docTypesError } = await supabase
          .from("document_types")
          .select("id, title")
          .order("title", { ascending: true });

        if (docTypesError) throw docTypesError;
        
        // Set document types with "All Documents" as first option
        setDocumentTypes([ // Assuming "all" is a meaningful filter for document types
          { id: "all", title: "All Documents" },
          ...(docTypesData || [])
        ]);
      } catch (error) {
        console.error("Error fetching projects or document types:", error);
        toast({ 
          title: "Error", 
          description: "Failed to load projects or document types", 
          variant: "destructive" 
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectsAndDocTypes();
  }, [user?.id, toast]);

    // Add this to the existing useEffect that loads agents
    useEffect(() => {
      // Define the global trigger function for the project wizard
      if (typeof window !== "undefined") {
        window.__triggerProjectWizardAgent = async () => {
          if (!user?.id) return;

          let currentSessionId = sessionId;
          if (!currentSessionId) {
            try {
              const newSession = await createChatSession(user.id);
              currentSessionId = newSession.id;
              setSessionId(currentSessionId);
              router.replace(`/dashboard/chat/${newSession.id}`, { scroll: false });
            } catch (err) {
              console.error("Failed to create chat session for wizard:", err);
              return;
            }
          }

          setSelectedAgent("project_creator_agent");

          // Trigger agent with a simulated user message (optional)
          append({ role: "user", content: "/project-wizard" });
        };
      }

      return () => {
        // Clean up the global function when component unmounts
        if (typeof window !== "undefined") {
          delete window.__triggerProjectWizardAgent;
        }
      };
    }, [append, setSelectedAgent, user?.id, sessionId, router, setSessionId]);

  const value = {
    sessionId,
    messages,
    input,
    handleInputChange,
    handleSubmit,
    chatLoading: vercelChatIsLoading, // Use the renamed isLoading from useVercelChat
    stop,
    append,
    setMessages,
    selectedModel,
    setSelectedModel,
    selectedProject,
    setSelectedProject,
    selectedDocType,
    setSelectedDocType,
    projects,
    documentTypes,
    isLoading,
    selectedAgent,
    setSelectedAgent,
    agents: availableAgents,
    selectedDocumentId,
    setSelectedDocumentId,
    streamingMessage,
    setStreamingMessage,
  }

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>
}

export const useChat = () => {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}
