"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import type { MessageInputProps } from "./message-input"

export function CreateProjectWizard({
  onCancel,
  onComplete,
  chatSettings,
}: {
  onCancel: () => void
  onComplete: (projectId: string) => void
  chatSettings: MessageInputProps["chatSettings"]
}) {
  const [step, setStep] = useState<"confirm" | "questions" | "done">("confirm")
  const [data, setData] = useState<{ name?: string; goal?: string }>({})

  return (
    <>
      {step === "confirm" && (
        <div className="mb-2 rounded-md bg-muted p-3 text-sm text-muted-foreground">
          Create a new project from this chat session?
          <div className="mt-2 flex gap-2">
            <Button size="sm" onClick={() => setStep("questions")}>Yes</Button>
            <Button size="sm" variant="outline" onClick={onCancel}>No</Button>
          </div>
        </div>
      )}

      {step === "questions" && (
        <div className="mb-2 rounded-md bg-muted p-3 text-sm text-muted-foreground">
          <div className="mb-2">What is the project name?</div>
          <input
            type="text"
            className="w-full rounded-md border px-2 py-1 text-sm"
            value={data.name || ""}
            onChange={(e) => setData({ ...data, name: e.target.value })}
            placeholder="e.g. AI marketing assistant"
          />
          <div className="mt-2">What is the goal of this project?</div>
          <input
            type="text"
            className="w-full rounded-md border px-2 py-1 text-sm mt-1"
            value={data.goal || ""}
            onChange={(e) => setData({ ...data, goal: e.target.value })}
            placeholder="e.g. Help marketers generate campaign briefs"
          />
          <div className="mt-3 flex gap-2">
            <Button size="sm" onClick={() => setStep("done")}>Continue</Button>
          </div>
        </div>
      )}

      {step === "done" && (
        <div className="mb-2 rounded-md bg-muted p-3 text-sm text-muted-foreground">
          <div className="font-medium mb-2">Project Summary:</div>
          <ul className="list-disc pl-4">
            <li><strong>Name:</strong> {data.name}</li>
            <li><strong>Goal:</strong> {data.goal}</li>
          </ul>
          <div className="mt-3 flex gap-2">
            <Button
              size="sm"
              onClick={async () => {
                const supabase = createClientComponentClient()
                const { data: session } = await supabase.auth.getSession()
                const userId = session?.session?.user?.id
                if (!userId) return

                const { data: project } = await supabase
                  .from("projects")
                  .insert([{ title: data.name || "Untitled", goal: data.goal, user_id: userId }])
                  .select()
                  .single()

                if (project?.id) {
                  onComplete(project.id)
                }
              }}
            >
              Confirm & Create
            </Button>
            <Button size="sm" variant="outline" onClick={() => setStep("questions")}>Edit</Button>
          </div>
        </div>
      )}
    </>
  )
}