import { Message, type ChatMessageProps } from './chat-message'

export interface MessagesProps {
  messages: ChatMessageProps[]
  onAction?: (action: { type: string; payload: any }) => void
}

export function Messages({ messages, onAction }: MessagesProps) {
  return (
    <div className="space-y-6 py-8">
      {messages.map(msg => (
        <Message key={msg.id} {...msg} onAction={onAction} />
      ))}
    </div>
  )
}
