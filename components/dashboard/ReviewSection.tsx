// src/components/dashboard/ReviewSection.tsx (or similar path)
import React from 'react';
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Edit2, ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils"; // Assuming you have this utility

interface ReviewSectionProps {
  title: string;
  stepToEdit: number;
  navigateToStep: (step: number) => void;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  children: React.ReactNode;
  className?: string;
}

export const ReviewSection: React.FC<ReviewSectionProps> = ({
  title,
  stepToEdit,
  navigateToStep,
  isOpen,
  onOpenChange,
  children,
  className,
}) => {
  const handleEditClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation(); // Prevent collapsible trigger when clicking edit
    navigateToStep(stepToEdit);
  };

  return (
    <Collapsible open={isOpen} onOpenChange={onOpenChange} className={cn("border rounded-lg overflow-hidden transition-all", className)}>
      <CollapsibleTrigger asChild>
        {/* Make the entire header clickable */}
        <div className={cn(
          "flex justify-between items-center p-3 cursor-pointer hover:bg-muted/50 transition-colors",
          !isOpen && "rounded-b-lg" // Keep bottom rounded when closed
        )}>
          <h3 className="text-sm font-medium text-foreground">{title}</h3>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon" // Use icon size for compactness
              className="h-7 w-7 rounded-full text-muted-foreground hover:text-foreground hover:bg-accent" // Styling for edit button
              onClick={handleEditClick}
              aria-label={`Edit ${title}`}
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-full text-muted-foreground hover:text-foreground hover:bg-accent" // Styling for chevron
              aria-label={isOpen ? "Collapse" : "Expand"}
            >
              {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CollapsibleTrigger>
      {/* Add padding to content */}
      <CollapsibleContent className="p-4 pt-2 border-t border-border/60 bg-background">
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
};