import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation"; // Added useRouter
import { useAuth } from "@/components/auth/auth-provider";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { supabase } from "@/lib/supabase-client";
import {
  Sparkles,
  FileCode,
  FileText,
  Briefcase,
  Layers,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronDown,
  Shield,
  Zap,
  User,
  ChevronRight,
  ChevronLeft,
  Home,
  MessageSquare,
  PlusCircle,
  Loader2,
  LayoutTemplate,
  Trash2, // Added
  Edit3,  // Added
  Check,  // Added
  X as CancelIcon, // Added
  ArrowLeft,
  ArrowRight,
  CirclePlus,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/hooks/use-toast";
import { deleteChatSession, updateChatSessionTitle } from "@/lib/chat-service"; // Added
import { Input } from "@/components/ui/input"; // Added for rename input
import { createNewProject } from "@/lib/store/project"; // Import the project creation function

// -----------------------------------------------------------------------------
// Props & Types
// -----------------------------------------------------------------------------
interface SidebarProps {
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  onCollapseChange?: (isCollapsed: boolean) => void;
}

type Document = {
  id: string;
  title: string;
  type: string;
};

type Project = {
  id: string;
  name: string;
  href: string;
  documents: Document[];
};

// Type for Chat Sessions
type ChatSession = {
  id: string;
  title: string | null; // Changed from 'name' to 'title'
};

// -----------------------------------------------------------------------------
// Component
// -----------------------------------------------------------------------------
export function DashboardSidebar({ onCollapseChange, className = "" }: SidebarProps) {
  // --- Hooks -----------------------------------------------------------------
  const { user, logout } = useAuth();
  const pathname = usePathname();
  const router = useRouter(); // Keep if needed for other actions
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const [userTier, setUserTier] = useState<string>(user?.subscription || "free");
  const [projectsOpen, setProjectsOpen] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [expandedProjects, setExpandedProjects] = useState<Record<string, boolean>>({});
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null); // Added
  const [newTitle, setNewTitle] = useState(''); // Added
  const [isCreatingProject, setIsCreatingProject] = useState(false); // Added

  // Update the hover handlers
  const handleMouseEnter = () => {
    setIsHovered(true);
  };
  
  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Update the visual expansion logic
  const isVisuallyExpanded = !isCollapsed || isHovered;

  // --- Chat State ---
  const [isChatOpen, setIsChatOpen] = useState(false); // State for chat dropdown
  // Initialize with dummy data
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([
    { id: 'dummy-1', title: 'Product Strategy Discussion' },
    { id: 'dummy-2', title: 'Feature Prioritization Ideas' },
    { id: 'dummy-3', title: null }, // Example of a chat without a title yet
  ]);
  const [isLoadingChatSessions, setIsLoadingChatSessions] = useState(false); // False for dummy data
  // Keep creation state if you plan to add a "New Chat" button later
  // const [isCreatingChatSession, setIsCreatingChatSession] = useState(false);

  // --- Event Handlers --------------------------------------------------------
  const toggleMobileMenu = () => setIsMobileMenuOpen((open) => !open);
  const closeMobileMenu = () => setIsMobileMenuOpen(false);
  const toggleProjects = () => setProjectsOpen(prev => !prev);
  const toggleChat = () => setIsChatOpen(prev => !prev); // Handler for chat dropdown
  
  // Add the missing toggleCollapse function
  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    localStorage.setItem("sidebarCollapsed", String(newState));
  };

  const toggleProjectExpansion = (projectId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setExpandedProjects(prev => ({
      ...prev,
      [projectId]: !prev[projectId]
    }));
  };

  // --- Fetch user tier --- (Keep existing logic)
  useEffect(() => {
    const fetchUserTier = async () => { /* ... */ };
    fetchUserTier();
  }, [user?.id]);

  // --- Collapse logic ---
  useEffect(() => {
    const saved = localStorage.getItem("sidebarCollapsed");
    if (saved !== null) {
      const newState = saved === "true";
      setIsCollapsed(newState);
      // Don't call onCollapseChange here
    }
  }, []);

  // Separate effect to notify parent of collapse changes
  useEffect(() => {
    // Skip the initial render
    const timeoutId = setTimeout(() => {
      if (onCollapseChange) {
        onCollapseChange(isCollapsed);
      }
    }, 0);
    
    return () => clearTimeout(timeoutId);
  }, [isCollapsed, onCollapseChange]);

  // --- Fetch projects ---
  useEffect(() => {
    const fetchProjects = async () => {
      if (!user?.id) return;
      
      setIsLoadingProjects(true);
      try {
        // For test user or preview, use mock data
        if (user.id === "test_user_id" || process.env.NEXT_PUBLIC_VERCEL_ENV === "preview") {
          // Mock data for testing
          const mockProjects = [
            { 
              id: "mock-project-1", 
              name: "Sample Project", 
              href: "/dashboard/project/mock-project-1", // Updated to use /project/ instead of /projects/
              documents: [
                { id: "doc-1", title: "Document 1", type: "document" },
                { id: "doc-2", title: "Document 2", type: "document" }
              ]
            },
            { 
              id: "mock-project-2", 
              name: "Another Project", 
              href: "/dashboard/project/mock-project-2", // Updated to use /project/ instead of /projects/
              documents: [
                { id: "doc-3", title: "Document 3", type: "document" }
              ]
            }
          ];
          setProjects(mockProjects);
          setIsLoadingProjects(false);
          return;
        }

        // Validate UUID format
        const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id);
        if (!isValidUUID) {
          console.error("Invalid UUID format:", user.id);
          setProjects([]);
          setIsLoadingProjects(false);
          return;
        }

        // Fetch projects from Supabase
        const { data: projectsData, error: projectsError } = await supabase
          .from("projects")
          .select("id, name, created_at")
          .eq("user_id", user.id)
          .order("created_at", { ascending: false });

        if (projectsError) {
          console.error("Error fetching projects:", projectsError);
          setProjects([]);
          return;
        }

        // Fetch documents for each project
        const projectsWithDocs = await Promise.all(
          (projectsData || []).map(async (project) => {
            const { data: docsData, error: docsError } = await supabase
              .from("project_documents")
              .select("id, title, type")
              .eq("project_id", project.id)
              .order("created_at", { ascending: false });

            return {
              ...project,
              href: `/dashboard/project/${project.id}`, // Updated to use /project/ instead of /projects/
              documents: docsError ? [] : docsData || []
            };
          })
        );
        
        setProjects(projectsWithDocs);
      } catch (err) {
        console.error("Error fetching projects for sidebar:", err);
      } finally {
        setIsLoadingProjects(false);
      }
    };
    
    fetchProjects();
  }, [user?.id]);

  // --- Fetch chat sessions ---
  useEffect(() => {
    const fetchChatSessions = async () => {
      if (!user?.id) return;
      
      setIsLoadingChatSessions(true);
      try {
        // For test user or preview, use mock data
        if (user.id === "test_user_id" || process.env.NEXT_PUBLIC_VERCEL_ENV === "preview") {
          // Mock data for testing
          const mockSessions = [
            { id: 'mock-chat-1', title: 'Product Strategy Discussion' },
            { id: 'mock-chat-2', title: 'Feature Prioritization Ideas' },
            { id: 'mock-chat-3', title: null }, // Example of a chat without a title yet
          ];
          setChatSessions(mockSessions);
          setIsLoadingChatSessions(false);
          return;
        }

        // Validate UUID format
        const isValidUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(user.id);
        if (!isValidUUID) {
          console.error("Invalid UUID format for chat sessions:", user.id);
          setChatSessions([]);
          setIsLoadingChatSessions(false);
          return;
        }

        // Fetch chat sessions from Supabase
        // Changed 'name' to 'title' to match the actual column name
        const { data: sessionsData, error: sessionsError } = await supabase
          .from("chat_sessions")
          .select("id, title, created_at")
          .eq("user_id", user.id)
          .order("created_at", { ascending: false });

        if (sessionsError) {
          console.error("Error fetching chat sessions:", sessionsError);
          setChatSessions([]);
          return;
        }

        setChatSessions(sessionsData || []);
      } catch (err) {
        console.error("Error fetching chat sessions for sidebar:", err);
      } finally {
        setIsLoadingChatSessions(false);
      }
    };
    
    fetchChatSessions();
  }, [user?.id]);


  // --- Chat Action Handlers ---
  const handleDeleteChat = async (idToDelete: string) => {
    if (!user?.id) return;
    if (window.confirm('Are you sure you want to delete this chat and all its messages? This cannot be undone.')) {
      try {
        await deleteChatSession(user.id, idToDelete);
        setChatSessions(prev => prev.filter(s => s.id !== idToDelete));
        toast({ title: "Success", description: "Chat session deleted." });

        // Check if the deleted chat was the active one
        if (pathname === `/dashboard/chat/${idToDelete}`) {
          router.push('/dashboard/chat'); // Navigate to new chat page
        }
      } catch (error: any) {
        console.error("Error deleting chat session:", error);
        toast({ title: "Error", description: error.message || "Failed to delete chat session.", variant: "destructive" });
      }
    }
  };

  const handleStartRename = (session: ChatSession) => {
    setEditingSessionId(session.id);
    setNewTitle(session.title || ''); // Initialize with current title or empty string
  };

  const handleCancelRename = () => {
    setEditingSessionId(null);
    setNewTitle('');
  };

  const handleSaveRename = async () => {
    if (!user?.id || !editingSessionId ) return;

    const trimmedTitle = newTitle.trim();
    // Prevent saving if title is empty after trimming (optional: allow empty title?)
    if (!trimmedTitle) {
        toast({ title: "Info", description: "Chat title cannot be empty.", variant: "default" });
        return;
    }

    const originalSession = chatSessions.find(s => s.id === editingSessionId);
    if (trimmedTitle === (originalSession?.title || '')) {
       handleCancelRename(); // No change, just cancel editing
       return;
    }

    // Consider setting a specific loading state
    // setIsLoadingChatSessions(true);
    try {
      await updateChatSessionTitle(user.id, editingSessionId, trimmedTitle);
      setChatSessions(prev => prev.map(s =>
        s.id === editingSessionId ? { ...s, title: trimmedTitle } : s
      ));
      toast({ title: "Success", description: "Chat title updated." });
      handleCancelRename(); // Exit editing mode
    } catch (error: any) {
      console.error("Error renaming chat session:", error);
      toast({ title: "Error", description: error.message || "Failed to rename chat session.", variant: "destructive" });
    } finally {
      // setIsLoadingChatSessions(false);
    }
  };

  const handleCreateClick = async (e: React.MouseEvent<HTMLAnchorElement>) => {
    // Only handle the click if the user is logged in
    if (user?.id) {
      e.preventDefault(); // Prevent default navigation
      
      setIsCreatingProject(true);
      try {
        // Call the same createNewProject function used in the dashboard page
        const newProjectId = await createNewProject(user.id);
        
        if (newProjectId) {
          toast({
            title: "Project Created",
            description: "Redirecting to your new project...",
          });
          
          // Redirect to the specific project page
          router.push(`/dashboard/project/${newProjectId}`);
        } else {
          throw new Error("Failed to get new project ID.");
        }
      } catch (err: any) {
        console.error("Error creating project from sidebar:", err);
        toast({
          title: "Project creation failed",
          description: err.message || "Could not create a new project. Please try again.",
          variant: "destructive",
        });
        
        // If creation fails, navigate to the regular create page
        router.push("/dashboard/project");
      } finally {
        setIsCreatingProject(false);
      }
    } else {
      // If user is not logged in, use default navigation to the create page
      router.push("/dashboard/project");
    }
  };

  // --- Navigation data -------------------------------------------------------
  const navItems = [
    { name: "Dashboard", href: "/dashboard", icon: Home },
    
    // { 
    //  name: isCreatingProject ? "Creating..." : "Create", 
    //  href: "/dashboard/project", 
    //  icon: isCreatingProject ? Loader2 : Sparkles,
    //  onClick: handleCreateClick,
    //  className: isCreatingProject ? "animate-spin" : ""
    //},
    { name: "Templates", href: "/dashboard/templates", icon: LayoutTemplate },

    // Chat and Projects are handled separately below
    
    //{ name: "Settings", href: "/dashboard/settings", icon: Settings },
  ];

  // ---------------------------------------------------------------------------
  // Sidebar Content JSX
  // ---------------------------------------------------------------------------
  const sidebarContent = (
    <>
      {/* ─── Header ──────────────────────────────────── */}
    {/*<div className="mb-2.5 flex items-center border-b px-0 py-2.5"> */}
      <div className="flex items-center border-b px-0" style={{ height: "56px" }}>

        <Link
          href="/dashboard"
          className={cn(
            "flex shrink-0 items-center gap-2 rounded-full p-1.5",
            "justify-start"
          )}
          onClick={closeMobileMenu}
        >
          <img alt="ProVibe Logo" src="/logo.png" width={30} height={30} className="rounded-full" />
          {isVisuallyExpanded && <span className="text-xl font-bold tracking-tight">Provibe</span>}
        </Link>
      </div>

      {/* ─── Scrollable content area ─────────────────────────────────────── */}
      <div className="flex flex-1 flex-col overflow-y-auto">
        {/* ─── Navigation list ─────────────────────────────────────────────── */} {/* Reduced gap from gap-2 to gap-1 */}
        {/* Apply different gap based on visual expansion state */}
        <ul className={cn("flex flex-col w-full px-2 pt-6", isVisuallyExpanded ? "gap-2" : "gap-4")}>
          {navItems.map(({ name, href, icon: Icon, onClick, className }) => {
            const active = pathname === href || (pathname?.startsWith(href) && href !== "/dashboard");
            return (
              <li
                key={href}
                className={cn(
                  "w-full font-medium transition-colors",
                  active ? "bg-primary/10" : "hover:bg-muted",
                  "rounded-lg"
                )}
              >
                <Link
                  href={href}
                  onClick={(e) => {
                    if (onClick) {
                      onClick(e);
                    } else {
                      closeMobileMenu();
                    }
                  }}
                  aria-current={active ? "page" : undefined}
                  className={cn(
                    "group flex items-center gap-2 rounded-[inherit]",
                    // Apply padding based on expanded/collapsed state
                    isVisuallyExpanded ? "justify-start p-2" : "justify-center p-0",
                    active ? "text-primary" : "text-muted-foreground hover:text-primary",
                    className
                  )}
                >
                  <Icon
                    className={cn(
                      "size-6 transition-colors stroke-[1.5px]",
                      active ? "fill-current stroke-current" : "fill-none group-hover:fill-current",
                      // Removed className from here if it was intended only for size/fill
                      // Keep if className is used for other icon specific styles
                    )}
                  />
                  {isVisuallyExpanded && <span className="truncate text-sm">{name}</span>}
                </Link>
              </li>
            );
          })}
          {/* ...other nav items */}
        {/* The </ul> tag previously here at line 483 was closing the list prematurely. */}
        {/* The "Projects" and "Chat" sections below are intended to be part of this same list. */}
          {/* --- Projects Section --- */}
          <li className={cn(
            "w-full font-medium transition-colors",
            pathname?.startsWith("/dashboard/project") ? "bg-primary/10" : "hover:bg-muted",
            "rounded-lg"
          )}>
            {isVisuallyExpanded ? (
              <div className="flex items-center justify-between w-full pr-2"> {/* Added pr-2 for consistent padding */}
                <Link
                  href="/dashboard/project"
                  onClick={closeMobileMenu}
                  className={cn(
                    "group flex flex-grow items-center gap-2 rounded-lg p-2", // flex-grow to take space
                    pathname?.startsWith("/dashboard/project") ? "text-primary" : "text-muted-foreground hover:text-primary"
                  )}
                  aria-current={pathname?.startsWith("/dashboard/project") ? "page" : undefined}
                >
                  <Briefcase className={cn(
                    "size-6 transition-colors stroke-[1.5px]",
                    pathname?.startsWith("/dashboard/project") ? "fill-current stroke-current" : "fill-none group-hover:fill-current"
                  )} />
                  <span className="truncate text-sm">Projects</span>
                </Link>
                <button
                  onClick={toggleProjects} // Keep toggleProjects for the arrow
                  className="p-1 rounded-md hover:bg-muted"
                  aria-label={projectsOpen ? "Collapse projects" : "Expand projects"}
                  aria-expanded={projectsOpen}
                >
                  <ChevronRight className={cn(
                    "h-4 w-4 transition-transform stroke-[1.5px] text-muted-foreground", // Added text-muted-foreground
                    projectsOpen && "rotate-90"
                  )} />
                </button>
              </div>
            ) : (
              <Link
                href="/dashboard/project"
                onClick={closeMobileMenu}
                aria-current={pathname?.startsWith("/dashboard/project") ? "page" : undefined}
                className={cn(
                  "group flex items-center justify-center p-0 rounded-lg",
                  pathname?.startsWith("/dashboard/project") ? "text-primary" : "text-muted-foreground hover:text-primary"
                )}
              >
                <Briefcase className={cn(
                  "size-6 transition-colors stroke-[1.5px]",
                  pathname?.startsWith("/dashboard/project") ? "fill-current stroke-current" : "fill-none group-hover:fill-current"
                )} />
              </Link>
            )}

                {/* Projects submenu */}
                {isVisuallyExpanded && projectsOpen && (
                  <ul className="mt-1 space-y-0.5 pl-8">
                    <li>
                      <Link
                        href="/dashboard/project"
                        onClick={(e) => {
                          handleCreateClick(e);
                          closeMobileMenu();
                        }}
                        className="flex items-center gap-2 rounded-md py-1.5 px-2 text-sm text-primary hover:bg-primary/10"
                      >
                        {isCreatingProject ? (
                          <>
                            <Loader2 className="h-4 w-4 stroke-[1.5px] animate-spin" />
                            <span>Creating...</span>
                          </>
                        ) : (
                          <>
                            <CirclePlus className="h-4 w-4 stroke-[1.5px]" />
                            <span>New Project</span>
                          </>
                        )}
                      </Link>
                    </li>
                 {isLoadingProjects ? (
                  <li className="py-1.5 px-2 text-sm text-muted-foreground">
                    Loading projects...
                  </li>
                ) : projects.length > 0 ? (
                  <>
                    {projects.map((project) => (
                      <li key={project.id} className="mb-0.5">
                        <div className="flex flex-col">
                          <div className="flex items-center justify-between">
                            <Link
                              href={project.href}
                              onClick={closeMobileMenu}
                              className={cn(
                                "flex-1 block rounded-md py-1.5 px-2 text-sm transition-colors",
                                pathname === project.href
                                  ? "bg-primary/10 text-primary"
                                  : "text-muted-foreground hover:bg-muted hover:text-primary"
                              )}
                            >
                              {project.name}
                            </Link>

                            {project.documents.length > 0 && (
                              <button
                                onClick={(e) => toggleProjectExpansion(project.id, e)}
                                className="p-1 rounded-md hover:bg-muted"
                                aria-label={expandedProjects[project.id] ? "Collapse documents" : "Expand documents"}
                              >
                                <ChevronDown
                                  className={cn(
                                    "h-4 w-4 text-muted-foreground transition-transform stroke-[1.5px]",
                                    expandedProjects[project.id] ? "rotate-180" : ""
                                  )}
                                />
                              </button>
                            )}
                          </div>

                          {/* Documents nested dropdown */}
                          {expandedProjects[project.id] && project.documents.length > 0 && (
                            <ul className="pl-4 mt-0.5 space-y-px">
                              {project.documents.map(doc => (
                                <li key={doc.id}>
                                  <Link
                                    href={`/dashboard/project/${project.id}?selectedDocId=${doc.id}`}
                                    onClick={closeMobileMenu}
                                    className="flex items-center gap-1.5 rounded-md py-1 px-2 text-xs text-muted-foreground hover:bg-muted hover:text-primary"
                                  >
                                    <FileCode className="h-3 w-3 stroke-[1.5px]" />
                                    <span className="truncate">{doc.title}</span>
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      </li>
                    ))}
                    <li>
                      <Link
                        href="/dashboard/project"
                        onClick={closeMobileMenu}
                        className="block rounded-md py-1.5 px-2 text-sm text-muted-foreground hover:bg-muted hover:text-primary"
                      >
                        View all projects →
                      </Link>
                    </li>
                  </>
                ) : (
                  <li className="py-1.5 px-2 text-sm text-muted-foreground">
                    No projects found
                  </li>
                )}
              </ul>
            )}
          </li>

          {/* --- Chat Section --- */}
          <li className={cn(
            "w-full font-medium transition-colors",
            pathname?.startsWith("/dashboard/chat") ? "bg-primary/10" : "hover:bg-muted",
            "rounded-lg"
          )}>
            {isVisuallyExpanded ? (
              <button onClick={toggleChat} className={cn(
                "group flex w-full items-center justify-between gap-2 rounded-lg p-2",
                pathname?.startsWith("/dashboard/chat") ? "text-primary" : "text-muted-foreground hover:text-primary"
              )} aria-expanded={isChatOpen}>
                <div className="flex items-center gap-2">
                  <MessageSquare className={cn(
                    "size-6 transition-colors stroke-[1.5px]",
                    pathname?.startsWith("/dashboard/chat") ? "fill-current stroke-current" : "fill-none group-hover:fill-current"
                  )} />
                  <span className="truncate text-sm">Chat</span>
                </div>
                <ChevronRight className={cn(
                  "h-4 w-4 transition-transform stroke-[1.5px]",
                  isChatOpen && "rotate-90"
                )} />
              </button>
            ) : (
              <Link
                href="/dashboard/chat"
                onClick={closeMobileMenu}
                aria-current={pathname?.startsWith("/dashboard/chat") ? "page" : undefined}
                className={cn(
                  "group flex items-center justify-center p-0 rounded-lg",
                  pathname?.startsWith("/dashboard/chat") ? "text-primary" : "text-muted-foreground hover:text-primary"
                )}
              >
                <MessageSquare className={cn(
                  "size-6 transition-colors stroke-[1.5px]",
                  pathname?.startsWith("/dashboard/chat") ? "fill-current stroke-current" : "fill-none group-hover:fill-current"
                )} />
              </Link>
            )}

            {/* Chat sessions submenu */}
            {isVisuallyExpanded && isChatOpen && ( /* Reduced space-y-1 to space-y-0.5 */
              <ul className="mt-1 space-y-0.5 pl-8">
                {/* New Chat button */}
                <li>
                  <Link
                    href="/dashboard/chat"
                    onClick={closeMobileMenu}
                    className="flex items-center gap-2 rounded-md py-1.5 px-2 text-sm text-primary hover:bg-primary/10"
                  >
                    <PlusCircle className="h-4 w-4 stroke-[1.5px]" />
                    <span>New Chat</span>
                  </Link>
                </li>
                
                {/* Session List */}
                {isLoadingChatSessions ? (
                  <li className="py-1.5 px-2 text-sm text-muted-foreground italic">
                    <Loader2 className="inline h-4 w-4 animate-spin mr-2" /> Loading chats...
                  </li>
                ) : chatSessions.length > 0 ? (
                  <>
                    {chatSessions.map((session) => (
                      <li key={session.id} className="group relative pr-1"> {/* Add group relative and padding */}
                        {editingSessionId === session.id ? (
                          // --- Editing View ---
                          <div className="flex items-center gap-1 py-1 pl-1">
                            <Input
                              type="text"
                              value={newTitle}
                              onChange={(e) => setNewTitle(e.target.value)}
                              onKeyDown={(e) => { if (e.key === 'Enter') handleSaveRename(); if (e.key === 'Escape') handleCancelRename(); }}
                              className="h-7 flex-grow bg-transparent rounded-md px-2 py-0.5 text-sm focus:outline-none focus:ring-1 focus:ring-ring"
                              autoFocus
                              onBlur={handleCancelRename} // Optional: Cancel on blur
                            />
                            <Button variant="ghost" size="icon" className="h-6 w-6 text-green-600 hover:bg-green-100 hover:text-green-700" onClick={handleSaveRename}>
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" className="h-6 w-6 text-red-600 hover:bg-red-100 hover:text-red-700" onClick={handleCancelRename}>
                              <CancelIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          // --- Default View ---
                          <div className="flex items-center justify-between">
                            <Link
                              href={`/dashboard/chat/${session.id}`}
                              onClick={closeMobileMenu}
                              className={cn(
                                "flex-1 block rounded-md py-1.5 pl-2 pr-10 text-sm transition-colors truncate", // Added more padding right for buttons
                                pathname === `/dashboard/chat/${session.id}`
                                  ? "bg-primary/10 text-primary font-medium" // Make active link bold
                                  : "text-muted-foreground hover:bg-muted hover:text-primary"
                              )}
                              title={session.title || `Chat ${session.id.substring(0, 6)}...`} // Add title attribute for full view on hover
                            >
                              {session.title || `Chat ${session.id.substring(0, 6)}...`}
                            </Link>
                            {/* Action buttons - visible on hover/focus within the group */}
                            <div className="absolute right-1 top-1/2 -translate-y-1/2 flex items-center opacity-0 group-hover:opacity-100 focus-within:opacity-100 group-focus-within:opacity-100 transition-opacity duration-150 bg-sidebar-background/80 backdrop-blur-sm rounded-md">
                               <Button variant="ghost" size="icon" className="h-6 w-6 text-muted-foreground hover:text-primary" onClick={() => handleStartRename(session)} title="Rename chat">
                                  <Edit3 className="h-3.5 w-3.5" />
                               </Button>
                               <Button variant="ghost" size="icon" className="h-6 w-6 text-muted-foreground hover:text-destructive" onClick={(e) => { e.preventDefault(); e.stopPropagation(); handleDeleteChat(session.id); }} title="Delete chat">
                                  <Trash2 className="h-3.5 w-3.5" />
                               </Button>
                            </div>
                          </div>
                        )}
                      </li>
                    ))}
                  </>
                ) : (
                  <li className="py-1.5 px-2 text-sm text-muted-foreground italic">
                    No chats yet
                  </li>
                )}
              </ul>
            )}
          </li>

        </ul> {/* This </ul> now correctly closes the main list that started at line 451. */}
      </div>

      {/* ─── Fixed bottom section ───────────────────────────────────────── */}
      <div className="mt-auto">
        {/* Collapse Toggle */}
        <div className="flex justify-center py-2">
          <Button 
            variant="outline" 
            size="icon" 
            className="size-8 rounded-md hover:bg-muted" 
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"} 
            onClick={toggleCollapse}
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
        {/* Plan Card */}
        {!isCollapsed && (
          <div className="mt-1.5 w-full px-2">
            <div className="flex flex-col gap-2 rounded-2xl border p-3 transition-shadow hover:shadow-sm">
              <div className="flex items-center justify-between text-sm font-medium">
                <p className="font-semibold">Current Plan</p>
                <span className="text-xs font-bold uppercase text-primary">{userTier}</span>
              </div>
              <Button
                variant="outline"
                className="group w-full gap-1.5 rounded-full border-primary bg-primary/5 text-primary hover:border-primary/20 hover:text-primary text-xs h-8"
                aria-label="Upgrade plan"
                onClick={closeMobileMenu}
              >
                {userTier === "free" ? "Upgrade" : "Manage Subscription"}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 16 16"
                  className="ml-auto size-3.5 transition-transform group-hover:translate-x-0.5"
                  fill="currentColor"
                >
                  <path d="m10.04 3.522 3.771 3.771a1 1 0 0 1 0 1.414l-3.77 3.772a1 1 0 0 1-1.415-1.415L10.69 9H3a1 1 0 0 1 0-2h7.69L8.627 4.936a1 1 0 0 1 1.414-1.414z" />
                </svg>
              </Button>
            </div>
          </div>
        )}
        {/* Account Dropdown */}
        <div className={cn("mt-1 px-2", isCollapsed && "flex justify-center")}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div
                className={cn(
                  "group flex cursor-pointer items-center rounded-full p-2 hover:bg-muted",
                  isCollapsed ? "justify-center" : "justify-start"
                )}
                aria-label="Account menu"
              >
                <Avatar className={cn("h-7 w-7 shrink-0", isCollapsed ? "mr-0" : "mr-2")}>
                  <AvatarImage src={user?.avatar || undefined} alt={user?.name || "User Avatar"} />
                  <AvatarFallback className="bg-foreground text-xs font-medium text-background">
                    {user?.name?.charAt(0)?.toUpperCase() ?? "U"}
                  </AvatarFallback>
                </Avatar>
                {!isCollapsed && (
                  <>
                    <span className="flex-1 truncate text-sm font-medium">
                      {user?.name ?? "User Name"}
                    </span>
                    <ChevronDown className="ml-1.5 h-4 w-4 shrink-0 text-muted-foreground transition-transform group-hover:rotate-180" />
                  </>
                )}
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem asChild>
                <Link href="/dashboard/profile" onClick={closeMobileMenu}>
                  <User className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/settings" onClick={closeMobileMenu}>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={logout}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Logout</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </>
  );

  // ---------------------------------------------------------------------------
  // Return JSX (Mobile/Desktop wrappers)
  // ---------------------------------------------------------------------------
  return (
    <div>
      {/* Mobile toggle */}
      <Button 
        variant="ghost" 
        size="icon" 
        className="fixed left-4 top-4 z-50 rounded-full md:hidden" 
        onClick={toggleMobileMenu} 
        aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"} 
        aria-expanded={isMobileMenuOpen}
      >
        {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>
      
      {/* Mobile sidebar panel */}
      <div 
        role="dialog" 
        aria-modal="true" 
        className={cn(
          "fixed inset-y-0 left-0 z-40 flex h-full w-64 transform flex-col border-r bg-sidebar-background shadow-lg transition-transform duration-300 ease-in-out md:hidden overflow-hidden", 
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        {sidebarContent}
      </div>
      
      {/* Overlay for mobile */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 z-30 bg-black/30 md:hidden" 
          onClick={closeMobileMenu} 
          aria-hidden="true" 
        />
      )}
      
      {/* Desktop sidebar */}
      <aside 
        ref={sidebarRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{ backgroundColor: 'hsl(var(--sidebar-background))' }}
        className={cn(
          "fixed top-0 left-0 z-10 hidden h-[calc(100vh)] flex-col  border p-1 transition-all duration-300 md:flex overflow-hidden", 
          isVisuallyExpanded ? "w-60" : "w-14",
          className
        )}
      >
        {sidebarContent}
      </aside>
    </div>
  );
}
