import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import { useEffect } from 'react'

interface RichTextViewerProps {
  content: string
  className?: string
}

export default function RichTextViewer({ content, className = '' }: RichTextViewerProps) {
  // Convert markdown to HTML with improved spacing handling
  const markdownToHtml = (markdown: string) => {
    if (!markdown) return ''
    
    // Normalize line breaks first
    let html = markdown
      // Normalize line breaks
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      
      // Headers (ensure proper spacing before and after)
      .replace(/\n### (.*?)(?:\n|$)/g, '\n<h3>$1</h3>\n')
      .replace(/\n## (.*?)(?:\n|$)/g, '\n<h2>$1</h2>\n')
      .replace(/\n# (.*?)(?:\n|$)/g, '\n<h1>$1</h1>\n')
      
      // Bold
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      
      // Italic
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      
      // Code
      .replace(/`(.*?)`/g, '<code>$1</code>')
      
      // Lists (handle multi-line lists properly)
      .replace(/\n- (.*?)(?:\n|$)/g, '\n<ul><li>$1</li></ul>\n')
      .replace(/\n\d+\. (.*?)(?:\n|$)/g, '\n<ol><li>$1</li></ol>\n')
      
      // Paragraphs (wrap text blocks in paragraphs)
      .replace(/\n\n((?:(?!\n\n|<h[1-6]|<ul|<ol|<li|<\/ul>|<\/ol>).)+)(?:\n\n|$)/gs, '\n<p>$1</p>\n')
      
      // Clean up excessive line breaks
      .replace(/\n{3,}/g, '\n\n')
      
      // Convert remaining line breaks to <br> tags
      .replace(/\n(?!<\/?(p|h[1-6]|ul|ol|li)>)/g, '<br>')
    
    // Trim extra whitespace
    return html.trim()
  }

  const editor = useEditor({
    extensions: [
      StarterKit,
    ],
    content: '',
    editable: false,
    immediatelyRender: false, // Add this line to fix the SSR warning
  })

  useEffect(() => {
    if (editor && content) {
      editor.commands.setContent(markdownToHtml(content))
    }
  }, [editor, content])

  return (
    <div className={`rich-text-content ${className}`}>
      <EditorContent editor={editor} />
    </div>
  )
}
