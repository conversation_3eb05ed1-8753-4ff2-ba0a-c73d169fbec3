import React from "react";
import { cn } from "@/lib/utils";

interface DashboardShellProps {
  children: React.ReactNode;
  className?: string;
  isSidebarCollapsed?: boolean; // Added prop to receive sidebar state

}

export function DashboardShell({ 
  children, 
  className,
  isSidebarCollapsed, // Destructure the new prop 
}: DashboardShellProps) {
  // Sidebar is fixed top-1 (0.25rem) left-1 (0.25rem).
  // CORRECTION: Sidebar is fixed top-0 left-0, with p-1 (0.25rem padding).
  // Expanded width w-60 (15rem), Collapsed width w-14 (3.5rem).
  // Margin left for shell should be equal to sidebar width for no gap.
  // Expanded: 15rem
  // Collapsed: 3.5rem
  return (
    <div className={cn(
      "flex flex-col flex-1 gap-2",
      "transition-all duration-300", // For smooth transition of margin
      "pt-0 pb-0", // Account for sidebar's top-1 offset
      isSidebarCollapsed ? "md:ml-[3.5rem]" : "md:ml-[15rem]", // Adjusted left margin
      className // Allow overriding or adding other classes
    )}>
      {children}
    </div>
  );
}
