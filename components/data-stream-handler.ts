export type DataStreamDelta = {
  type: string;
  content?: unknown;
  [key: string]: unknown;
};

export async function processDataStream(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  onDelta: (delta: DataStreamDelta) => void,
) {
  const decoder = new TextDecoder();
  let buffer = '';
  while (true) {
    const { value, done } = await reader.read();
    if (done) break;
    buffer += decoder.decode(value, { stream: true });
    const events = buffer.split('\n\n');
    buffer = events.pop() || '';
    for (const event of events) {
      const lines = event.split('\n');
      for (const line of lines) {
        if (line.startsWith('data:')) {
          const dataStr = line.slice(5).trim();
          if (!dataStr || dataStr === '[DONE]') continue;
          try {
            const json = JSON.parse(dataStr);
            onDelta(json);
          } catch (e) {
            console.error('Failed to parse delta', e);
          }
        }
      }
    }
  }
}

