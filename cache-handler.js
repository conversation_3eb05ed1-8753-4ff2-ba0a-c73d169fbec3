// Simple in-memory cache implementation
const cache = new Map();

class CacheHandler {
  constructor() {
    // Initialize any configuration here if needed
  }

  async get(key) {
    return cache.get(key);
  }

  async set(key, data, options = {}) {
    cache.set(key, {
      value: data,
      lastModified: Date.now(),
      tags: options.tags || [],
    });
  }

  async revalidateTag(tag) {
    const tags = Array.isArray(tag) ? tag : [tag];
    
    for (const [key, value] of cache.entries()) {
      if (value.tags && value.tags.some(t => tags.includes(t))) {
        cache.delete(key);
      }
    }
  }
}

// Use CommonJS module.exports instead of ES module export
module.exports = CacheHandler;
