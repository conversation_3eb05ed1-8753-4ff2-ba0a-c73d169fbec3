-- Add count columns to profiles table
-- This migration adds project_count and doc_count fields to track user statistics
-- and sets up automatic triggers to maintain these counts

-- Step 1: Add the count columns to profiles table (if they don't exist)
DO $$ 
BEGIN 
  -- Add project_count column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='project_count') THEN
    ALTER TABLE profiles ADD COLUMN project_count integer DEFAULT 0 NOT NULL;
  END IF;
  
  -- Add doc_count column if it doesn't exist  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='doc_count') THEN
    ALTER TABLE profiles ADD COLUMN doc_count integer DEFAULT 0 NOT NULL;
  END IF;
END $$;

-- Step 2: Create function to update project count
CREATE OR REPLACE FUNCTION update_project_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Increment project count when a new project is created
    UPDATE profiles 
    SET project_count = project_count + 1 
    WHERE id = NEW.user_id;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Decrement project count when a project is deleted
    UPDATE profiles 
    SET project_count = project_count - 1 
    WHERE id = OLD.user_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create function to update document count
CREATE OR REPLACE FUNCTION update_document_count()
RETURNS TRIGGER AS $$
DECLARE
  project_user_id uuid;
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Get the user_id from the projects table for this document
    SELECT user_id INTO project_user_id 
    FROM projects 
    WHERE id = NEW.project_id;
    
    -- Increment document count
    IF project_user_id IS NOT NULL THEN
      UPDATE profiles 
      SET doc_count = doc_count + 1 
      WHERE id = project_user_id;
    END IF;
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- Get the user_id from the projects table for this document
    SELECT user_id INTO project_user_id 
    FROM projects 
    WHERE id = OLD.project_id;
    
    -- Decrement document count
    IF project_user_id IS NOT NULL THEN
      UPDATE profiles 
      SET doc_count = doc_count - 1 
      WHERE id = project_user_id;
    END IF;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Create triggers for projects table
DROP TRIGGER IF EXISTS trigger_update_project_count ON projects;
CREATE TRIGGER trigger_update_project_count
  AFTER INSERT OR DELETE ON projects
  FOR EACH ROW
  EXECUTE FUNCTION update_project_count();

-- Step 5: Create triggers for project_documents table  
DROP TRIGGER IF EXISTS trigger_update_document_count ON project_documents;
CREATE TRIGGER trigger_update_document_count
  AFTER INSERT OR DELETE ON project_documents
  FOR EACH ROW
  EXECUTE FUNCTION update_document_count();

-- Step 6: Initialize counts for existing users
-- This will calculate the current counts for all existing users
UPDATE profiles SET 
  project_count = (
    SELECT COUNT(*) 
    FROM projects 
    WHERE projects.user_id = profiles.id
  ),
  doc_count = (
    SELECT COUNT(*) 
    FROM project_documents pd
    JOIN projects p ON pd.project_id = p.id
    WHERE p.user_id = profiles.id
  );

-- Step 7: Add indexes for better performance on count queries
CREATE INDEX IF NOT EXISTS idx_projects_user_id ON projects(user_id);
CREATE INDEX IF NOT EXISTS idx_project_documents_project_id ON project_documents(project_id);

-- Step 8: Add comments for documentation
COMMENT ON COLUMN profiles.project_count IS 'Auto-maintained count of projects owned by this user';
COMMENT ON COLUMN profiles.doc_count IS 'Auto-maintained count of documents across all user projects';
COMMENT ON FUNCTION update_project_count() IS 'Trigger function to automatically update project_count in profiles table';
COMMENT ON FUNCTION update_document_count() IS 'Trigger function to automatically update doc_count in profiles table';