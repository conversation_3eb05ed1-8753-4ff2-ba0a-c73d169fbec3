# Project and Document Count System

This system automatically tracks the number of projects and documents for each user in the `profiles` table using database triggers.

## Overview

The count system adds two new fields to the `profiles` table:
- `project_count`: Number of projects owned by the user
- `doc_count`: Number of documents across all user's projects

These counts are automatically maintained by database triggers, ensuring they stay accurate without requiring application-level counting.

## Features

- ✅ **Automatic counting**: Triggers handle all increment/decrement operations
- ✅ **Real-time updates**: Counts update immediately when projects/documents are created/deleted
- ✅ **Performance optimized**: Uses indexes for efficient queries
- ✅ **Existing data support**: Initializes counts for existing users
- ✅ **Safe deployment**: Uses conditional column creation (idempotent)

## Database Changes

### New Columns
```sql
ALTER TABLE profiles ADD COLUMN project_count integer DEFAULT 0 NOT NULL;
ALTER TABLE profiles ADD COLUMN doc_count integer DEFAULT 0 NOT NULL;
```

### Trigger Functions
- `update_project_count()`: Maintains project_count when projects are added/removed
- `update_document_count()`: Maintains doc_count when documents are added/removed

### Triggers
- `trigger_update_project_count`: Fires on INSERT/DELETE in `projects` table
- `trigger_update_document_count`: Fires on INSERT/DELETE in `project_documents` table

## Deployment

### Option 1: Manual SQL Execution
Run the migration file directly in Supabase SQL editor:
```bash
# Copy contents of supabase/migrations/add_count_columns_and_triggers.sql
# Paste and execute in Supabase > SQL Editor
```

### Option 2: Node.js Script
Use the provided deployment script:
```bash
# Set environment variables
export NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"

# Run deployment script
node scripts/deploy-count-triggers.js
```

## Usage in Application

### TypeScript Types
The count fields are included in the `User` interface:
```typescript
export interface User {
  // ... other fields
  project_count: number
  doc_count: number
}
```

### Reading Counts
```typescript
// Get user with counts
const { data: profile } = await supabase
  .from('profiles')
  .select('*, project_count, doc_count')
  .eq('id', userId)
  .single();

console.log(`User has ${profile.project_count} projects and ${profile.doc_count} documents`);
```

### Counts Update Automatically
```typescript
// When you create a project, project_count automatically increments
const { data: newProject } = await supabase
  .from('projects')
  .insert({ user_id: userId, name: 'New Project' });
// project_count is now incremented by trigger

// When you create a document, doc_count automatically increments  
const { data: newDoc } = await supabase
  .from('project_documents')
  .insert({ project_id: projectId, title: 'New Document' });
// doc_count is now incremented by trigger
```

## Monitoring

### Verify Trigger Function Status
```sql
-- Check if functions exist
SELECT proname FROM pg_proc WHERE proname IN ('update_project_count', 'update_document_count');

-- Check if triggers exist
SELECT tgname FROM pg_trigger WHERE tgname IN ('trigger_update_project_count', 'trigger_update_document_count');
```

### Manual Count Verification
```sql
-- Verify project counts are accurate
SELECT 
  p.id,
  p.project_count,
  (SELECT COUNT(*) FROM projects WHERE user_id = p.id) AS actual_project_count
FROM profiles p
WHERE p.project_count != (SELECT COUNT(*) FROM projects WHERE user_id = p.id);

-- Verify document counts are accurate  
SELECT 
  p.id,
  p.doc_count,
  (SELECT COUNT(*) FROM project_documents pd JOIN projects pr ON pd.project_id = pr.id WHERE pr.user_id = p.id) AS actual_doc_count
FROM profiles p
WHERE p.doc_count != (SELECT COUNT(*) FROM project_documents pd JOIN projects pr ON pd.project_id = pr.id WHERE pr.user_id = p.id);
```

## Troubleshooting

### If Counts Become Inaccurate
Run the recalculation query:
```sql
UPDATE profiles SET 
  project_count = (
    SELECT COUNT(*) 
    FROM projects 
    WHERE projects.user_id = profiles.id
  ),
  doc_count = (
    SELECT COUNT(*) 
    FROM project_documents pd
    JOIN projects p ON pd.project_id = p.id
    WHERE p.user_id = profiles.id
  );
```

### If Triggers Stop Working
Re-create the triggers:
```sql
-- Drop and recreate triggers
DROP TRIGGER IF EXISTS trigger_update_project_count ON projects;
DROP TRIGGER IF EXISTS trigger_update_document_count ON project_documents;

-- Recreate (run the functions and triggers sections from migration file)
```

## Performance Impact

- **Minimal**: Triggers add negligible overhead to INSERT/DELETE operations
- **Indexes**: Added for optimal query performance
- **Scalable**: Works efficiently even with large datasets

## Security

- Triggers run with database permissions, not user permissions
- Counts cannot be manually manipulated by application users
- Only actual project/document creation/deletion affects counts