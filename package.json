{"name": "my-v0-project", "version": "0.1.0", "private": true, "type": "commonjs", "engines": {"node": ">=18.17.0"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.10", "@ai-sdk/google": "^1.2.14", "@ai-sdk/openai": "^1.3.20", "@ai-sdk/react": "^1.2.12", "@ai-sdk/xai": "^1.2.16", "@anthropic-ai/sdk": "^0.39.0", "@clerk/nextjs": "^6.20.1", "@codemirror/lang-python": "^6.2.1", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.37.1", "@emotion/is-prop-valid": "^1.3.1", "@excalidraw/excalidraw": "^0.18.0", "@google/generative-ai": "^0.2.1", "@hookform/resolvers": "^5.0.1", "@lexical/clipboard": "^0.30.0", "@lexical/code": "^0.30.0", "@lexical/file": "^0.30.0", "@lexical/hashtag": "^0.30.0", "@lexical/headless": "^0.31.2", "@lexical/html": "^0.30.0", "@lexical/link": "^0.30.0", "@lexical/list": "^0.30.0", "@lexical/markdown": "^0.30.0", "@lexical/overflow": "^0.30.0", "@lexical/react": "^0.30.0", "@lexical/rich-text": "^0.30.0", "@lexical/selection": "^0.30.0", "@lexical/table": "^0.30.0", "@lexical/text": "^0.30.0", "@lexical/utils": "^0.30.0", "@lexical/yjs": "^0.30.0", "@liveblocks-examples/nextjs-notion-like-ai-editor": "github:Provibe-dev/notion-like-ai-editor", "@liveblocks/react": "^2.24.2", "@mistralai/mistralai": "^1.6.0", "@notionhq/client": "^2.3.0", "@octokit/rest": "^22.0.0", "@openrouter/ai-sdk-provider": "^0.4.6", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.77.0", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-floating-menu": "^2.12.0", "@tiptap/extension-paragraph": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/suggestion": "^2.12.0", "@vercel/analytics": "^1.5.0", "@vercel/blob": "^1.1.1", "@vercel/functions": "^2.1.0", "@vercel/speed-insights": "^1.2.0", "ai": "^4.3.16", "bcrypt-ts": "^7.0.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "codemirror": "^6.0.1", "date-fns": "^2.30.0", "drizzle-orm": "^0.44.2", "embla-carousel-react": "8.5.1", "fast-deep-equal": "^3.1.3", "framer-motion": "^11.18.2", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "gpt-tokenizer": "^2.9.0", "groq": "^3.87.1", "groq-sdk": "^0.21.0", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "katex": "^0.16.22", "langchain": "^0.3.27", "lexical": "^0.30.0", "lodash-es": "^4.17.21", "lucide-react": "^0.454.0", "marked": "^15.0.11", "mermaid": "^11.6.0", "next": "15.2.4", "next-themes": "^0.4.6", "openai": "^4.104.0", "papaparse": "^5.5.3", "postgres": "^3.4.7", "prosemirror": "^0.11.1", "prosemirror-example-setup": "^1.2.3", "react": "^19.1.0", "react-colorful": "^5.6.1", "react-data-grid": "^7.0.0-beta.55", "react-date-picker": "^11.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "react-window": "^1.8.11", "recharts": "2.15.0", "redis": "^5.5.5", "remark-gfm": "^4.0.1", "remeda": "^2.21.3", "resumable-stream": "^2.2.0", "shiki": "^1.29.2", "simple-git": "^3.27.0", "sonner": "^1.7.4", "stream": "^0.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.25.46"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/marked": "^5.0.2", "@types/node": "^22.14.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "turbo": "^2.5.4", "typescript": "^5", "webpack": "^5.72.1"}}