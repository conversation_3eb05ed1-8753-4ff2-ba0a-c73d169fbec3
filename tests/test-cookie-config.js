#!/usr/bin/env node

/**
 * Test script to verify cookie configuration for different domains
 * Run with: node scripts/test-cookie-config.js
 */

// Mock environment variables for testing
const testConfigs = [
  {
    name: 'Local Development (Explicit)',
    env: {
      NODE_ENV: 'development',
      NEXT_PUBLIC_APP_DOMAIN: 'localhost:3000'
    }
  },
  {
    name: 'Main App Production (Explicit)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_DOMAIN: 'www.provibe.dev'
    }
  },
  {
    name: 'App Domain Production (Explicit)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_DOMAIN: 'app.provibe.io'
    }
  },
  {
    name: 'Vercel Auto-Detection (Public URL)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_VERCEL_URL: 'staging.provibe.io'
    }
  },
  {
    name: 'Vercel Auto-Detection (Internal URL)',
    env: {
      NODE_ENV: 'production',
      VERCEL_URL: 'preview-abc123.vercel.app'
    }
  },
  {
    name: 'Custom Domain (Fallback)',
    env: {
      NODE_ENV: 'production',
      NEXT_PUBLIC_APP_DOMAIN: 'my-custom-domain.com'
    }
  },
  {
    name: 'No Configuration (Runtime Detection)',
    env: {
      NODE_ENV: 'production'
      // No domain configuration - would use runtime detection
    }
  }
];

// Mock the getCookieConfig function with new domain detection logic
function getCookieConfig(env) {
  const isProduction = env.NODE_ENV === 'production';

  // Try multiple ways to determine the current domain (same as actual implementation)
  let currentDomain = '';

  if (env.NEXT_PUBLIC_APP_DOMAIN) {
    currentDomain = env.NEXT_PUBLIC_APP_DOMAIN;
  } else if (env.NEXT_PUBLIC_VERCEL_URL) {
    currentDomain = env.NEXT_PUBLIC_VERCEL_URL;
  } else if (env.VERCEL_URL) {
    currentDomain = env.VERCEL_URL;
  }

  // Default configuration
  let cookieName = "sb-provibe-default-auth-token";
  let domain = "";
  let detectionMethod = "none";

  // Configure based on detected domain
  if (currentDomain) {
    if (env.NEXT_PUBLIC_APP_DOMAIN) detectionMethod = "explicit";
    else if (env.NEXT_PUBLIC_VERCEL_URL) detectionMethod = "vercel_public";
    else if (env.VERCEL_URL) detectionMethod = "vercel_internal";

    if (currentDomain.includes('app.provibe.io')) {
      cookieName = "sb-provibe-app-auth-token";
      domain = isProduction ? "app.provibe.io" : "";
    } else if (currentDomain.includes('www.provibe.dev') || currentDomain.includes('provibe.dev')) {
      cookieName = "sb-provibe-main-auth-token";
      domain = isProduction ? "www.provibe.dev" : "";
    } else if (currentDomain.includes('localhost') || currentDomain.includes('127.0.0.1')) {
      cookieName = "sb-provibe-dev-auth-token";
      domain = "";
    } else {
      // Unknown domain - use domain-specific cookie name
      const domainSafe = currentDomain.replace(/[^a-zA-Z0-9]/g, '-');
      cookieName = `sb-provibe-${domainSafe}-auth-token`;
      domain = isProduction ? currentDomain : "";
    }
  } else {
    detectionMethod = "runtime_fallback";
  }

  return {
    name: cookieName,
    lifetime: 60 * 60 * 8, // 8 hours
    domain: domain,
    path: "/",
    sameSite: "lax",
    secure: isProduction,
    detectionMethod,
    detectedDomain: currentDomain || "(none)"
  };
}

console.log('🍪 Cookie Configuration Test\n');
console.log('Testing cookie configurations for different deployment environments:\n');

testConfigs.forEach((config, index) => {
  console.log(`${index + 1}. ${config.name}`);
  console.log(`   Environment: ${JSON.stringify(config.env, null, 2)}`);

  const cookieConfig = getCookieConfig(config.env);

  console.log(`   Cookie Config:`);
  console.log(`     Detection Method: ${cookieConfig.detectionMethod}`);
  console.log(`     Detected Domain: ${cookieConfig.detectedDomain}`);
  console.log(`     Cookie Name: ${cookieConfig.name}`);
  console.log(`     Cookie Domain: ${cookieConfig.domain || '(current domain)'}`);
  console.log(`     Path: ${cookieConfig.path}`);
  console.log(`     Secure: ${cookieConfig.secure}`);
  console.log(`     SameSite: ${cookieConfig.sameSite}`);
  console.log(`     Lifetime: ${cookieConfig.lifetime}s (${cookieConfig.lifetime / 3600}h)`);
  console.log('');
});

console.log('✅ All configurations generated successfully!');
console.log('\n📝 Key Benefits for Same Repository Deployment:');
console.log('- ✅ Same codebase works on multiple domains');
console.log('- ✅ Automatic domain detection (no code changes needed)');
console.log('- ✅ Unique cookie names prevent conflicts');
console.log('- ✅ Multiple detection methods for flexibility');
console.log('- ✅ Production-ready security settings');
console.log('- ✅ Fallback support for unknown domains');
console.log('\n🚀 Deploy the same /app directory to unlimited domains!');
